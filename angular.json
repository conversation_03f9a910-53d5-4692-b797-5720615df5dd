{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mandiner": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "inlineStyle": false, "inlineTemplate": false, "prefix": "app", "skipTests": true, "changeDetection": "OnPush"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"allowedCommonJsDependencies": ["date-fns-tz"], "outputPath": {"base": "dist"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["src/scss", "."]}, "assets": ["src/new-favicon.ico", "src/assets", {"glob": "**/*", "input": "src/assets/images/favicons", "output": "/"}, {"glob": "manifest.json", "input": "src/assets/json", "output": "/"}, {"glob": "robots.txt", "input": "src", "output": "/"}, {"glob": "serviceworker.js", "input": "src/assets/vendors/strossle", "output": "/"}, {"glob": "ads.txt", "input": "src", "output": "/"}, {"glob": "**/*", "input": "node_modules/@trendency/kesma-ui/assets", "output": "/assets"}], "styles": ["src/scss/styles.scss"], "scripts": ["src/assets/vendors/embedly/platform.js", "src/assets/vendors/instagram/instagram.js", "src/assets/vendors/newsletter-script.js"], "preserveSymlinks": true, "browser": "src/main.ts", "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "test": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "local-ssr": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "ssr": false}}, "defaultConfiguration": "local"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "mandiner:build:production"}, "test": {"buildTarget": "mandiner:build:test"}, "development": {"buildTarget": "mandiner:build:development"}, "local": {"buildTarget": "mandiner:build:local"}}, "defaultConfiguration": "local"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "d350187e-d3c4-43d0-9b8b-d4f4eba16a19", "schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}