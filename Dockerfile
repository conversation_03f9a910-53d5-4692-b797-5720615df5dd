FROM node:18-alpine AS build

WORKDIR /app

ARG NEXUS_TOKEN_PROD
RUN npm set //prod-nexus.trendency.hu/repository/npm-hosted/:_authToken $NEXUS_TOKEN_PROD
ARG NEXUS_TOKEN_DEV
RUN npm set //dev-nexus.trendency.hu/repository/npm-hosted/:_authToken $NEXUS_TOKEN_DEV

COPY package.json package-lock.json ./

RUN npm ci

COPY . .

ARG ENV=dev
RUN npm run build:ssr-$ENV


FROM node:18-alpine

WORKDIR /app

COPY --from=build /app/dist ./dist

EXPOSE 4000/tcp
CMD node dist/server/server.mjs > /dev/stdout

RUN apk add curl
HEALTHCHECK CMD curl -f localhost:4000
