{"apps": [{"name": "mandiner-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "mandiner-test", "script": "dist/server/server.mjs", "exec_mode": "cluster", "instances": "2", "cwd": "/content/apps/mandinerfe/app", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30063}, "out_file": "/content/logs/mandinerfe/out.log", "err_file": "/content/logs/mandinerfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "mandiner-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "8", "cwd": "/content/apps/mandinerfe/app", "max_restarts": 10, "env": {"PORT": 30064}, "out_file": "/content/logs/mandinerfe/out.log", "err_file": "/content/logs/mandinerfe/err.log", "log_type": "json", "time": true, "merge_logs": true}]}