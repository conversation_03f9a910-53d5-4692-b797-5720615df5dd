@use 'shared' as *;
html,
body {
  height: 100%;
}
body {
  margin: 0;
}

.row {
  --bs-gutter-x: 25px !important;
}

section {
  /*.wrapper.with-aside > aside {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
  }*/

  .wrapper.with-aside {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 40px;

    @include media-breakpoint-down(md) {
      margin-top: 15px;
    }

    > .left-column {
      display: block;
      width: calc(100% - #{$aside-width} - 40px);
      @include media-breakpoint-down(lg) {
        width: 100%;
      }
    }

    > aside {
      display: block;
      width: $aside-width;
      margin-bottom: 30px;
      @include media-breakpoint-down(md) {
        width: 100%;
      }

      > * {
        margin-bottom: $block-bottom-margin;
      }

      .aside-box {
        .heading-line {
          margin-bottom: 18px;
        }

        man-article-card {
          margin-bottom: 10px;
        }
      }
    }
  }
}
