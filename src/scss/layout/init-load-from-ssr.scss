@use 'shared' as *;
@mixin anim($left, $frame) {
  left: $left + 0px;
  animation: circle#{$frame} 0.5s infinite;
}
@keyframes circle1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes circle2 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
@keyframes circle3 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(calc(0.343 * 70px), 0);
  }
}
@include media-breakpoint-down(lg) {
  body.ssr {
    .ssr-loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -35px;
      margin-top: -7px;
      display: inline-block;
      width: 70px;
      height: 15px;

      div {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 35%;
        background: #d74929;

        &:nth-child(1) {
          @include anim(calc(0.114 * 70), 1);
        }

        &:nth-child(2) {
          @include anim(calc(0.114 * 70), 3);
        }

        &:nth-child(3) {
          @include anim(calc(0.457 * 70), 3);
        }

        &:nth-child(4) {
          @include anim(calc(0.8 * 70), 2);
        }
      }
    }
  }
}
