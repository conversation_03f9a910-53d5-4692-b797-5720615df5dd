@use 'variables' as *;
@use 'breakpoints' as *;

// Vertical center
@mixin center-vertically {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Display block icon
@mixin icon($name) {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url($images-path + $name);
}

// Background image
@mixin img($file) {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url($images-path + $file);
}

// IE10 IE11 only
@mixin ieonly() {
  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    @content;
  }
}

// Firefox only
@mixin ffonly() {
  @-moz-document url-prefix() {
    @content;
  }
}

// DOM element with fixed ratio - 100% width
@mixin imgRatio($x, $y) {
  display: block;
  width: 100%;
  padding-top: ($y/$x) * 100%;
  background-size: cover;
  background-position: center;
  background-color: grey;
}

@mixin stickyLayoutElement() {
  background: var(--man-sticky-bg);
  position: sticky;
  top: 70px;
  margin-bottom: 20px;
}

@mixin layoutMakeExplicitColumnsSticky() {
  /**
      Make sticky elements when a column has an inner row which is the last element in the column!
     */
  .row-element .column-element {
    height: 100%;
    //We need to eliminate rows that are after another row
    .row-element:last-child:not(.row-element ~ .row-element) {
      @include stickyLayoutElement();
    }
  }
}

@mixin listItemBase() {
  position: relative;
  font-size: 18px;
  line-height: 24px;
  ol,
  ul {
    margin: 0 0 0 5px;
  }
}

@mixin unordered-list() {
  li {
    @include listItemBase();
    list-style: none;
    font-weight: 700;
    padding-left: 20px;

    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: calc(0.6em - 5px);
      left: 0;
      background-size: contain;
      background-repeat: no-repeat;
      height: 8px;
      width: 8px;
      background-image: url('/assets/images/icons/new-ellipse.svg');
    }
  }
}

@mixin ordered-list() {
  padding-left: 0;

  li {
    @include listItemBase();
    font-weight: 600;
    text-transform: uppercase;
    list-style: none;
    counter-increment: all;
    padding-left: 27px;

    &::before {
      content: counter(list-item) '.';
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-style: italic;
      color: var(--kui-orange-600);
    }
  }
}
