@use 'shared' as *;

.mandiner-form {
  &-row {
    margin-bottom: 10px;
  }

  &-label {
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;

    strong {
      font-weight: 700;
      color: var(--kui-orange-600);
    }
  }

  &-input {
    border: 1px solid var(--kui-gray-400);
    display: block;
    width: 100%;
    height: 40px;
    padding: 10px;
    margin-bottom: 5px;

    &-password {
      position: relative;

      &-img {
        position: absolute;
        right: 10px;
        top: 13px;
        cursor: pointer;
      }
    }
  }

  &-small {
    display: block;
    font-size: 10px;
    line-height: 16px;
  }

  &-checkbox-item {
    display: flex;
  }

  &-checkboxes {
    .checkbox + .checkbox {
      margin-top: 10px;
    }
  }

  &-checkbox {
    margin-left: 0;
    display: flex;
    align-self: flex-start;
    min-width: 16px;

    &-label {
      margin-bottom: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      margin-left: 6px;

      &-link {
        color: var(--kui-orange-600);
        text-decoration: underline;
      }
    }
  }
}

kesma-form-control {
  &.checkbox .form-error {
    top: -14px;
    left: 0;
    right: initial;
  }

  &.only-border .form-error {
    display: none;
  }
}
