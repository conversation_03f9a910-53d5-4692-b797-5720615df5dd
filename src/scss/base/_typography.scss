@use 'shared' as *;

// Typography

body,
button,
input {
  font-family: var(--kui-font-primary);
}

body {
  font-family: var(--kui-font-primary);
  color: var(--kui-black);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--kui-font-condensed);
  font-weight: 700;
  color: var(--kui-black);
}

a {
  &,
  &:visited,
  &:active {
    color: var(--kui-black);
  }
}

.man-article-title {
  color: var(--kui-orange-600);
  font-weight: 700;
  font-size: 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--kui-gray-100);
  margin-bottom: 10px;
  text-align: left;
}
