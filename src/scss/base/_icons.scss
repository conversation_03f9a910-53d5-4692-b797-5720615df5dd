@use 'shared' as *;

.icon {
  &.close {
    @include icon('icons/man-close.svg');
  }

  &.fullscreen {
    @include icon('icons/fullscreen.svg');
  }

  &.arrow-left {
    @include icon('icons/arrow-left.svg');
  }

  &.arrow-right {
    @include icon('icons/arrow-right.svg');
  }

  &.edit {
    @include icon('icons/icon-edit.svg');
  }

  &.icon-comment-edit {
    @include icon('icons/comment-edit.svg');
  }

  &.icon-logo {
    @include icon('new-mandiner-logo.svg');
  }

  &.icon-logo-inverse {
    @include icon('logo-inverse.svg');
  }

  &.icon-logo-gallery-box {
    @include icon('logo-gallery-box.svg');
  }

  &.icon-chevron-right {
    @include icon('icons/icon-chevron-right.svg');
  }

  &.icon-news-feed {
    @include icon('hirfolyam.svg');
  }

  &.icon-check {
    @include icon('icons/icon-check.svg');
  }

  &.icon-mandiner-question-mark {
    @include icon('icons/icon-mandiner-question-mark.svg');
  }

  &.icon-mandiner-breaking-strip-arrow {
    @include icon('icons/icon-mandiner-breaking-strip-arrow.svg');
  }

  &.icon-mandiner-youtube {
    @include icon('icons/icon-mandiner-youtube.svg');
  }

  &.icon-mandiner-facebook {
    @include icon('icons/icon-mandiner-facebook.svg');
  }

  &.icon-mandiner-instagram {
    @include icon('icons/icon-mandiner-instagram.svg');
  }

  &.icon-mandiner-linkedin {
    @include icon('icons/icon-mandiner-linkedin.svg');
  }

  &.icon-mandiner-twitter {
    @include icon('icons/icon-mandiner-twitter.svg');
  }

  &.icon-mandiner-folder-open {
    @include icon('icons/icon-mandiner-folder-open.svg');
  }

  &.icon-mandiner-folder-open-white {
    @include icon('icons/icon-mandiner-folder-open-white.svg');
  }

  &.icon-mandiner-euro {
    @include icon('icons/icon-mandiner-euro.svg');
  }

  &.icon-mandiner-euro-white {
    @include icon('icons/icon-mandiner-euro-white.svg');
  }

  &.icon-mandiner-dollar {
    @include icon('icons/icon-mandiner-dollar.svg');
  }

  &.icon-mandiner-dollar-white {
    @include icon('icons/icon-mandiner-dollar-white.svg');
  }

  &.icon-mandiner-exchange-rate-up {
    @include icon('icons/icon-mandiner-exchange-rate-rise.svg');
  }

  &.icon-mandiner-exchange-rate-down {
    @include icon('icons/icon-mandiner-exchange-rate-decrease.svg');
  }

  &.icon-mandiner-search {
    @include icon('icons/icon-mandiner-search.svg');
  }

  &.icon-mandiner-chevron-up {
    @include icon('icons/icon-mandiner-chevron-up.svg');
  }

  &.icon-mandiner-arrow-up-white {
    @include icon('icons/icon-arrow-up-white.svg');
  }

  &.icon-mandiner-chevron-left {
    @include icon('icons/icon-mandiner-chevron-left.svg');
  }

  &.icon-mandiner-chevron-right {
    @include icon('icons/icon-mandiner-chevron-right.svg');
  }

  &.icon-mandiner-chevron-right-gold {
    @include icon('icons/icon-mandiner-chevron-right-gold.svg');
  }

  &.icon-mandiner-hamburger {
    @include icon('icons/icon-mandiner-hamburger.svg');
  }

  &.icon-mandiner-user {
    @include icon('icons/icon-mandiner-user.svg');
  }

  &.icon-mandiner-hamburger-user {
    @include icon('icons/icon-mandiner-hamburger-user.svg');
  }

  &.icon-mandiner-close {
    @include icon('icons/icon-mandiner-close.svg');
  }

  &.icon-mandiner-hamburger-close {
    @include icon('icons/icon-mandiner-hamburger-close.svg');
  }

  &.icon-mandiner-hamburger-search {
    @include icon('icons/icon-mandiner-hamburger-search.svg');
  }

  &.icon-mandiner-video-play {
    @include icon('icons/new-icon-mandiner-video-play.svg');
  }

  &.icon-mandiner-gallery {
    @include icon('icons/new-icon-mandiner-gallery.svg');
  }

  &.icon-mandiner-arrow-right-gold {
    @include icon('icons/icon-mandiner-arrow-right-gold.svg');
  }

  &.icon-mandiner-plus {
    @include icon('icons/icon-mandiner-plus.svg');
  }

  &.icon-mandiner-breaking {
    @include icon('icons/icon-mandiner-breaking.svg');
  }

  &.icon-man-close {
    @include icon('icons/close.svg');
  }

  &.icon-man-answer {
    @include icon('icons/new-icon-answer.svg');
  }

  &.icon-man-upvote {
    @include icon('icons/new-icon-like.svg');
  }

  &.icon-man-downvote {
    @include icon('icons/new-icon-dislike.svg');
  }

  &.icon-man-arrow-right {
    @include icon('icons/icon-arrow-right.svg');
  }

  &.icon-man-person {
    @include icon('icons/icon-person.svg');
  }

  &.icon-m {
    @include icon('icons/icon-m.svg');
  }

  &.icon-date {
    @include icon('icons/icon-date.svg');
    width: 12px;
    height: 12px;
  }

  &.icon-gallery {
    @include icon('icons/icon-gallery.svg');
    width: 48px;
    height: 37px;
  }

  &.icon-mandiner-podcast {
    @include icon('icons/icon-podcast.svg');
    width: 10.5px;
    height: 12px;
  }

  &.icon-mandiner-minute-to-minute {
    @include icon('icons/icon-minute-to-minute.svg');
    width: 12px;
    height: 12px;
  }

  &.icon-facebook {
    @include icon('icons/icon-facebook.svg');
  }

  &.icon-google {
    @include icon('icons/icon-google.svg');
  }

  &.icon-apple {
    @include icon('icons/icon-apple.svg');
  }

  &.icon-man-filter {
    @include icon('icons/filter.svg');
  }

  &.icon-man-filter-black {
    @include icon('icons/filter-black.svg');
  }

  &.icon-user-edit {
    @include icon('icons/user-edit.svg');
  }

  &.icon-mandiner-play {
    @include icon('icons/video.svg');
    width: 48px;
    height: 56px;
  }
}

// Weather icons
.weather-icon {
  &.sunny {
    @include icon('icons/weather/sunny.svg');
  }

  &.cloud-hail {
    @include icon('icons/weather/cloud-hail.svg');
  }

  &.cloud-hurricane {
    @include icon('icons/weather/cloud-hurricane.svg');
  }

  &.cloud-rain {
    @include icon('icons/weather/cloud-rain.svg');
  }

  &.cloud-snow {
    @include icon('icons/weather/cloud-snow.svg');
  }

  &.cloud-thunder-heavy {
    @include icon('icons/weather/cloud-thunder-heavy.svg');
  }

  &.cloud-wind {
    @include icon('icons/weather/cloud-wind.svg');
  }

  &.clouds {
    @include icon('icons/weather/clouds.svg');
  }

  &.day {
    @include icon('icons/weather/day.svg');
  }

  &.night {
    @include icon('icons/weather/night.svg');
  }

  &.rain {
    @include icon('icons/weather/rain.svg');
  }

  &.snowflake {
    @include icon('icons/weather/snowflake.svg');
  }

  &.wind-sun {
    @include icon('icons/weather/wind-sun.svg');
  }
}
