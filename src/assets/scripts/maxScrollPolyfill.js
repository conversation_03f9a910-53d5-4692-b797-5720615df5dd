// A navigáció almenüjéhez szükséges.

(function(elmProto) {
    if ('scrollTopMax' in elmProto) {
      return;
    }
    Object.defineProperties(elmProto, {
      'scrollTopMax': {
        get: function scrollTopMax() {
          return this.scrollHeight - this.clientHeight;
        }
      },
      'scrollLeftMax': {
        get: function scrollLeftMax() {
          return this.scrollWidth - this.clientWidth;
        }
      }
    });
  }
)(Element.prototype);
