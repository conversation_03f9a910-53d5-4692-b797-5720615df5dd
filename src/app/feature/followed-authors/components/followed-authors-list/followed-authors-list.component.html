<man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON> szerz<PERSON>im' }]"></man-breadcrumb>

<section class="caption">
  <h1><PERSON><PERSON><PERSON><PERSON> szerz<PERSON>im</h1>
</section>

<section class="content">
  <ng-container *ngFor="let author of authors$ | async; let i = index">
    <section class="ad-mid-content" *ngIf="i > 0 && i % 6 === 0">
      <img *ngIf="ads.length > currentPage" src="https://via.placeholder.com/300x250?text=Hirdet%C3%A9s%20300x250%20{{ ads[currentPage] }}" alt="Hírdetés" />
    </section>
    <man-author-info [data]="author"></man-author-info>
  </ng-container>
</section>

<section class="show-more">
  <man-simple-button (click)="loadMore()" *ngIf="currentPage < maxPage">
    <span>Továbbiak betöltése</span>
  </man-simple-button>
</section>
