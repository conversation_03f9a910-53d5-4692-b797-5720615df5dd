import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, ArticleAuthor, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { AuthorService } from '../../../author-page/api/author.service';
import {
  BackendAuthorData,
  createMandinerTitle,
  defaultMetaInfo,
  ManAuthorInfoComponent,
  ManBreadcrumbComponent,
  ManSimpleButtonComponent,
} from '../../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-followed-authors-list',
  templateUrl: './followed-authors-list.component.html',
  styleUrls: ['./followed-authors-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, NgForOf, AsyncPipe, NgIf, ManAuthorInfoComponent, ManSimpleButtonComponent],
})
export class FollowedAuthorsListComponent implements OnInit {
  currentPage = 0;
  maxPage = 0;

  authors$: Observable<ArticleAuthor[]> = this.route.data.pipe(
    map((res) => res['data'] as ApiResult<BackendAuthorData[], ApiResponseMetaList>),
    tap((res) => (this.maxPage = res.meta.limitable.pageMax || 0)),
    map((res) => res.data.map((author) => this.service.mapAuthorDataToArticleAuthor(author)))
  );
  ads: any[] = []; // TODO: Reklám típus
  constructor(
    private readonly route: ActivatedRoute,
    private readonly service: AuthorService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.ads = [this.service.getAd()];
    this.setMetaData();
  }

  loadMore(): void {
    this.currentPage++;
    this.authors$ = combineLatest([this.authors$, this.service.getAuthorsForPage$(this.currentPage)]).pipe(
      map(
        ([authors, newAuthors]) => [...authors, ...newAuthors] // Flatten the array.
      )
    );

    this.ads = [...this.ads, this.service.getAd()];
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('kovetett-szerzoim');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Követett szerzőim');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
