import { Routes } from '@angular/router';
import { FollowedAuthorsListComponent } from './components/followed-authors-list/followed-authors-list.component';
import { FollowedAuthorsListResolver } from './api/followed-authors-list.resolver';

export const followedAuthorsRoutes: Routes = [
  {
    path: '',
    component: FollowedAuthorsListComponent,
    pathMatch: 'full',
    resolve: {
      data: FollowedAuthorsListResolver,
    },
    providers: [FollowedAuthorsListResolver],
  },
];
