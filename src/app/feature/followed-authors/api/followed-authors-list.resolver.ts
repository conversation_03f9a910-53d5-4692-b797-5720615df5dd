import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { FollowedAuthorsService } from './followed-authors.service';
import { AuthorData } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class FollowedAuthorsListResolver {
  constructor(private readonly service: FollowedAuthorsService) {}

  resolve(): Observable<ApiResult<AuthorData[], ApiResponseMetaList>> {
    return this.service.getAuthorsForPageWithMetaData$(0);
  }
}
