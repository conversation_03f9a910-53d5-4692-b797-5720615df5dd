import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ApiService, BackendAuthorData } from '../../../shared';
import { ApiResponseMetaList, ApiResult, ArticleAuthor } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class FollowedAuthorsService {
  constructor(private readonly api: ApiService) {}

  // TODO: change Authors Endpoint if BE has the new one and Authentication is working

  /**
   * Returns the author list for the given page with metadata
   * @param page page number, starting with 0
   * @param perPage number of authors per page, default 10
   */
  getAuthorsForPageWithMetaData$(page: number, perPage = 12): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    return this.api.getAuthors(page, perPage);
  }

  /**
   * Returns the author list for the given page without metadata
   * @param page page number, starting with 0
   * @param perPage number of authors per page, default 10
   */
  getAuthorsForPage$(page: number, perPage = 10): Observable<ArticleAuthor[]> {
    return this.getAuthorsForPageWithMetaData$(page, perPage).pipe(map((res) => res.data.map((author) => this.mapAuthorDataToArticleAuthor(author))));
  }

  /**
   * Maps the backend response type (AuthorData) to the frontend model (ArticleAuthor) if possible
   * @param data backend response
   */
  mapAuthorDataToArticleAuthor(data: BackendAuthorData): ArticleAuthor {
    return {
      name: data.public_author_name,
      description: data.public_author_description,
      facebook: data.facebook,
      instagram: data.instagram,
      tiktok: data.tiktok,
      avatar: data.avatar?.fullSizeUrl,
    };
  }

  getAd(): any {
    // TODO: Reklám típus
    return {}; // TODO: Reklámok lekérése
  }
}
