import { Routes } from '@angular/router';
import { SlugRouteHandlerComponent } from './slug-route-handler.component';
import { ArticleResolverService } from '../article-page/article-page.resolver';
import { StaticPageResolver } from '../static-page/api/static-page.resolver';

export const staticPageRoutes: Routes = [
  {
    path: '',
    component: SlugRouteHandlerComponent,
    resolve: {
      staticPageData: StaticPageResolver,
      articlePageData: ArticleResolverService,
    },
  },
];
