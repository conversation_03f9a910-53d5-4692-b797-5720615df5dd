import { ArticleSearchResult, BackendArticleSearchResult, toBool } from '@trendency/kesma-ui';
import { THUMBNAIL_PLACEHOLDER } from '../../shared/utils/image.utils';

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
    contentType: article.contentType?.toString() || '',
    hideThumbnailFromBody: toBool(article.hideThumbnailFromBody),
  };
}

export const searchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  year: publishYear,
  month: publishMonth,
  contentType,
  preTitle,
  tags,
  regions,
  likeCount,
  dislikeCount,
  commentCount,
  isLikesAndDislikesDisabled,
  isCommentsDisabled,
  isPaywalled,
  isVideo,
  isAdultsOnly,
  hasGallery,
  hasDossiers,
  hideThumbnailFromBody,
  foundationTagSlug,
  foundationTagTitle,
  thumbnailFocusedImages,
}: ArticleSearchResult): any => ({
  id,
  title,
  preTitle,
  slug,
  category: {
    name: columnTitle,
    slug: columnSlug,
  },
  publishDate,
  publishYear,
  publishMonth,
  lead,
  thumbnail: {
    url: toBool(hideThumbnailFromBody) ? THUMBNAIL_PLACEHOLDER : thumbnailUrl,
  },
  author: {
    name: authorName,
  },
  tags,
  regions,
  contentType,
  columnSlug,
  columnTitle,
  tag,
  likeCount,
  dislikeCount,
  commentCount,
  isLikesAndDislikesDisabled,
  isCommentsDisabled,
  isPaywalled: isPaywalled ? !!+isPaywalled : false,
  isVideoType: isVideo ? !!+isVideo : false,
  hasGallery: hasGallery ? !!+hasGallery : false,
  hasDossiers,
  isAdultsOnly,
  foundationTagSlug,
  foundationTagTitle,
  thumbnailFocusedImages,
});
