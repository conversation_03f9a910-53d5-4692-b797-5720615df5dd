import { TagsPageService } from './tags-page.service';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Injectable } from '@angular/core';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { TagsPageResolverResponse } from './tags-page.definitions';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';

@Injectable()
export class TagsPageResolver {
  constructor(
    private readonly tagsPageService: TagsPageService,
    private readonly router: Router,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<TagsPageResolverResponse> {
    const tagSlug = route.params['tag'];
    return forkJoin({
      tags: this.tagsPageService.getTag(tagSlug).pipe(
        map((res) => res.data),
        catchError((err) => {
          this.router
            .navigate(['/', '404'], {
              skipLocationChange: true,
            })
            .then(null);
          return throwError(err);
        })
      ),
      searchFilterData: this.searchFilterDataService.getSearchFilterColoumns(),
    });
  }
}
