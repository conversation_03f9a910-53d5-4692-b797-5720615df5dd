import { Injectable } from '@angular/core';
import { buildPhpArrayParam, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult, Tag } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class TagsPageService {
  constructor(private readonly reqService: ReqService) {}

  searchArticleByTags(
    tags: string[],
    page = 0,
    itemsPerPage = 20,
    orderByAsc?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[]
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    let params: any = {
      ...buildPhpArrayParam(tags, 'tags'),
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'publishDate_order[0]': orderByAsc === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    return this.reqService
      .get(`/content-page/search`, {
        params,
      })
      .pipe(
        map(({ data, meta }: any /*ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>*/) => ({
          data: data,
          meta,
        }))
      );
  }

  public getTag(slug: string): Observable<ApiResult<Tag>> {
    return this.reqService.get(`content-group/tags/${slug}`, {});
  }
}
