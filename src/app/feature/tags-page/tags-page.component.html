<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <man-breadcrumb *ngIf="tag" [items]="[{ label: tag.title || '' }]"></man-breadcrumb>

      <h1 class="tag-title">{{ tag?.title }}</h1>
      <div class="tag-results-meta">
        <div class="tag-results-counter">
          <strong>{{ rowAllCount }} db</strong> cikk öss<PERSON>en
        </div>

        <man-search-filter
          [data]="(searchFilterColumns$ | async) || undefined"
          [showSearchBar]="false"
          [showSearchHeader]="false"
          (filterEvent)="onFilter($event)"
        ></man-search-filter>
      </div>

      <div class="article-list">
        <ng-container *ngIf="articles.length > 0">
          <ng-container *ngFor="let article of articles; let i = index">
            <man-article-card
              class="article-card"
              [data]="article"
              [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
              [isTagVisible]="true"
              [isMplus]="article.isPaywalled"
            >
            </man-article-card>

            <div *ngIf="i === 3" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
            </div>

            <div *ngIf="i === 7" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
            </div>

            <div *ngIf="i === 11" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
            </div>

            <div *ngIf="i === 15" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
            </div>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="isLoading">
          <div class="no-results-text">
            <p>Betöltés...</p>
          </div>
        </ng-container>
      </div>

      <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()"> Mutass többet </man-simple-button>
    </div>
    <aside>
      <app-sidebar [excludedIds]="sidebarExcludedIds"></app-sidebar>
    </aside>
  </div>
</section>
