import { Routes } from '@angular/router';
import { TagsPageComponent } from './tags-page.component';
import { TagsPageResolver } from './tags-page.resolver';

export const tagsPageRoutes: Routes = [
  {
    path: ':tag/:tagname',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { tag: TagsPageResolver },
    providers: [TagsPageResolver],
  },
  {
    path: ':tag',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { tag: TagsPageResolver },
    providers: [TagsPageResolver],
  },
  {
    path: '',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { tag: TagsPageResolver },
    providers: [TagsPageResolver],
  },
];
