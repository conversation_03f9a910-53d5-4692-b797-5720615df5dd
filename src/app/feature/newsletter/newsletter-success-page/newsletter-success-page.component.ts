import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../shared';
import { createMandinerTitle } from '../../../shared';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-newsletter-success-page',
  templateUrl: './newsletter-success-page.component.html',
  styleUrls: ['./newsletter-success-page.component.scss'],
  imports: [NgIf],
})
export class NewsletterSuccessPageComponent implements OnInit, OnDestroy {
  pageTextSuffix = '';
  routeSubscription$ = new Subscription();
  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.routeSubscription$ = this.route.data.subscribe((data) => {
      this.pageTextSuffix = data['pageTextSuffix'];
      const title = createMandinerTitle(`Hírlevél ${this.pageTextSuffix}`);
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
      });
    });
  }

  ngOnDestroy(): void {
    this.routeSubscription$.unsubscribe();
  }
}
