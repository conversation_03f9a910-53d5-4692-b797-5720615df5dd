import { Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { DOCUMENT } from '@angular/common';
import { createMandinerTitle, defaultMetaInfo } from '../../../shared';
import { BypassPipe, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-newsletter-signup-page',
  templateUrl: './newsletter-signup-page.component.html',
  styleUrls: ['./newsletter-signup-page.component.scss'],
  imports: [RouterLink, BypassPipe],
})
export class NewsletterSignupPageComponent implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  recaptcha = 'https://www.google.com/recaptcha/api.js';
  action = 'https://api.automizy.com/v2/forms/submit/FP3u0eg2JOol4tUr32OytjrAzpctPU5kw1XSS8L5Bzs/kpeBB569p3o7uiUwDgvIcP0uMho';

  ngOnInit(): void {
    this.renderRecaptcha();
    this.setMetaData();
  }

  private renderRecaptcha(): void {
    const s = this.renderer2.createElement('script');
    s.type = 'text/javascript';
    s.src = this.recaptcha;
    s.text = ``;
    this.renderer2.appendChild(this.document.body, s);
  }

  private setMetaData(): void {
    const title = createMandinerTitle('Hírlevél-feliratkozás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hirlevel-feliratkozas');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
