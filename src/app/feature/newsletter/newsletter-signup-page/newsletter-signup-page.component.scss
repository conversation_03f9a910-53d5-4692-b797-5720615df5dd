@use 'shared' as *;

.newsletter-signup-header {
  max-width: 790px;
  margin: 50px auto;

  @include media-breakpoint-down(md) {
    padding: 0 20px;
  }

  &-title {
    font-weight: 700;
    font-size: 24px;
    color: var(--kui-orange-600);
    margin-bottom: 20px;
  }

  &-text {
    font-weight: 400;
    font-size: 16px;
    margin-bottom: 20px;
  }
}

.form-wrapper {
  width: 485px;
  margin: 50px auto 0;

  @include media-breakpoint-down(md) {
    width: 100%;
    padding: 0 20px;
  }

  .automizy-form-form {
    display: flex;
    flex-direction: column;
    font-family: var(--kui-font-primary);
    font-weight: 700;

    .automizy-form-fields {
      display: flex;
      flex-direction: column;
      align-items: center;

      .automizy-form-input-box {
        width: 100%;

        .automizy-form-input-label {
          font-size: 18px;
          margin-bottom: 5px;
        }

        .automizy-form-input {
          width: 100%;
          border: 1px solid var(--kui-gray-200);
          padding: 15px 30px 15px 15px;
          margin-bottom: 20px;
        }

        &.marketing {
          display: flex;
          align-items: flex-start;
          margin-bottom: 80px;

          label {
            margin-left: 15px;
            font-family: var(--kui-font-primary);
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            width: 100%;
          }
        }
      }

      .automizy-form-button-box {
        .automizy-form-button {
          font-family: var(--kui-font-primary);
          font-weight: 700;
          align-self: center;
          margin-top: 30px;
          padding: 15px 30px;
          background-color: var(--kui-black);
          font-size: 20px;
          color: var(--kui-white);
          cursor: pointer;
        }
      }

      .automizy-form-privacy {
        font-family: var(--kui-font-primary);
        font-size: 14px;
        line-height: 20px;
        margin-top: 50px;
        margin-bottom: 20px;
        font-weight: 400;

        .terms {
          cursor: pointer;
          color: var(--kui-orange-600);
          text-decoration: underline;
        }
      }
    }
  }
}
