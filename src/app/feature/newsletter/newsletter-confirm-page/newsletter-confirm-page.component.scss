@use 'shared' as *;

.wrapper {
  max-width: 550px;
  margin: 50px auto 0;

  @include media-breakpoint-down(md) {
    width: 100%;
    padding: 0 20px;
  }

  .content {
    display: flex;
    flex-direction: column;
    font-weight: 700;

    h2 {
      font-family: var(--kui-font-primary);
      font-size: 36px;
      margin-bottom: 30px;
      text-align: center;

      &.confirm {
        margin-top: 20px;
      }

      @include media-breakpoint-down(md) {
        font-size: 30px;
        line-height: 36px;
      }
    }

    .email-sent {
      font-family: var(--kui-font-primary);
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      text-align: center;
      margin-top: 30px;
      margin-bottom: 50px;

      span {
        font-weight: 700;
        color: var(--kui-orange-600);
      }
    }
  }
}
