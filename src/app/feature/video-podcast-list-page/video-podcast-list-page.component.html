<section>
  <div class="wrapper">
    <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems"></man-breadcrumb>
    <h1 class="man-article-title">Videók</h1>

    <man-search-filter
      [data]="(searchFilterColumns$ | async) || undefined"
      [resultCount]="rowAllCount"
      [isShowContentTypes]="false"
      (filterEvent)="onFilter($event)"
      [showSearchHeader]="false"
      [filterOnSide]="true"
      articleTitle="Videók"
    >
      <strong>{{ rowAllCount }} db</strong> videó összesen
    </man-search-filter>

    <section class="results">
      <ng-container *ngIf="results.length > 0">
        <div *ngFor="let result of results; let i = index">
          <man-article-card
            class="article-card"
            [data]="result"
            [styleID]="ArticleCardType.ImgRightUpperMeta"
            [isTagVisible]="true"
            [isMaxWidth]="true"
            [hideDate]="true"
            [isMplus]="result.isPaywalled"
          >
          </man-article-card>

          <div *ngIf="i === 5" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="isLoading">
        <div class="no-results-text">
          <p>Betöltés...</p>
        </div>
      </ng-container>
    </section>

    <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()"> Mutass többet </man-simple-button>
  </div>
</section>
