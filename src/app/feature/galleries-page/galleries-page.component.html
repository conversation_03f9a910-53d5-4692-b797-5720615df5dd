<section class="galleries-page">
  <div class="wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' }]"></man-breadcrumb>
    <h1 class="title">Galériák időrendben</h1>

    <div class="flex-container">
      <ng-container *ngFor="let gallery of galleries">
        <man-gallery-card [routerLink]="['/', 'galeria', gallery.slug]" [styleID]="GalleryCardTypes.IMAGE_WITH_TITLE" [data]="gallery"> </man-gallery-card>
      </ng-container>
    </div>

    <man-simple-button *ngIf="canLoadMore()" (click)="loadMore()">
      <span>Továbbiak betöltése</span>
    </man-simple-button>
  </div>
</section>
