@use 'shared' as *;

:host {
  display: block;

  .galleries-page {
    margin: 40px 0;
  }

  .title {
    color: var(--kui-orange-600);
    margin: 20px 0;
  }

  .flex-container {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;

    man-gallery-card {
      width: 33.333%;
      padding: 15px;

      @include media-breakpoint-down(md) {
        width: 50%;
      }

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
