import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, GalleryData } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';

@Injectable()
export class GalleriesPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    return this.apiService.getGalleries(0, 12).pipe(
      map((res) => {
        if (res.data.length === 0) {
          throw new Error('No more items');
        }
        return res as any;
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
