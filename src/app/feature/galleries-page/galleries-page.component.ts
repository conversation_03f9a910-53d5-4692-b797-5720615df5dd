import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { createCanonicalUrlForPageablePage, GalleryData } from '@trendency/kesma-ui';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map, tap } from 'rxjs/operators';
import {
  ApiService,
  createMandinerTitle,
  defaultMetaInfo,
  GalleryCardTypes,
  ManBreadcrumbComponent,
  ManGalleryCardComponent,
  ManSimpleButtonComponent,
} from '../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-galleries-page',
  templateUrl: './galleries-page.component.html',
  styleUrls: ['./galleries-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, ManGalleryCardComponent, NgForOf, RouterLink, ManSimpleButtonComponent, NgIf],
})
export class GalleriesPageComponent implements OnInit {
  galleries: GalleryData[] = [];

  pageMax = 0;
  page = 0;

  readonly GalleryCardTypes = GalleryCardTypes;

  private readonly galleriesPageSize = 12;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly apiService: ApiService,
    private readonly changeDetector: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.setMetaData();

    this.route.data
      .pipe(
        tap((res) => (this.pageMax = res['data']['meta']['limitable']['pageMax'])),
        map((res) => res['data']['data'])
      )
      .subscribe((galleries) => {
        this.galleries = galleries;
      });
  }

  loadMore(): void {
    if (!this.canLoadMore()) {
      return;
    }

    this.page++;
    this.apiService
      .getGalleries(this.page, this.galleriesPageSize)
      .pipe(map((result) => result?.data))
      .subscribe((galleries) => {
        this.galleries = [...this.galleries, ...galleries] as GalleryData[];
        this.changeDetector.markForCheck();
      });
  }

  canLoadMore(): boolean {
    return this.page < this.pageMax;
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('galeriak');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Galériák');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
