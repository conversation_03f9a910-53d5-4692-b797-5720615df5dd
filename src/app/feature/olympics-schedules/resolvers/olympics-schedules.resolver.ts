import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, map, take } from 'rxjs/operators';
import { forkJoin, Observable, throwError } from 'rxjs';
import { OlympicsSchedulesService } from '../services/olympics-schedules.service';
import { Layout, OlimpiaHungarianCompetitions } from '@trendency/kesma-ui';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';

export const olympicsSchedulesResolver: ResolveFn<
  Observable<{ olympicsSchedules: OlimpiaHungarianCompetitions[]; olympicImportantSidebar: Layout | null }>
> = () => {
  const router = inject(Router);

  return forkJoin({
    olympicsSchedules: inject(OlympicsSchedulesService)
      .getOlympicsSchedules()
      .pipe(map(({ data }) => data)),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  }).pipe(
    catchError((err) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => err);
    })
  );
};
