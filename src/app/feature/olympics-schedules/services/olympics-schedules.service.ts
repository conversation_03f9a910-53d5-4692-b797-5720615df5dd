import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, OlimpiaHungarianCompetitions } from '@trendency/kesma-ui';
import { formatDate } from 'date-fns';

@Injectable({
  providedIn: 'root',
})
export class OlympicsSchedulesService {
  constructor(private readonly reqService: ReqService) {}

  getOlympicsSchedules(): Observable<ApiResult<OlimpiaHungarianCompetitions[], ApiResponseMetaList>> {
    const today = formatDate(new Date(), 'yyyy-MM-dd');

    const params = {
      countryCode_filter: 'hun',
      startDate_filter: today,
    };

    return this.reqService.get('/olympics/events/2024', { params });
  }
}
