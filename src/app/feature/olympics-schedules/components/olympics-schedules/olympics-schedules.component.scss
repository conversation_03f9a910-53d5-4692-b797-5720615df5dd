@use 'shared' as *;

:host {
  display: block;

  .wrapper {
    gap: 24px;
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .title {
      color: var(-kui-black);
      font-size: 28px;
      font-weight: 700;
      line-height: 34px;
      margin: 10px 0 2px;
      font-family: var(--kui-font-opensans);

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 34px;
        margin-top: -8px;
        margin-bottom: -6px;
      }
    }

    man-breadcrumb {
      margin-bottom: -4px;
    }

    kesma-olimpia-hungarian-competitions {
      margin-bottom: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }
    }

    .load-more-btn {
      width: 134px;
      height: 43px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 1px auto 0 auto;
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      line-height: 19px;
      background: #000;

      @include media-breakpoint-down(md) {
        max-width: 330px;
        width: 100%;
      }
    }

    kesma-olimpia-navigator {
      margin: 32px 0;

      @include media-breakpoint-down(md) {
        margin: 18px 0 16px;
      }
    }
  }
}
