import { Async<PERSON>ip<PERSON>, <PERSON><PERSON>orO<PERSON>, NgIf, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import {
  BreadcrumbItem,
  Layout,
  LayoutPageType,
  OlimpiaHungarianCompetitions,
  OlimpiaHungarianCompetitionsComponent,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
} from '@trendency/kesma-ui';
import { formatDate } from 'date-fns';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { ManBreadcrumbComponent } from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';

@Component({
  selector: 'app-sport-olympics-schedules',
  templateUrl: './olympics-schedules.component.html',
  styleUrl: './olympics-schedules.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    OlimpiaPageBannerComponent,
    OlimpiaHungarianCompetitionsComponent,
    NgIf,
    NgForOf,
    SlicePipe,
    OlimpiaNavigatorComponent,
    StrossleAdvertComponent,
    LayoutComponent,
    AsyncPipe,
  ],
})
export class OlympicsSchedulesComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);

  groupedSchedules: OlimpiaHungarianCompetitions[][];

  showAllDates = false;
  breadcrumbItems: BreadcrumbItem[] = [{ label: 'Olimpia 2024', url: '' }];

  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;

  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));

  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.data
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ data }) => (this.groupedSchedules = this.groupSchedulesByStartDate(data['olympicsSchedules'])));
  }

  /**
   * Groups OlimpiaHungarianCompetitions by their start date.
   *
   * @param olympicsSchedules An array of OlimpiaHungarianCompetitions to be grouped.
   * @returns An array of arrays where each inner array contains OlimpiaHungarianCompetitions with the same start date.
   */
  groupSchedulesByStartDate(olympicsSchedules: OlimpiaHungarianCompetitions[]): OlimpiaHungarianCompetitions[][] {
    const schedules = olympicsSchedules.map((schedule) => {
      return {
        ...schedule,
        startDate: new Date(schedule.startDate!),
      };
    });

    return schedules.reduce((acc: OlimpiaHungarianCompetitions[][], obj: OlimpiaHungarianCompetitions) => {
      const key = formatDate(obj.startDate!, 'yyyy-MM-dd');
      const index = acc.findIndex((arr) => formatDate(arr[0].startDate!, 'yyyy-MM-dd') === key);

      if (index === -1) {
        acc.push([obj]);
      } else {
        acc[index].push(obj);
      }

      return acc;
    }, []);
  }
}
