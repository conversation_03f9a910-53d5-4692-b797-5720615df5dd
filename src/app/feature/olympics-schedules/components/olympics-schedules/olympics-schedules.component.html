<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <man-breadcrumb [items]="breadcrumbItems"></man-breadcrumb>

      <kesma-olimpia-page-banner [styleID]="OlimpicPortalEnum.OlimpicMANDINER"></kesma-olimpia-page-banner>

      <h2 class="title">A magyar csapat versenyszámai</h2>

      <ng-container *ngFor="let dailySchedules of groupedSchedules | slice: 0 : 4">
        <kesma-olimpia-hungarian-competitions
          [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
          [isDetailPage]="true"
          [headerDateOverride]="dailySchedules[0].startDate!.toString()"
          [data]="dailySchedules"
        ></kesma-olimpia-hungarian-competitions>
      </ng-container>

      <ng-container *ngIf="groupedSchedules?.length > 4 && !showAllDates">
        <button class="load-more-btn" (click)="showAllDates = true">
          <span>Mutass többet</span>
        </button>
      </ng-container>

      <ng-container *ngIf="showAllDates">
        <ng-container *ngFor="let dailySchedules of groupedSchedules | slice: 4">
          <kesma-olimpia-hungarian-competitions
            [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
            [isDetailPage]="true"
            [headerDateOverride]="dailySchedules[0].startDate!.toString()"
            [data]="dailySchedules"
          ></kesma-olimpia-hungarian-competitions>
        </ng-container>
      </ng-container>

      <kesma-olimpia-navigator [styleID]="OlimpicPortalEnum.OlimpicMANDINER"></kesma-olimpia-navigator>

      <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
      ></app-layout>
    </aside>
  </div>
</section>
