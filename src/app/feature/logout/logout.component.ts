import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../shared';

@Component({
  selector: 'app-logout',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LogoutComponent implements OnInit {
  constructor(
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  ngOnInit(): void {
    this.logout();
  }

  logout(): void {
    this.authService.invalidate().subscribe(() => {
      this.router.navigate(['/', 'bejelentkezes']);
    });
  }
}
