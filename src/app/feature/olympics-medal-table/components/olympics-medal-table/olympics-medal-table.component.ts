import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Layout,
  LayoutPageType,
  OlimpiaMedalTableComponent,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
} from '@trendency/kesma-ui';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { map, Observable, Subject } from 'rxjs';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';

@Component({
  selector: 'app-olympics-medal-table',
  templateUrl: './olympics-medal-table.component.html',
  styleUrl: './olympics-medal-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, OlimpiaPageBannerComponent, OlimpiaMedalTableComponent, OlimpiaNavigatorComponent, LayoutComponent, StrossleAdvertComponent],
})
export class OlympicsMedalTableComponent {
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;
  readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  medalTable$: Observable<MedalTable> = this.route.data.pipe(map(({ data }) => data['medalTable']['data']));

  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));
}
