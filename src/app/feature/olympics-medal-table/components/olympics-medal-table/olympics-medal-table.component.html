<section>
  <div class="wrapper with-aside">
    <div class="left-column" *ngIf="medalTable$ | async as data">
      <kesma-olimpia-page-banner [styleID]="OlimpicPortalEnum.OlimpicMANDINER"></kesma-olimpia-page-banner>
      <h1 class="table-title">Éremtáblázat</h1>
      <kesma-olimpia-medal-table [styleID]="OlimpicPortalEnum.OlimpicMANDINER" [data]="data" [showAllItems]="true"> </kesma-olimpia-medal-table>
      <kesma-olimpia-navigator [navigationLink]="['/', 'olimpia-2024']" [styleID]="OlimpicPortalEnum.OlimpicMANDINER"> </kesma-olimpia-navigator>

      <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
      ></app-layout>
    </aside>
  </div>
</section>
