import { Routes } from '@angular/router';

import { CategoryPageComponent } from './components/category-page/category-page.component';
import { CategoryResolver } from './category.resolver';
import { PagerValidatorGuard } from '../../shared';

export const categoryRoutes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PagerValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-layout' },
    providers: [CategoryResolver],
  },
  {
    path: ':year',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PagerValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-year' },
    providers: [CategoryResolver],
  },
  {
    path: ':year/:month',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PagerValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-month' },
    providers: [CategoryResolver],
  },
];
