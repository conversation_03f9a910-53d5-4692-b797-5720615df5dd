import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';

import { forkJoin, Observable } from 'rxjs';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import { CategoryResolverResponse, CategoryRouteType } from './category.definitions';
import { CategoryService } from './category.service';

@Injectable()
export class CategoryResolver {
  public constructor(
    private readonly categoryService: CategoryService,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<CategoryResolverResponse> {
    const categoryRouteType: CategoryRouteType = route.data['categoryRouteType'];
    const params = route.params;
    const queryParams = route.queryParams;

    switch (categoryRouteType) {
      case 'category-layout':
        return forkJoin({
          category: this.categoryService.getRequestForCategoryLayout(params, queryParams),
          searchFilterData: this.searchFilterDataService.getSearchFilterColoumns(),
        });
      default:
        return forkJoin({
          category: this.categoryService.getRequestForCategoryByDate(params, queryParams),
          searchFilterData: this.searchFilterDataService.getSearchFilterColoumns(),
        });
    }
  }
}
