import { ApiResponseMetaList, ArticleCard, AutoFill, LayoutStruct, SelectedArticle } from '@trendency/kesma-ui';
import { SearchFilterDefinitions } from '../../shared';

export const articlesPageSize = 20;

export type CategoryRouteType = 'category-layout' | 'category-year' | 'category-month';

export type ArticleResponse = Readonly<{
  readonly data: ArticleCard[];
  readonly meta: ApiResponseMetaList;
}>;

export type CategoryServiceResponse = Readonly<{
  readonly layoutApiResponse: Layout | null;
  readonly columnParentTitle?: string;
  readonly columnParentSlug?: string;
  readonly excludedIds: string[];
  readonly columnTitle: string;
  readonly columnLead?: string;
  readonly category: ArticleResponse;
  readonly slug: string;
  readonly year: string;
  readonly month: string;
}>;

export type Layout = Readonly<{
  readonly struct: LayoutStruct[];
  readonly content: LayoutContent[];
}>;

export type LayoutContent = Readonly<{
  readonly autoFill: AutoFill;
  readonly hasImage: boolean;
  readonly layoutElementId: string;
  readonly selectedArticles: SelectedArticle[];
  readonly selectedOpinions: selectedOpinions[];
}>;

type selectedOpinions = Readonly<{
  readonly id: string;
  readonly data: Data;
}>;

type Data = Readonly<{
  readonly id: string;
  readonly slug: string;
  readonly title: string;
  readonly excerpt?: string;
  readonly columnId: string;
  readonly isActive?: string;
  readonly sponsorId?: string[];
  readonly columnSlug: string;
  readonly priorityId?: string;
  readonly columnTitle: string;
  readonly publishDate: string;
  readonly sponsorTitle?: string[];
  readonly thumbnailUrl?: string;
  readonly priorityTitle?: string;
  readonly columnParentId?: any;
  readonly reading_length?: string;
  readonly columnParentSlug?: any;
  readonly publicAuthorName?: string;
  readonly columnParentTitle?: any;
  readonly publicAuthorAvatarThumbnailUrl?: string;
  readonly lead?: string;
}>;

export type CategoryResolverResponse = Readonly<{
  readonly category: CategoryServiceResponse;
  readonly searchFilterData: SearchFilterDefinitions;
}>;
