<ng-container *ngFor="let article of articles; let i = index">
  <man-article-card
    [styleID]="cardType"
    [data]="article"
    [isMaxWidth]="true"
    (socialInteraction)="onSocialInteraction($event)"
    [isMplus]="article.isPaywalled"
  ></man-article-card>

  <div *ngIf="i === 3" class="desktop">
    <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
  </div>

  <div *ngIf="i === 7" class="desktop">
    <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
  </div>

  <div *ngIf="i === 11" class="desktop">
    <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
  </div>

  <div *ngIf="i === 15" class="desktop">
    <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
  </div>
</ng-container>
