import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AnalyticsService, ArticleCard } from '@trendency/kesma-ui';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { ArticleCardType, ManArticleCardComponent, SocialInteractionEvent, SocialInteractionEventType } from '../../../../shared';

@Component({
  selector: 'app-category-article-list[articles]',
  templateUrl: './category-article-list.component.html',
  styleUrls: ['./category-article-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManArticleCardComponent, NgForOf, NgIf, StrossleAdvertComponent],
})
export class CategoryArticleListComponent {
  @Input() articles: ArticleCard[];

  readonly cardType = ArticleCardType.ImgRightTitleLeadDateMeta;

  constructor(private readonly analyticsService: AnalyticsService) {}

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }
}
