@use 'shared' as *;

:host {
  display: block;
  padding: 40px 0;

  .category {
    &-container {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid var(--kui-gray-150);
      padding-bottom: 10px;
      margin-bottom: 5px;
      gap: 10px;
    }

    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-size: 24px;
      line-height: 24px;
      font-weight: 700;
    }

    &-follow-btn {
      border: 1px solid var(--kui-orange-600);
      border-radius: 3px;
      display: flex;
      padding: 6px 12px;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      text-align: center;
      align-items: center;
      justify-content: center;

      &-text {
        margin: auto;
      }

      .done {
        height: 10px;
        width: 14px;
        margin-left: 5px;
      }
    }

    &-lead {
      font-family: var(--kui-font-secondary);
      line-height: 30px;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 24px;
    }

    &-breadcrumb {
      padding-bottom: 8px;
      border-bottom: 1px solid var(--kui-gray-100);
    }

    &-search {
      margin: 30px 0 20px;

      &-title {
        font-weight: 700;
        font-size: 18px;
        margin-bottom: 10px;
      }
    }

    &-flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    man-breadcrumb {
      margin-bottom: 20px;
      border-bottom: 1px solid var(--kui-gray-100);
      padding-bottom: 8px;
    }
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
