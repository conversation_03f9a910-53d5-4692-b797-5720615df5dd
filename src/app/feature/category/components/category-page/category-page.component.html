<section *ngIf="pageData$ | async as pageData">
  <div class="wrapper">
    <div class="category-container">
      <h1 class="category-title">{{ pageData?.columnTitle }}</h1>
      <man-category-follow-button
        (actionEvent)="handleCategoryFollow(pageData.columnSlug)"
        [isFollowed]="isColumnFollowed$ | async"
        [isLoading]="isCategoryFollowButtonLoading"
      ></man-category-follow-button>
    </div>
    <div *ngIf="columnLead" class="category-lead">
      {{ columnLead }}
    </div>

    <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems" class="category-breadcrumb"></man-breadcrumb>

    <ng-container *ngIf="pageData?.layoutData as layoutData">
      <app-layout [configuration]="layoutData.content" [layoutType]="LayoutPageType.COLUMN" [structure]="layoutData.struct"> </app-layout>
    </ng-container>

    <div class="category-search">
      <div class="category-search-title">Rovat hírei</div>
      <man-search-filter
        (filterEvent)="onFilter($event)"
        [data]="(searchFilterColumns$ | async) || undefined"
        [filterOnSide]="true"
        [isShowColumns]="false"
        [showSearchHeader]="false"
      >
        <strong>{{ rowAllCount }} db</strong> cikk összesen
      </man-search-filter>
    </div>

    <ng-container>
      <app-category-article-list [articles]="articles"></app-category-article-list>
    </ng-container>

    <man-simple-button (click)="loadMoreResults()" *ngIf="canLoadMore">
      <strong>Mutass többet</strong>
    </man-simple-button>
  </div>
</section>
