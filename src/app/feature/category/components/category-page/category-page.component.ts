import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  ArticleCard,
  BreadcrumbItem,
  buildColumnUrl,
  createCanonicalUrlForPageablePage,
  FollowStatus,
  LayoutApiData,
  LayoutPageType,
  mapBackendArticleDataToArticleCardWithHideThumbnail,
} from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ApiService,
  ArticleCardType,
  categoriesMetaInfo,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  FollowHelperService,
  makeBreadcrumbSchema,
  ManBreadcrumbComponent,
  ManCategoryFollowButtonComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
} from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { articlesPageSize } from '../../category.definitions';
import { CategoryArticleListComponent } from '../category-article-list/category-article-list.component';

@Component({
  selector: 'app-category-page',
  templateUrl: './category-page.component.html',
  styleUrls: ['./category-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AsyncPipe,
    ManCategoryFollowButtonComponent,
    ManBreadcrumbComponent,
    LayoutComponent,
    ManSearchFilterComponent,
    CategoryArticleListComponent,
    ManSimpleButtonComponent,
  ],
})
export class CategoryPageComponent implements OnInit, OnDestroy {
  LayoutPageType = LayoutPageType;
  ArticleCardType = ArticleCardType;

  recommendedArticles: ArticleCard[] = [];

  filterValues: FilterValues = {};
  rowAllCount? = 0;
  page = 0;
  breadcrumbItems?: BreadcrumbItem[];
  columnLead?: string;
  excludedIds?: string[] = [];
  articles: ArticleCard[] = [];
  isCategoryFollowButtonLoading = false;
  pageData$: Observable<{
    columnTitle: string;
    columnSlug: string;
    layoutData: LayoutApiData;
  }> = this.route.data.pipe(
    map((res) => {
      const columnSlug: string = res['pageData']['category']['slug'];
      const columnParentSlug: string = res['pageData']['category']['columnParentSlug'];

      this.analyticsService.sendPageView({
        pageCategory: columnSlug,
      });
      this.setMetaData(res['pageData']['category']['columnTitle'], res['pageData']['category']['slug']);

      this.breadcrumbItems = [
        {
          label: res['pageData']['category']['columnTitle'],
          url: buildColumnUrl({ columnSlug } as any),
        },
      ];
      if (columnParentSlug) {
        this.breadcrumbItems.unshift({
          label: res['pageData']['category']['columnParentTitle'],
          url: buildColumnUrl({ columnSlug: columnParentSlug } as any),
        });
      }
      const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
      this.schemaService.insertSchema(breadcrumbSchema);
      this.columnLead = res?.['pageData']?.['category']?.['columnLead'];
      this.excludedIds = res?.['pageData']?.['category']?.['excludedIds'];

      return {
        columnTitle: res['pageData']['category']['columnTitle'],
        layoutData: res['pageData']['category']['layoutApiResponse'],
        columnSlug,
      };
    })
  );
  isColumnFollowed$: Observable<FollowStatus> = this.pageData$.pipe(
    switchMap((pageData) => this.followHelperService.getColumnFollowedStatus(pageData.columnSlug))
  );
  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((res) => res['pageData']['searchFilterData']));
  private readonly destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly analyticsService: AnalyticsService,
    private readonly cd: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly searchFilterDataService: SearchFilterDataService,
    private readonly followHelperService: FollowHelperService
  ) {}

  get canLoadMore(): boolean {
    if (this.rowAllCount) {
      return this.rowAllCount !== 0 && articlesPageSize * (this.page + 1) < this.rowAllCount;
    } else {
      return false;
    }
  }

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
      // Need for reinitialise articles when switching between category's.
      this.articles = [];
      this.rowAllCount = 0;
      this.getArticles();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  loadMoreResults(): void {
    if (this.canLoadMore) {
      this.page += 1;
      this.getArticles();
    }
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.page = 0;
    this.articles = [];
    this.getArticles();
  }

  getArticles(): void {
    const slug = this.route.snapshot.paramMap.get('categorySlug') as string;
    const searchQuery = this.searchFilterDataService.setSearchQuery(this.filterValues);
    this.apiService
      .searchByKeyword(
        this.filterValues?.keyword ?? '',
        this.filterValues?.sort,
        searchQuery.fromDate,
        searchQuery.toDate,
        this.filterValues?.contentTypes ?? [],
        [slug],
        this.page,
        20
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe((result) => {
        this.rowAllCount = result.meta['limitable'].rowAllCount;
        this.articles = this.articles.concat(
          result.data
            .map((data) => mapBackendArticleDataToArticleCardWithHideThumbnail(data))
            .map((data) => ({
              ...data,
              thumbnail: {
                url: data.thumbnail?.url || '/assets/images/placeholder-1-1.svg',
                alt: data.thumbnail?.alt,
              },
            }))
        );
        this.cd.detectChanges();
      });
  }

  handleCategoryFollow(columnSlug: string): void {
    this.isCategoryFollowButtonLoading = true;
    this.followHelperService.handleColumnFollow(columnSlug).subscribe(() => {
      this.isCategoryFollowButtonLoading = false;
      this.cd.detectChanges();
    });
  }

  private setMetaData(title: string, slug: string): void {
    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);

    // If category has a predefined metadata to be used.
    if (slug in categoriesMetaInfo) {
      const overrideMeta = categoriesMetaInfo[slug];
      const title = createMandinerTitle(overrideMeta.title);
      const meta: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
        description: overrideMeta.description,
        ogDescription: overrideMeta.description,
        keywords: overrideMeta.keywords,
      };
      this.seo.setMetaData(meta);
    } else {
      // If not, use the default ones.
      const browserTitle = createMandinerTitle(title);
      const metaData: IMetaData = {
        ...defaultMetaInfo,
        title: browserTitle,
        ogTitle: browserTitle,
        // eslint-disable-next-line max-len
        description: `${title} rovat - Friss hírek, aktuális események itthonról és a világból egyaránt. Belföld, Külföld, Sport vagy Kultúra, a legfontosabb híreket megtalálja a Mandiner.hu oldalán.`,
      };
      this.seo.setMetaData(metaData);
    }
  }
}
