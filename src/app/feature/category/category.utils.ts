import { ApiResponseMetaList, ApiResult, ArticleCard, BackendArticleSearchResult, LayoutWithExcludeIds } from '@trendency/kesma-ui';

import { ArticleResponse, CategoryServiceResponse, Layout } from './category.definitions';

export const mapCategoryResponse = (
  categoryResponse: ApiResult<(BackendArticleSearchResult | ArticleCard)[], ApiResponseMetaList>,
  slug: string,
  year = '',
  month = '',
  layoutResponse: LayoutWithExcludeIds = { data: null, excludedIds: [], columnTitle: '', columnParentSlug: '', columnLead: '' },
  noLayoutData = false
): CategoryServiceResponse => ({
  layoutApiResponse: (noLayoutData ? null : layoutResponse?.data) as Layout,
  columnTitle: layoutResponse.columnTitle,
  columnLead: layoutResponse.columnLead,
  columnParentTitle: String(categoryResponse?.meta?.['columnParentTitle']),
  columnParentSlug: layoutResponse.columnParentSlug,
  excludedIds: layoutResponse?.excludedIds,
  category: categoryResponse as unknown as ArticleResponse,
  slug,
  year,
  month,
});
