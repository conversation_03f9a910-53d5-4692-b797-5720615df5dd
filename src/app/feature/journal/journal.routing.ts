import { Routes } from '@angular/router';
import { JournalsListComponent } from './pages/journals-list/journals-list.component';
import { JournalListResolver } from './api/journal-list.resolver';
import { JournalDetailPageComponent } from './pages/journal-detail-page/journal-detail-page.component';
import { JournalDetailsResolver } from './api/journal-details.resolver';
import { JournalPdfComponent } from './pages/journal-pdf/journal-pdf.component';

export const journalRouting: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: JournalsListComponent,
    resolve: {
      data: JournalListResolver,
    },
    providers: [JournalListResolver],
  },
  {
    path: ':slug',
    component: JournalDetailPageComponent,
    resolve: {
      data: JournalDetailsResolver,
    },
    providers: [JournalDetailsResolver],
  },
  {
    path: ':slug/lapozhato-pdf',
    component: JournalPdfComponent,
    resolve: {
      data: JournalDetailsResolver,
    },
    providers: [JournalDetailsResolver],
  },
];
