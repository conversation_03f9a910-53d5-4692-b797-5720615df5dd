import { Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';
import { AuthService, JournalData, JournalDataWithArticles, ManSimpleButtonComponent, TranslatedDatePipe } from '../../../../shared';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { RouterLink } from '@angular/router';
import { AsyncPipe, NgForOf, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-journal-recommender',
  templateUrl: './journal-recommender.component.html',
  styleUrls: ['./journal-recommender.component.scss'],
  imports: [RouterLink, ManSimpleButtonComponent, NgIf, AsyncPipe, SlicePipe, NgForOf, TranslatedDatePipe],
})
export class JournalRecommenderComponent {
  readonly buildArticleUrl = buildArticleUrl;

  @Input() data: JournalData | JournalDataWithArticles;
  @Input() @HostBinding('class.showing-articles') showArticles = true;
  @Output() subscribeClicked = new EventEmitter<void>();
  @Output() pdfClicked = new EventEmitter<void>();

  constructor(private readonly authService: AuthService) {}

  get isSubscribed$(): Observable<boolean> {
    return this.authService.currentUserSubject.asObservable().pipe(map((user) => user?.hasValidSubscription || false));
  }

  get articles(): ArticleCard[] {
    return 'articles' in this.data ? this.data.articles : [];
  }

  get detailsLink(): [string] | undefined {
    return this.showArticles ? [this.data.urlSlug] : undefined;
  }

  publishDate(journal: JournalData): string {
    return typeof journal.publishDate === 'string' ? journal.publishDate : journal.publishDate.date;
  }
}
