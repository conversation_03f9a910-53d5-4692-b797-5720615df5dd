<div class="only-mobile title">
  <a [routerLink]="detailsLink">
    <h1 class="font-main-title">{{ data.name }}</h1>
    <h2 class="font-journal-title">{{ data.mainTitle }}</h2>
    <h3 class="font-journal-subtitle">{{ data.mainText }}</h3>
    <h4 class="font-publish-date">{{ publishDate(data) | manTranslatedDatePipe }}</h4>
  </a>
</div>
<div class="cover">
  <a [routerLink]="detailsLink">
    <img class="cover-image" [src]="data.imageThumbnailUrl" alt="Hetilap - Címoldal" />
  </a>
</div>
<div class="actions only-mobile" [class.mt-24px]="!showArticles">
  <man-simple-button *ngIf="(isSubscribed$ | async) === false" color="primary" [wide]="true" (click)="subscribeClicked.emit()">
    Előfizetek a hetilapra
  </man-simple-button>
  <div>
    <man-simple-button color="light" [wide]="true" (click)="pdfClicked.emit()">Lapozható pdf<span>*</span></man-simple-button>
    <p class="footnote"><span>*</span>Ez a tartalom csak előfizetéssel rendelkező olvasóink számára elérhető.</p>
  </div>
</div>
<div class="details">
  <a [routerLink]="detailsLink">
    <h1 class="font-main-title not-mobile">{{ data.name }}</h1>
    <h2 class="font-journal-title not-mobile">{{ data.mainTitle }}</h2>
    <h3 class="font-journal-subtitle not-mobile">{{ data.mainText }}</h3>
    <h4 class="font-publish-date not-mobile">{{ publishDate(data) | manTranslatedDatePipe }}</h4>
  </a>
  <div class="articles-list" *ngIf="showArticles">
    <ul class="small-articles">
      <li *ngFor="let article of articles | slice: 2 : 8">
        <article class="small-articles-container">
          <a class="font-article-title-small" [routerLink]="buildArticleUrl(article)">{{ article.title }}</a>
        </article>
      </li>
    </ul>
  </div>
  <div class="actions not-mobile" [class.mt-24px]="!showArticles">
    <man-simple-button *ngIf="(isSubscribed$ | async) === false" color="primary" [wide]="true" (click)="subscribeClicked.emit()">
      Előfizetek a hetilapra
    </man-simple-button>
    <div>
      <man-simple-button color="light" [wide]="true" (click)="pdfClicked.emit()">Lapozható pdf <span>*</span></man-simple-button>
      <p class="footnote"><span>*</span>Ez a tartalom csak előfizetéssel rendelkező olvasóink számára elérhető.</p>
    </div>
  </div>
</div>
<div class="highlighted" *ngIf="showArticles">
  <article *ngFor="let article of articles | slice: 0 : 2">
    <div class="highlighted-articles-image">
      <img [src]="article.thumbnailUrl || article.thumbnail?.url || '/assets/images/logo-small.svg'" alt="Hetilap - Cikk" loading="lazy" />
    </div>
    <div class="font-article-title-large">
      <a [routerLink]="buildArticleUrl(article)">{{ article.title }}</a>
    </div>
  </article>
</div>
