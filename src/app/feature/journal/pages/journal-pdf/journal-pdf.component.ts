import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, tap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { AsyncPipe, NgIf } from '@angular/common';
import { BypassPipe } from '@trendency/kesma-ui';
import { JournalDataWithColumns } from '../../../../shared';

@Component({
  selector: 'app-journal-pdf',
  templateUrl: 'journal-pdf.component.html',
  styleUrls: ['journal-pdf.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, BypassPipe, NgIf],
})
export class JournalPdfComponent {
  readonly pdfEmbedCode$ = this.activatedRoute.data.pipe(
    map(({ data }) => (data as JournalDataWithColumns).pdfEmbedCode),
    tap((pdfEmbedCode) => {
      if (pdfEmbedCode) {
        return;
      }
      this.router.navigate(['/', '404']).then();
    })
  );

  get isSafari(): boolean {
    return this.utils.isBrowser() && this.utils.isBrowser('safari');
  }

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService
  ) {}
}
