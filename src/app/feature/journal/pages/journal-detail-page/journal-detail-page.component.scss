@use 'shared' as *;
@use '../../style/journal-typography' as *;

.journal-detail {
  margin-top: 30px;
}

.ad-container {
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  padding: 20px 0 30px 0;
}

.container {
  width: fit-content;
  max-width: 792px;
  margin: 70px auto 10px auto;

  @include media-breakpoint-down(md) {
    max-width: 100%;
    margin: 20px;
  }
}

.back-button {
  max-width: 384px;
  margin: 20px auto;

  @include media-breakpoint-down(md) {
    max-width: 100%;
    margin: 20px;
  }

  &-content {
    font-weight: 700;
    line-height: 14px;
  }
}

man-breadcrumb {
  @include media-breakpoint-down(md) {
    margin-left: 20px;
  }
}

.journal-content {
  display: flex;
  flex-direction: column;
  padding-top: 40px;
  gap: 20px;

  @include media-breakpoint-down(md) {
    padding-top: 20px;
  }
}

.column {
  &-title {
    margin-bottom: 10px;
  }

  &-data {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .article {
      display: flex;
      flex-direction: column;
      gap: 5px;
      padding-bottom: 5px;
      margin-bottom: 20px;
      border-bottom: 1px solid var(--kui-gray-200);

      @include media-breakpoint-down(md) {
        padding-bottom: 15px;
      }

      &-image {
        width: 100%;

        a img {
          width: 100%;
          object-fit: cover;
        }
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 28px;
        line-height: 34px;
      }

      &-lead {
        font-weight: 400;
        font-size: 16px;
        line-height: 21px;
      }
    }
  }
}
