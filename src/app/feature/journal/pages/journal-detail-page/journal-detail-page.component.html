<section class="journal-detail">
  <div class="wrapper">
    <man-breadcrumb [items]="[{ label: 'Hetilap' }]"></man-breadcrumb>

    <main class="container" *ngIf="data$ | async as data">
      <!-- RECOMMENDER -->
      <app-journal-recommender [data]="data" [showArticles]="false" (subscribeClicked)="subscribe()" (pdfClicked)="pdfClicked(data)"></app-journal-recommender>
      <!-- /RECOMMENDER -->

      <!-- CONTENT -->
      <section class="journal-content">
        <div class="column-data" *ngFor="let columnData of data.columns">
          <h3 class="column-title font-column-title">{{ columnData.title | limitString: 100 }}</h3>
          <div *ngFor="let article of columnData.articles" class="article">
            <div class="article-image">
              <a [routerLink]="article.url">
                <img
                  *ngIf="article.thumbnail?.url || article.thumbnailUrl"
                  [src]="article.thumbnail?.url ?? article.thumbnailUrl"
                  [alt]="article.thumbnail?.alt ?? 'Cikk kép'"
                />
              </a>
            </div>
            <h2 class="article-title">
              <a [routerLink]="article.url">{{ article.title }}</a>
            </h2>
            <p class="article-lead">{{ article.lead }}</p>
          </div>
        </div>
      </section>
      <!-- /CONTENT -->

      <div class="back-button">
        <man-simple-button [wide]="true" (click)="backClicked()">
          <span class="back-button-content">Vissza a Hetilapokhoz</span>
        </man-simple-button>
      </div>
    </main>
  </div>
</section>
