import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import {
  AuthService,
  createMandinerTitle,
  defaultMetaInfo,
  JournalDataWithArticles,
  JournalDataWithColumns,
  JournalService,
  ManBreadcrumbComponent,
  ManSimpleButtonComponent,
} from '../../../../shared';
import { createCanonicalUrlForPageablePage, LimitStringPipe, User } from '@trendency/kesma-ui';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { JournalRecommenderComponent } from '../../components/journal-recommender/journal-recommender.component';

@Component({
  selector: 'app-journal-detail',
  templateUrl: './journal-detail-page.component.html',
  styleUrls: ['./journal-detail-page.component.scss'],
  imports: [ManBreadcrumbComponent, AsyncPipe, NgIf, JournalRecommenderComponent, NgForOf, LimitStringPipe, RouterLink, ManSimpleButtonComponent],
})
export class JournalDetailPageComponent implements OnDestroy, OnInit {
  private readonly destroy$: Subject<void> = new Subject();
  readonly data$ = this.activatedRoute.data.pipe(
    map((res) => {
      return this.setColumnOrder(res['data']);
    }),
    tap((data: JournalDataWithColumns) => {
      this.setMetaData(data.mainTitle);
    }),
    takeUntil(this.destroy$)
  );

  private currentUser: User | undefined;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly router: Router,
    private readonly journalService: JournalService,
    private readonly authService: AuthService
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit(): void {
    this.user$.subscribe((user) => {
      this.currentUser = user;
    });
  }

  subscribe(): void {
    this.journalService.subscribeClicked();
  }

  pdfClicked(item: JournalDataWithColumns): void {
    this.journalService.pdfClicked(this.currentUser, item as any as JournalDataWithArticles);
  }

  backClicked(): void {
    this.router.navigate(['/hetilap']).then();
  }

  private get user$(): Observable<User | undefined> {
    return this.authService.currentUserSubject.asObservable();
  }

  private setMetaData(title: string): void {
    const canonical = createCanonicalUrlForPageablePage('hetilap', this.activatedRoute.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    title = createMandinerTitle(title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  private setColumnOrder(data: JournalDataWithColumns): JournalDataWithColumns {
    // eslint-disable-next-line max-len
    data.columns.sort((a: any, b: any) => +a.articles[0].secondaryColumns[0]?.columnOrder - +b.articles[0].secondaryColumns[0]?.columnOrder);
    return data;
  }
}
