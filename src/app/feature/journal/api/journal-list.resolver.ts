import { Injectable } from '@angular/core';
import { Observable, switchMap } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { JournalDataWithArticles, JournalService } from '../../../shared';

@Injectable()
export class JournalListResolver {
  constructor(private readonly journalService: JournalService) {}

  resolve(): Observable<ApiResult<JournalDataWithArticles[], ApiResponseMetaList>> {
    return this.journalService.getInitialJournalListData(0, 13).pipe(
      switchMap((data) =>
        this.journalService.getFeaturedArticlesOfJournal(data.data[0].urlSlug).pipe(
          map((articles) => {
            data.data[0].articles = articles;
            return data;
          })
        )
      )
    );
  }
}
