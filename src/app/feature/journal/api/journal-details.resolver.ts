import { ActivatedRouteSnapshot, Params, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { JournalDataWithColumns, JournalService } from '../../../shared';

@Injectable()
export class JournalDetailsResolver {
  constructor(
    private readonly service: JournalService,
    private readonly router: Router
  ) {}

  resolve(snapshot: ActivatedRouteSnapshot): Observable<JournalDataWithColumns> {
    return this.getJournalData$(snapshot.params);
  }

  private getJournalData$(params: Params): Observable<JournalDataWithColumns> {
    return this.service.getJournalDetails(params['slug']).pipe(
      catchError((error) => {
        this.router
          .navigate(['/', '404'], {
            state: {
              errorResponse: JSON.stringify(error),
            },
            skipLocationChange: true,
          })
          .then();
        return throwError(error);
      })
    );
  }
}
