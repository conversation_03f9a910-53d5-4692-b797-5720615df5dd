@use 'shared' as *;

.loader {
  position: fixed;
  top: 55px;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 9999;
  background: white;

  &:after {
    content: '';
    box-sizing: border-box;
    display: inline-block;
    height: 30px;
    width: 30px;
    margin-left: 10px;
  }

  @include media-breakpoint-up(md) {
    display: none;
  }
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
