import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { defaultMetaInfo } from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, LayoutComponent],
})
export class HomeComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
  private readonly document = inject(DOCUMENT);
  destroy$ = new Subject<void>();
  LayoutPageType = LayoutPageType;
  readonly routeData$ = this.route.data.pipe(map(({ layoutData }) => layoutData as LayoutApiData));

  readonly struct$ = this.routeData$.pipe(map(({ struct }) => struct));

  private links: HTMLCollectionOf<HTMLAnchorElement>;

  isLoader = signal<boolean>(true);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly analytics: AnalyticsService,
    private readonly elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      this.isLoader.set(false);
    }
    this.setMetaData();
  }

  ngAfterViewInit(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    queueMicrotask(() => {
      this.links = this.elementRef.nativeElement.getElementsByTagName('a');
      Array.from(this.links ?? []).forEach((element) => {
        element.addEventListener('click', this.handleAnchorClickEvent.bind(this));
        element.addEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
      });
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (!this.utilsService.isBrowser()) {
      return;
    }

    Array.from(this.links ?? []).forEach((element) => {
      element.removeEventListener('click', this.handleAnchorClickEvent.bind(this));
      element.removeEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
    });
  }

  private setMetaData(): void {
    const title = 'Mandiner.hu | Címlap';
    const description =
      'Friss hírek a Mandiner.hu oldalán. Belföld, Külföld, Sport, Kultúra. ' +
      'A magyarországi nyilvánosságban megjelenő legújabb és legfontosabb véleménycikkek.';
    const meta: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description,
      ogDescription: description,
    };
    this.seo.setMetaData(meta);
    this.seo.updateCanonicalUrl('');
  }

  private handleAnchorClickEvent(mouseEvent: MouseEvent): void {
    const link = (mouseEvent.composedPath().find((elem: EventTarget) => (elem as Element).nodeName === 'A') as HTMLAnchorElement)?.href;
    link && this.analytics.sendMainPageClick(link, this.document.referrer);
  }
}
