import { Ng<PERSON>orO<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AnalyticsService, ArticleCard, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { Subject, takeUntil } from 'rxjs';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import {
  ApiService,
  ArticleCardType,
  createMandinerTitle,
  defaultMetaInfo,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSimpleButtonComponent,
  SocialInteractionEvent,
  SocialInteractionEventType,
} from '../../shared';
import { searchResultToArticleCard } from '../tags-page/tags-page.utils';

@Component({
  selector: 'app-foundation-content',
  templateUrl: './foundation-content.component.html',
  styleUrls: ['./foundation-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, NgForOf, ManArticleCardComponent, NgIf, ManSimpleButtonComponent, StrossleAdvertComponent],
})
export class FoundationContentComponent implements OnInit, OnDestroy {
  articles: ArticleCard[] = [];
  MAX_RESULTS_PER_PAGE = 20;
  isLoading = false;
  page = 0;
  rowAllCount = 0;

  ArticleCardType = ArticleCardType;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly analyticsService: AnalyticsService,
    private readonly api: ApiService
  ) {}

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.MAX_RESULTS_PER_PAGE * (this.page + 1) < this.rowAllCount;
  }

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.setMetaData();
      this.getFoundationContentArticles();
    });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item.id ?? item.slug ?? index?.toString();
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }

  loadMoreResults(): void {
    if (this.canLoadMore) {
      this.page += 1;
      this.getFoundationContentArticles();
    }
  }

  getFoundationContentArticles(): void {
    this.isLoading = true;
    this.api.getSearch({ isFoundationContent: 'true' }, this.page, this.MAX_RESULTS_PER_PAGE).subscribe((res) => {
      this.articles = this.articles.concat(res.data.map((sr) => searchResultToArticleCard(sr)));
      this.rowAllCount = res.meta?.limitable?.rowAllCount || 0;
      this.isLoading = false;
      this.cd.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.seo.currentUrl, this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Alapkőtartalom');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
