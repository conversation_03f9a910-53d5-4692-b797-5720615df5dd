<section>
  <div class="wrapper foundation-content-wrapper">
    <man-breadcrumb [items]="[{ label: 'Tartalmaink' }]"></man-breadcrumb>

    <h1 class="foundation-content-title">Tartalmaink</h1>
    <div class="foundation-content-results-meta">
      <div class="foundation-content-results-counter">
        <strong>{{ rowAllCount }} db</strong> cikk <PERSON>
      </div>
    </div>

    <div class="foundation-content-list">
      <ng-container *ngFor="let article of articles; let i = index; trackBy: trackByFn">
        <man-article-card
          [data]="article"
          [isMplus]="article.isPaywalled"
          [isTagVisible]="true"
          [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
          class="article-card"
        >
        </man-article-card>

        <div *ngIf="i === 3" class="desktop">
          <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
        </div>

        <div *ngIf="i === 7" class="desktop">
          <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
        </div>

        <div *ngIf="i === 11" class="desktop">
          <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
        </div>

        <div *ngIf="i === 15" class="desktop">
          <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
        </div>
      </ng-container>

      <ng-container *ngIf="isLoading">
        <div class="no-results-text">
          <p>Betöltés...</p>
        </div>
      </ng-container>

      <man-simple-button (click)="loadMoreResults()" *ngIf="canLoadMore">Mutass többet</man-simple-button>
    </div>
  </div>
</section>
