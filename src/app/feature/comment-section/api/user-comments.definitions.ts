import { ApiResponseMetaList, Api<PERSON><PERSON>ult, Comment } from '@trendency/kesma-ui';

export type UserCommentsType = {
  title: string;
  publishDate?: string;
  fullSizeUrl?: string;
  column: string;
  lead: string;
  commentCount: string;
  comments: {
    id: string;
    createdAt: string;
    commentText: string;
  }[];
};

export type CommentHistory = {
  original: Partial<Comment>;
  latest: Partial<Comment>;
};

export type UserCommentsResponse = ApiResult<UserCommentsType[], ApiResponseMetaList>;
