import { BehaviorSubject, Observable } from 'rxjs';
import { CommentAnswerFormState, CommentingState } from '@trendency/kesma-ui';
import { COMMENTING_STATES } from './commenting-states.const';

/** View Model for Comments */
export class AnsweringState {
  private readonly _actualState$: BehaviorSubject<CommentingState>;
  private readonly _isLoading$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(
    private _state: CommentAnswerFormState = 'idle',
    private _action: keyof typeof COMMENTING_STATES = 'creating'
  ) {
    this._actualState$ = new BehaviorSubject<CommentingState>(COMMENTING_STATES[this._action].idle);
  }

  /** Observable for the actual state of the comment form */
  get actual$(): Observable<CommentingState> {
    return this._actualState$.asObservable();
  }

  /** Observable for the loading state of the comment form */
  get isLoading$(): Observable<boolean> {
    return this._isLoading$.asObservable();
  }

  /**
   * Getter for the loading state of the comment form.
   * @note Do NOT use this in TEMPLATE! Use isLoading$ instead!
   */
  get loading(): boolean {
    return this._isLoading$.value;
  }

  /** Setter for the loading state of the comment form. Triggers the isLoading$ observable. */
  set loading(value: boolean) {
    this._isLoading$.next(value);
  }

  /**
   * Getter for whether the comment form is in idle state.
   * @note Do NOT use this in TEMPLATE!
   */
  get isIdle(): boolean {
    return this._actualState$.value.isIdle;
  }

  /**
   * Change current state to the given state. If timeout is given, it will change back to idle state after the given time.
   * @param state The state to change to
   * @param timeout The time to wait before changing back to idle state
   */
  changeTo(state: CommentAnswerFormState, timeout?: number): Promise<void> {
    this._state = state;
    this._actualState$.next(COMMENTING_STATES[this._action][this._state]);
    return new Promise<void>((resolve) => {
      if (!timeout) {
        resolve();
        return;
      }
      setTimeout(() => {
        this._state = 'idle';
        this._actualState$.next(COMMENTING_STATES[this._action][this._state]);
        resolve();
      }, timeout);
    });
  }

  /** Switch to comment editing mode */
  setEditing(): void {
    this._action = 'editing';
    this._actualState$.next(COMMENTING_STATES[this._action][this._state]);
  }

  /** Switch to comment creating mode (default) */
  setCreating(): void {
    this._action = 'creating';
    this._actualState$.next(COMMENTING_STATES[this._action][this._state]);
  }
}
