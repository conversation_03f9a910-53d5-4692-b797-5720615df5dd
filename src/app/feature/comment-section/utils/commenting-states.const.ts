import { CommentAnswerFormState, CommentingState } from '@trendency/kesma-ui';

export const COMMENTING_STATES: {
  creating: { [key in CommentAnswerFormState]: CommentingState };
  editing: { [key in CommentAnswerFormState]: CommentingState };
  answering: { [key in CommentAnswerFormState]: CommentingState };
} = {
  creating: {
    idle: {
      buttonText: 'Hozzászólás küldése',
      buttonColor: 'primary',
      isIdle: true,
      isDisabled: false,
    },
    busy: {
      buttonText: 'Küldés...',
      buttonColor: 'primary',
      isIdle: false,
      isDisabled: true,
    },
    success: {
      buttonText: 'Elküldve',
      buttonColor: 'success',
      isIdle: false,
      isDisabled: false,
    },
    error: {
      buttonText: 'Hiba',
      buttonColor: 'danger',
      isIdle: false,
      isDisabled: false,
    },
  },
  editing: {
    idle: {
      buttonText: 'Mentés',
      buttonColor: 'info',
      isIdle: true,
      isDisabled: false,
    },
    busy: {
      buttonText: 'Mentés...',
      buttonColor: 'outline',
      isIdle: false,
      isDisabled: true,
    },
    success: {
      buttonText: 'Mentve',
      buttonColor: 'success',
      isIdle: false,
      isDisabled: false,
    },
    error: {
      buttonText: 'Hiba',
      buttonColor: 'danger',
      isIdle: false,
      isDisabled: false,
    },
  },
  answering: {
    idle: {
      buttonText: 'Válasz',
      buttonColor: 'primary',
      isIdle: true,
      isDisabled: false,
    },
    busy: {
      buttonText: 'Küldés...',
      buttonColor: 'primary',
      isIdle: false,
      isDisabled: true,
    },
    success: {
      buttonText: 'Elküldve',
      buttonColor: 'success',
      isIdle: false,
      isDisabled: false,
    },
    error: {
      buttonText: 'Hiba',
      buttonColor: 'danger',
      isIdle: false,
      isDisabled: false,
    },
  },
} as const;
