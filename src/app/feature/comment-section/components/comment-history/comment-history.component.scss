:host {
  display: flex;
  padding: 20px;
  border: 1px solid var(--kui-gray-50);
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
}

h2,
h3,
p {
  font-family: var(--kui-font-primary);
  font-style: normal;
  font-weight: 400;
}

.col {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 20px 10px 20px;

  h2 {
    font-size: 20px;
    font-weight: 700;
    line-height: 135%;
  }

  .icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
}

.original {
  h3 {
    color: var(--kui-orange-600);
    font-size: 14px;
    line-height: normal;
  }
}

.timestamp {
  color: var(--kui-gray-550);
  font-size: 12px;
  line-height: 12px;
}

.latest {
  .timestamp {
    text-decoration: underline;
  }
}

.content {
  color: var(--kui-gray-900);
  font-size: 18px;
  font-style: normal;
  line-height: 24px;
}

.icon-chevron-down {
  width: 9px;
  height: 9px;
}

.separator {
  width: 100%;
  height: 1px;
  background-color: var(--kui-gray-50);
}
