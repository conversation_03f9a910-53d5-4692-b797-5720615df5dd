import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommentHistory } from '../../api/user-comments.definitions';
import { PublishDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-comment-history',
  templateUrl: './comment-history.component.html',
  styleUrls: ['./comment-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PublishDatePipe],
})
export class CommentHistoryComponent {
  @Input() data: CommentHistory | null;
  @Output() closed = new EventEmitter<void>();

  close(): void {
    this.closed.emit();
  }
}
