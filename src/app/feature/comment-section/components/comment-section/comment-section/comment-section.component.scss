:host {
  font-family: var(--kui-font-primary);
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.section-title {
  font-weight: 700;
  font-size: 20px;
  line-height: 135%;
}

.comment-warning {
  font-weight: 400;
  font-size: 18px;
  line-height: 140%;
  padding: 20px;
  background: var(--kui-blue-150);
  width: 100%;

  a {
    text-decoration: underline;
    font-weight: 700;
  }
}

.sort {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  span {
    font-weight: 400;
    font-size: 14px;
    line-height: 15px;
    color: var(--kui-gray-775);
  }

  select {
    background: none;
    border: none;
    font-weight: 700;
    font-size: 14px;
    color: var(--kui-black);
  }
}

.refresh {
  color: var(--kui-white);
}

.comments {
  &-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 0;
    transition: height 0.3s ease-in-out;

    &.active {
      height: 50px;
    }
  }

  app-comment-card-container:not(:last-child) {
    padding-bottom: 10px;
    border-bottom: 2px solid var(--kui-gray-50);
    margin-bottom: 20px;
  }
}

.guest-overlay {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: calc(100% + 10px);
  height: 194px;
  top: -145px;
  left: -10px;
  padding: 0 10px;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  font-family: var(--kui-font-primary);
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  margin-bottom: -145px;

  .buttons {
    display: flex;
    gap: 10px;
    width: fit-content;
  }
}

.response-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;

  man-counting-text-area {
    margin: 0;
  }
}
