import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CommentService } from '../../../api/comment.service';
import { AuthService, ManSimpleButtonComponent, ManSpinnerComponent } from '../../../../../shared';
import { combineLatest, EMPTY, first, ReplaySubject } from 'rxjs';
import { LoadMorePaginator, Ordering } from '@trendency/kesma-ui';
import { AnsweringState } from '../../../utils/answering.state';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Router, RouterLink } from '@angular/router';
import { CommentAnswerComponent } from '../../comment-answer/comment-answer.component';
import { UtilService } from '@trendency/kesma-core';
import { <PERSON><PERSON><PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AsyncPipe } from '@angular/common';
import { CommentCardContainerComponent } from '../../comment-card-container/comment-card-container.component';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-comment-section',
  templateUrl: './comment-section.component.html',
  styleUrls: ['./comment-section.component.scss'],
  imports: [
    NgIf,
    RouterLink,
    CommentAnswerComponent,
    FormsModule,
    NgFor,
    CommentCardContainerComponent,
    AsyncPipe,
    ManSimpleButtonComponent,
    ManSpinnerComponent,
  ],
})
export class CommentSectionComponent implements AfterViewInit {
  @ViewChild('scrollTarget', { static: true }) scrollTarget: ElementRef<HTMLElement> | undefined;
  answerSubmitted = false;
  sort: Ordering = 'latest';
  @Input() allCommentCount = 0;
  @Output() commentSubmitted = new EventEmitter<void>();
  readonly state = new AnsweringState();
  readonly user$ = this.authService.currentUserSubject.asObservable();
  readonly isGuest$ = this.user$.pipe(map((user) => !user));
  readonly articleId$ = new ReplaySubject<string>(1);
  readonly paginator = new LoadMorePaginator((page) =>
    combineLatest({
      articleId: this.articleId$,
      isGuest: this.isGuest$,
    }).pipe(
      first(),
      switchMap(({ articleId, isGuest }) => this.commentService.getCommentsForArticle(articleId, page, isGuest ? 4 : 20, this.sort))
    )
  );

  constructor(
    private readonly commentService: CommentService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly utilService: UtilService,
    private readonly scroller: ViewportScroller
  ) {}

  @Input() set articleID(value: string) {
    this.articleId$.next(value);
    this.paginator.reset();
  }

  ngAfterViewInit(): void {
    if (!this.router.url.endsWith('#kommentek') || !this.scrollTarget) {
      return;
    }

    if (this.utilService.isBrowser()) {
      this.setupLayoutObserver();
    }
  }

  private setupLayoutObserver(): void {
    let finish = 0;
    const observer = new IntersectionObserver((entries) =>
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Element is visible
          finish = setTimeout(() => observer.disconnect(), 2000) as unknown as number; // Layout shift settled, disconnect
          return;
        }

        // Element is not visible
        setTimeout(() => this.scrollToTarget(), 100); // Layout shifted, follow
        clearTimeout(finish);
      })
    );

    observer.observe(this.scrollTarget?.nativeElement as HTMLElement);
  }

  loadMore(): void {
    this.paginator.next();
  }

  submitAnswer(text: string, formComponent: CommentAnswerComponent): void {
    if (!this.state.isIdle) {
      return;
    }

    this.state.changeTo('busy').then();
    this.answerSubmitted = true;
    this.articleId$
      .pipe(
        switchMap((articleId) => this.commentService.submitComment(articleId, text)),
        first(),
        catchError((err) => {
          console.error('[CommentSectionComponent::onSubmit] Error:', err);
          this.state.changeTo('error', 2000).then();
          return EMPTY;
        })
      )
      .subscribe(() => {
        this.state.changeTo('success', 2000).then();
        formComponent.reset();
        this.allCommentCount++;
        this.commentSubmitted.emit();
      });
  }

  onSortChange(): void {
    this.paginator.reset();
  }

  promptLogin(to: 'bejelentkezes' | 'regisztracio'): void {
    this.router.navigate([`/${to}`], { queryParams: { redirect: this.router.url } }).then();
  }

  scrollToTarget(): void {
    if (!this.scrollTarget || !this.utilService.isBrowser()) {
      return;
    }

    this.scroller.scrollToAnchor('kommentek');
  }

  reloadComments(): void {
    this.answerSubmitted = false;
    this.paginator.reset();
  }
}
