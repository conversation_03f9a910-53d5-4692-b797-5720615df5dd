<div #scrollTarget id="kommentek"></div>
<ng-container *ngIf="paginator.data$ | async as data; else spinner">
  <ng-container *ngIf="state.actual$ | async as actualState">
    <h2 class="section-title">Összesen {{ allCommentCount }} komment</h2>
    <div class="comment-warning" *ngIf="isGuest$ | async">
      A kommentek nem szerkesztett tartalmak, tartalmuk a szerzőjük álláspontját tükrözi. Mielőtt hozzászólna, k<PERSON>rj<PERSON>k, olvassa el a
      <a [routerLink]="['/felhasznalasi-feltetelek']">kommentszabályzatot</a>.
    </div>
    <ng-container *ngIf="user$ | async">
      <app-comment-answer
        #form
        *ngIf="actualState"
        [buttonColor]="actualState.buttonColor"
        [buttonText]="actualState.buttonText"
        [isIdle]="actualState.isIdle"
        (response)="submitAnswer($event, form)"
      ></app-comment-answer>
    </ng-container>
    <p *ngIf="answerSubmitted">K<PERSON>sz<PERSON>njük kommentjét! Hozzászólása gyorsabb megjelenítéséhez, kérjük, kattintson a komment frissítése gombra!</p>
    <man-simple-button color="primary" (click)="reloadComments()">
      <span class="refresh">Kommentek frissítése</span>
    </man-simple-button>
    <div class="sort">
      <span>Sorrend: </span>
      <select [(ngModel)]="sort" (change)="onSortChange()">
        <option value="latest">Legújabb hozzászólások elöl</option>
        <option value="oldest">Legrégebbi hozzászólások elöl</option>
        <option value="most-popular">Legtöbbet kedveltek elöl</option>
      </select>
    </div>

    <section class="comments">
      <div class="comments-spinner" [class.active]="paginator.isLoading$ | async">
        <man-spinner *ngIf="paginator.isLoading$ | async"></man-spinner>
      </div>
      <app-comment-card-container *ngFor="let comment of data ?? []" [data]="comment" [sort]="sort"></app-comment-card-container>
    </section>

    <div class="guest-overlay" *ngIf="isGuest$ | async">
      <div>Jelenleg csak a hozzászólások egy kis részét látja. Hozzászóláshoz és a további kommentek megtekintéséhez lépjen be, vagy regisztráljon!</div>
      <div class="buttons">
        <man-simple-button color="primary" (click)="promptLogin('regisztracio')"> Regisztráció </man-simple-button>
        <man-simple-button color="tertiary" (click)="promptLogin('bejelentkezes')"> Bejelentkezés </man-simple-button>
      </div>
    </div>

    <man-simple-button *ngIf="(paginator.hasMore$ | async) && (user$ | async)" (click)="loadMore()" [disabled]="actualState.isDisabled">
      Továbbiak betöltése
    </man-simple-button>
  </ng-container>
</ng-container>

<ng-template #spinner>
  <man-spinner></man-spinner>
</ng-template>
