import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ButtonStyleInfo, Comment, extendResponseChain, LoadMorePaginator, Ordering, PopoverService, ReactionEvent } from '@trendency/kesma-ui';
import { BehaviorSubject, combineLatest, debounceTime, EMPTY, ReplaySubject, Subject, switchMap } from 'rxjs';
import { CommentService } from '../../api/comment.service';
import { catchError, filter, first, map, shareReplay, takeUntil, tap } from 'rxjs/operators';
import { AuthService, ManCommentCardComponent, ManSimpleButtonComponent, ManSpinnerComponent } from '../../../../shared';
import { AnsweringState } from '../../utils/answering.state';
import { Router } from '@angular/router';
import { CommentAnswerComponent } from '../comment-answer/comment-answer.component';
import { CommentHistory } from '../../api/user-comments.definitions';
import { CommentHistoryComponent } from '../comment-history/comment-history.component';
import { NgIf, NgFor, AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-comment-card-container',
  templateUrl: './comment-card-container.component.html',
  styleUrls: ['./comment-card-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, CommentAnswerComponent, CommentHistoryComponent, NgFor, AsyncPipe, ManCommentCardComponent, ManSpinnerComponent, ManSimpleButtonComponent],
})
export class CommentCardContainerComponent implements OnInit, OnDestroy {
  readonly _data$ = new BehaviorSubject<Comment>(this._commentService.getTempComment());
  /** Level of the comment. 0 is top level */
  @Input() level = 0;
  /** Sort order of the child comments */
  @Input() sort: Ordering = 'latest';
  readonly history$ = new ReplaySubject<CommentHistory>(1);
  readonly MAX_INDENT = 2;
  /** State of the answer form. Loading is used to display progress spinners */
  readonly answerFormState = new AnsweringState();
  readonly dropdownStates$ = new BehaviorSubject({
    optionsOpen: false,
    answerFormOpen: false,
    childrenOpen: false,
    isEditing: false,
    isHistoryOpen: false,
  });
  readonly paginator = new LoadMorePaginator<Comment>(
    (page) =>
      this._data$.pipe(
        switchMap((data) => this._commentService.getAnswersForComment(data.id, page, 5, this.sort).pipe(extendResponseChain(data))),
        tap((res) => {
          const answerCount = res.meta?.limitable?.rowAllCount ?? 0;
          this.mutateComment({ answerCount: answerCount });
          if (answerCount === 0) {
            this.changeState({ childrenOpen: false });
          }
        }),
        catchError((err) => {
          console.error('[CommentCardContainerComponent] getAnswersForComment', err);
          return EMPTY;
        })
      ),
    EMPTY
  );
  readonly isLoggedIn$ = this._auth.currentUserSubject.pipe(map((user) => !!user));
  readonly popupOptions = {
    inPlace: true,
    closeOnScroll: true,
    offset: { x: '-87%', y: '10px' },
  };
  readonly optionButtonStyle: ButtonStyleInfo = {
    icon: {
      width: '20px',
      height: '20px',
      'margin-right': '10px',
    },
  };
  readonly vm$ = combineLatest({
    currentComment: this._data$,
    dropdowns: this.dropdownStates$,
    state: this.answerFormState.actual$,
  }).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  private readonly _destroy$ = new Subject<void>();
  private readonly _debouncedSubmit$ = new Subject<{ text: string; form: CommentAnswerComponent; update?: boolean }>();

  constructor(
    private readonly _commentService: CommentService,
    public readonly _auth: AuthService,
    private readonly router: Router,
    private readonly popoverService: PopoverService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get data(): Comment {
    return this._data$.value;
  }

  /** Comment to display */
  @Input() set data(value: Comment) {
    this._data$.next(value);
  }

  ngOnInit(): void {
    if (this.data.responseTo) {
      this.mutateComment({ responseTo: [this.data.responseTo[this.data.responseTo.length - 1]] });
    }

    this._commentService.dropDownOpened // When a comment card opens a dropdown...
      .pipe(
        filter((id) => id !== this.data.id), // ...and it's not this one...
        takeUntil(this._destroy$) // ...and this component is not destroyed...
      )
      .subscribe(
        () =>
          this.changeState({
            optionsOpen: false,
            answerFormOpen: false,
            isEditing: false,
            isHistoryOpen: false,
          }) // ...close options and response forms, but not children
      );

    this._debouncedSubmit$.pipe(debounceTime(500), takeUntil(this._destroy$)).subscribe(({ text, form, update }) => this.submitAnswer(text, form, update));

    // Trigger CD when scroll event closes the popover:
    this.popoverService.isPopupOpen$
      .pipe(
        filter((isOpen) => !isOpen),
        takeUntil(this._destroy$)
      )
      .subscribe(() => {
        this.changeState({ optionsOpen: false });
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  toggleResponse(show: boolean): void {
    if (!this._auth.currentUser) {
      this.promptLogin();
      return;
    }

    if (show) {
      this.answerFormState.setCreating();
    }

    this.notifyService(show);
    this.changeState({
      answerFormOpen: show,
      optionsOpen: false,
      isEditing: false,
      isHistoryOpen: false,
    });
  }

  toggleOptions(show: boolean): void {
    this.notifyService(show);

    this.changeState({
      optionsOpen: show,
      answerFormOpen: false,
    });
  }

  submitReaction(reaction: ReactionEvent): void {
    if (!this._auth.currentUser) {
      this.promptLogin();
      return;
    }

    this.answerFormState.loading = true;
    if (reaction.liked) {
      this._commentService
        .likeComment(reaction.commentId)
        .pipe(first())
        .subscribe(() => {
          switch (this.data.myReaction) {
            case 'like':
              this.mutateComment({
                likeCount: this.data.likeCount - 1,
                myReaction: undefined,
              });
              break;
            case 'dislike':
              this.mutateComment({
                likeCount: this.data.likeCount + 1,
                dislikeCount: this.data.dislikeCount - 1,
                myReaction: 'like',
              });
              break;
            default:
              this.mutateComment({
                likeCount: this.data.likeCount + 1,
                myReaction: 'like',
              });
          }
          this.answerFormState.loading = false;
        });
      return;
    }

    this._commentService
      .dislikeComment(reaction.commentId)
      .pipe(first())
      .subscribe(() => {
        switch (this.data.myReaction) {
          case 'dislike':
            this.mutateComment({
              dislikeCount: this.data.dislikeCount - 1,
              myReaction: undefined,
            });
            break;
          case 'like':
            this.mutateComment({
              likeCount: this.data.likeCount - 1,
              dislikeCount: this.data.dislikeCount + 1,
              myReaction: 'dislike',
            });
            break;
          default:
            this.mutateComment({
              dislikeCount: this.data.dislikeCount + 1,
              myReaction: 'dislike',
            });
        }
        this.answerFormState.loading = false;
      });
  }

  handleSubmit(text: string, form: CommentAnswerComponent): void {
    if (this.answerFormState.loading) {
      return; // To avoid duplicate create requests.
    }
    this._debouncedSubmit$.next({ text, form });
  }

  handleUpdate(text: string, form: CommentAnswerComponent): void {
    this.mutateComment({ updatedAt: new Date(), isUpdated: true });
    this._debouncedSubmit$.next({ text, form, update: true });
  }

  submitAnswer(text: string, form: CommentAnswerComponent, update = false): void {
    if (!this._auth.currentUser) {
      this.promptLogin();
      return;
    }

    if (!this.answerFormState.isIdle) {
      return;
    }

    this.answerFormState.loading = true;

    const request$ = update ? this._commentService.editComment(this.data.id, text) : this._commentService.submitAnswer(this.data.id, text);
    request$
      .pipe(
        first(),
        catchError((err) => {
          console.error('[CommentCardContainerComponent] submitAnswer', err);
          this.answerFormState.changeTo('error', 2000).then(() => {
            this.answerFormState.loading = false;
          });
          return EMPTY;
        })
      )
      .subscribe(() => {
        if (update) {
          this.mutateComment({
            text,
            updatedAt: new Date(),
          });
        } else {
          setTimeout(() => this.paginator.reset(), 2000);
          this.mutateComment({ answerCount: this.data.answerCount + 1 });
        }
        this.answerFormState.changeTo('success', 2000).then(() => {
          this.answerFormState.loading = false;
          if (!update) {
            form.reset();
            this.toggleResponse(false);
            return;
          }
          this.stopEdit();
        });
      });
  }

  toggleChildren(): void {
    this.changeState({ childrenOpen: !this.dropdownStates$.value.childrenOpen });

    if (!this.paginator.data?.length) {
      this.paginator.reset(); // Only load data if there is none
    }
  }

  loadMoreChildren(): void {
    if (!this._auth.currentUser) {
      this.promptLogin();
      return;
    }

    this.paginator.next();
  }

  canOpenOptions(data: Comment): boolean {
    return data.author.uid === this._auth.currentUser?.uid || !!data.isUpdated;
  }

  startEdit(): void {
    this.changeState({ isEditing: true, optionsOpen: false, answerFormOpen: false, isHistoryOpen: false });
    this.answerFormState.setEditing();
    this.popoverService.destroyPopover();
    this.notifyService(true);
  }

  stopEdit(): void {
    this.changeState({ isEditing: false });
    this.answerFormState.setCreating();
    this.notifyService(false);
  }

  openHistory(data: Comment): void {
    this.changeState({
      isHistoryOpen: true,
      optionsOpen: false,
      childrenOpen: false,
      answerFormOpen: false,
      isEditing: false,
    });
    this.popoverService.destroyPopover();

    this._commentService
      .getHistory(data)
      .pipe(first())
      .subscribe((res) => this.history$.next(res));
    this.notifyService(true);
  }

  closeHistory(): void {
    this.changeState({ isHistoryOpen: false });
    this.notifyService(false);
  }

  private promptLogin(): void {
    this.router.navigate(['bejelentkezes']).then();
  }

  private changeState(param: Partial<typeof this.dropdownStates$.value>): void {
    this.dropdownStates$.next({ ...this.dropdownStates$.value, ...param });
  }

  private notifyService(show: boolean): void {
    if (show) {
      this._commentService.openDropdownForId(this.data.id);
      return;
    }
    this._commentService.closeDropdownForId(this.data.id);
  }

  private mutateComment(update: Partial<Comment>): void {
    this.data = { ...this.data, ...update };
  }
}
