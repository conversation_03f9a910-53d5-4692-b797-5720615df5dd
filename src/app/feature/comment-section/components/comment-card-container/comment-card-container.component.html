<ng-container *ngIf="vm$ | async as vm">
  <man-comment-card
    (optionsShownChange)="toggleOptions($event)"
    (reaction)="submitReaction($event)"
    (responseFormShownChange)="toggleResponse($event)"
    [canAnswer]="vm.currentComment.id !== 'new'"
    [canOpenOptions]="canOpenOptions(vm.currentComment)"
    [canReact]="vm.currentComment.id !== 'new'"
    [data]="vm.currentComment"
    [hasAdvancedContent]="vm.dropdowns.isEditing"
    [isLoggedIn]="(isLoggedIn$ | async) ?? false"
    [maxResponseLevel]="1"
    [optionsShown]="vm.dropdowns.optionsOpen"
    [optionsTemplate]="options"
    [popupOptions]="popupOptions"
    [responseFormShown]="vm.dropdowns.answerFormOpen"
  >
    <div class="comment-edit-form" content>
      <app-comment-answer
        #form
        (cancel)="stopEdit()"
        (response)="handleUpdate($event, form)"
        [buttonColor]="vm.state.buttonColor"
        [buttonText]="vm.state.buttonText"
        [content]="vm.currentComment.text"
        [isDisabled]="!vm.state.isIdle"
        [isIdle]="vm.state.isIdle"
        type="edit"
      ></app-comment-answer>
    </div>
    <app-comment-history
      *ngIf="vm.dropdowns.isHistoryOpen && (history$ | async)"
      afterContent
      [data]="history$ | async"
      (closed)="closeHistory()"
    ></app-comment-history>
  </man-comment-card>
  <man-spinner *ngIf="answerFormState.isLoading$ | async" [height]="10" [width]="30" class="reaction-spinner"></man-spinner>

  <div *ngIf="vm.dropdowns.answerFormOpen">
    <app-comment-answer
      #form
      (response)="handleSubmit($event, form)"
      [buttonColor]="vm.state.buttonColor"
      [buttonText]="vm.state.buttonText"
      [isIdle]="vm.state.isIdle"
      type="answer"
    ></app-comment-answer>
  </div>

  <div class="response-count">
    <man-simple-button (click)="toggleChildren()" *ngIf="vm.currentComment.answerCount" color="link">
      <i [class.rotate]="vm.dropdowns.childrenOpen" class="comment-card-answer-toggle-icon icon icon-mandiner-chevron-right"></i>
      <span class="show-more-text"> {{ vm.currentComment.answerCount }} válasz {{ vm.dropdowns.childrenOpen ? 'elrejtése' : 'megtekintése' }} </span>
    </man-simple-button>
  </div>

  <div
    *ngIf="vm.dropdowns.childrenOpen"
    [class.ms-20px]="level < MAX_INDENT"
    [class.ms-n10px]="level >= MAX_INDENT"
    [class.has-border-left]="level < MAX_INDENT"
    class="comment-card-sub"
  >
    <ng-container>
      <app-comment-card-container *ngFor="let child of (paginator.data$ | async) ?? []" [data]="child" [level]="level + 1"></app-comment-card-container>
      <ng-container *ngIf="(paginator.isLoading$ | async) === false; else loading">
        <man-simple-button (click)="loadMoreChildren()" *ngIf="(isLoggedIn$ | async) && (paginator.hasMore$ | async)" color="primary">
          Továbbiak betöltése
        </man-simple-button>
      </ng-container>
    </ng-container>
  </div>

  <ng-template #loading>
    <man-spinner></man-spinner>
  </ng-template>

  <ng-template #options>
    <div class="options">
      <man-simple-button
        (click)="startEdit()"
        *ngIf="vm.currentComment.author.uid === _auth.currentUser?.uid"
        [styleInfo]="optionButtonStyle"
        color="link"
        icon="comment-edit"
        iconPosition="left"
      >
        <span> Bejegyzés szerkesztése </span>
      </man-simple-button>
      <man-simple-button
        (click)="openHistory(vm.currentComment)"
        *ngIf="vm.currentComment.isUpdated"
        [styleInfo]="optionButtonStyle"
        color="link"
        icon="user-edit"
        iconPosition="left"
      >
        Szerkesztési előzmények
      </man-simple-button>
    </div>
  </ng-template>
</ng-container>
