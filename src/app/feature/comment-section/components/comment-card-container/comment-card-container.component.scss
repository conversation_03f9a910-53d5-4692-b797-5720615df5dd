@use 'shared' as *;

:host {
  display: block;
}

.comment {
  &-edit-form {
    margin-bottom: 10px;
  }

  &-card {
    margin-bottom: 30px;

    &-sub {
      max-width: 792px;
      margin: 20px 0 0 0;
      padding: 10px;
      border-top: 1px solid var(--kui-gray-100);
    }
    &-answer-toggle {
      font-size: 12px;
      font-weight: 700;
      margin-bottom: 10px;
      margin-left: 20px;
      &-icon {
        transition: 0.2s all ease-out;
        width: 10px;
        height: 10px;
        margin-top: -2px;
      }
    }
  }
}

.rotate {
  transform: rotate(90deg);
}

.show-more-text {
  font-weight: 700;
  font-size: 12px;
  line-height: 15px;
}

.response-count {
  width: fit-content;
  margin-bottom: 10px;
}

.ms-n10px {
  margin-left: -10px;
}

.ms-20px {
  margin-left: 20px;
}

.reaction-spinner {
  position: relative;
  top: -34px;
  left: 230px;
}

.options {
  width: 100%;
  min-width: 320px;
  background-color: var(--kui-white);
  border: 1px solid var(--kui-orange-600);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;

  man-simple-button {
    margin: 0;

    span {
      line-height: 14px;
    }
  }
}

.has-border-left {
  border-left: 1px solid var(--kui-gray-100);
}

man-spinner {
  margin-left: 25px;
}
