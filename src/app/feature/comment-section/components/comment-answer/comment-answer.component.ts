import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService, ManCountingTextAreaComponent, ManSimpleButtonComponent } from '../../../../shared';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { ButtonColor } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-comment-answer',
  templateUrl: './comment-answer.component.html',
  styleUrls: ['./comment-answer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, AsyncPipe, ManCountingTextAreaComponent, ManSimpleButtonComponent],
})
export class CommentAnswerComponent implements OnInit {
  @Input() isDisabled? = false;
  @Input() type: 'comment' | 'answer' | 'edit' = 'comment';
  @Input() content = '';
  @Input() buttonText = 'Hozzászólás küldése';
  @Input() buttonColor: ButtonColor = 'primary';
  @Input() isIdle? = true;

  @Output() response = new EventEmitter<string>();
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() cancel = new EventEmitter<void>();
  /* If comment contains a link, we should notify the user and prevent form submission */
  linkInComment = false;

  readonly answerFormGroup = this.fb.group({
    text: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(1000)]],
  });

  constructor(
    private readonly auth: AuthService,
    private readonly fb: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    if (!this.content) {
      return;
    }

    this.answerFormGroup.patchValue({ text: this.content });
  }

  get username$(): Observable<string | undefined> {
    return this.auth.currentUserSubject.pipe(map((user) => user?.username));
  }

  onSubmit(): void {
    this.linkInComment = false;
    const textInputValue = this.answerFormGroup.value.text;
    const containsLink = this.containsLink(textInputValue);
    if (containsLink) {
      this.linkInComment = true;
      return;
    }
    if (!this.answerFormGroup.valid) {
      return;
    }

    this.response.emit(textInputValue);
  }

  reset(): void {
    this.answerFormGroup.reset();
  }

  onCancel(): void {
    this.cancel.emit();
  }

  private containsLink(input: string): boolean {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return urlRegex.test(input);
  }
}
