<p class="font-username" *ngIf="type !== 'edit'">
  {{ type === 'answer' ? 'Válaszolok' : 'Ho<PERSON>ászólo<PERSON>' }}, mint <span class="font-username-bold">{{ username$ | async }}</span>
</p>
<form [formGroup]="answerFormGroup">
  <man-counting-text-area
    formControlName="text"
    [required]="true"
    [minLength]="3"
    [maxLength]="1000"
    [wide]="true"
    [placeholder]="type === 'answer' ? 'V<PERSON>lasz írása...' : 'Hozzászólás írása...'"
    [disabled]="!!isDisabled"
  ></man-counting-text-area>
</form>
<p *ngIf="linkInComment" class="general-form-error">Link nem helyezhető el a komment szövegében. Kérjük törölje, és próbálja elküldeni új<PERSON>.</p>
<div class="buttons">
  <man-simple-button [color]="buttonColor" [disabled]="!!isIdle && (isDisabled || answerFormGroup.invalid)" (click)="onSubmit()">
    {{ buttonText }}
  </man-simple-button>
  <man-simple-button *ngIf="type === 'edit'" color="outline" (click)="onCancel()"> Mégse </man-simple-button>
</div>
