import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { createCanonicalUrlForPageablePage, emailValidator, KesmaFormControlComponent, markControlsTouched } from '@trendency/kesma-ui';
import { NgIf, ViewportScroller } from '@angular/common';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { environment } from '../../../environments/environment';
import { ApiService, createMandinerTitle, defaultMetaInfo, ManSimpleButtonComponent } from '../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ReactiveFormsModule, KesmaFormControlComponent, ManSimpleButtonComponent, RouterLink],
})
export class ForgotPasswordComponent implements OnInit {
  formGroup: UntypedFormGroup;
  isLoading = false;
  isSubmitted = false;
  error: string | null = null;

  constructor(
    private readonly apiService: ApiService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly viewportScroller: ViewportScroller,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      email: [null, [Validators.required, emailValidator]],
    });
  }

  requestPasswordReset(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_password_forget',
      (recaptchaToken: string) => {
        this.apiService.requestPasswordReset(this.formGroup.value.email, recaptchaToken).subscribe({
          next: () => {
            this.isSubmitted = true;
            this.isLoading = false;
            this.viewportScroller.scrollToPosition([0, 0]);
            this.cdr.detectChanges();
          },
          error: () => {
            // If we have error we just proceed
            this.isSubmitted = true;
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('elfelejtett-jelszo');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Elfelejtett jelszó');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
