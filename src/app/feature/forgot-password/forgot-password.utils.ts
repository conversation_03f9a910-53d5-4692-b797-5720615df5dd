import { BackendUserRequestPasswordResetRequest, BackendUserResetPasswordRequest } from './forgot-password.definitions';

export function requestPasswordResetDataToBackendRequest(email: string, recaptchaToken: string): BackendUserRequestPasswordResetRequest {
  return {
    email,
    recaptcha: recaptchaToken,
  };
}

export function resetPasswordDataToBackendRequest(email: string, password: string, resetPasswordToken: string): BackendUserResetPasswordRequest {
  return {
    email,
    passwordNew: password,
    passwordNewConfirm: password,
    token: resetPasswordToken,
  };
}
