<section>
  <div class="wrapper forgot-form-wrapper">
    <div class="forgot-form-header">
      <h1 class="forgot-form-header-title"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> be<PERSON>a</h1>
      <p class="forgot-form-header-text">
        <PERSON><PERSON>lej<PERSON>tt j<PERSON> v<PERSON>llításának utols<PERSON>, k<PERSON><PERSON>, adjon meg egy ú<PERSON>, amivel a jövőben bejelentkezni kíván a felhasználói
        fiókjába.
      </p>
    </div>
    <div *ngIf="!formGroup" class="general-form-error"><PERSON><PERSON>, ér<PERSON><PERSON><PERSON><PERSON> link, kér<PERSON><PERSON><PERSON> ellenőrizze a böngészőben megadott hivatkozást!</div>
    <form (ngSubmit)="resetPassword()" *ngIf="formGroup" [formGroup]="formGroup" class="forgot-form">
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="password"><PERSON><PERSON> j<PERSON><PERSON> <strong>*</strong></label>
          <div class="mandiner-form-input-password">
            <input [type]="showPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="password" id="password" />
            <img
              (click)="showPassword = !showPassword"
              [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small"
          >A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot.</small
        >
      </div>
      <div *ngIf="error" class="general-form-error">
        {{ error }}
      </div>
      <div class="row forgot-form-button">
        <div class="col-12 col-lg-6">
          <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100">
            {{ isLoading ? 'Kérem, várjon...' : 'Jelszó beállítása' }}
          </man-simple-button>
        </div>
        <div class="col-12 col-lg-6">
          <man-simple-button [disabled]="isLoading" class="w-100" color="outline" routerLink="/bejelentkezes">Mégsem</man-simple-button>
        </div>
      </div>
    </form>
  </div>
</section>

<man-popup
  (resultEvent)="handleSuccessPopupResult()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'Tovább a bejelentkezéshez'"
  [showCancelButton]="false"
  [title]="'Sikeres jelszóváltoztatás'"
>
  Kedves Olvasónk!<br /><br />
  Sikeresen módosította jelszavát. További kellemes olvasást kívánunk!
</man-popup>
