import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { Subject } from 'rxjs';
import { ApiService, ManPopupComponent, ManSimpleButtonComponent } from '../../../shared';
import { KesmaFormControlComponent, markControlsTouched, passwordValidator } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ReactiveFormsModule, KesmaForm<PERSON>ontrolComponent, ManS<PERSON><PERSON><PERSON>uttonComponent, RouterLink, ManPopupComponent],
})
export class ResetPasswordComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<void> = new Subject<void>();

  formGroup: UntypedFormGroup;
  showPassword = false;
  showSuccessPopup = false;
  isLoading = false;
  error: string | null = null;

  email: string | null = null;
  token: string | null = null;

  constructor(
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      this.email = params['email'];
      this.token = params['token'];
      if (this.email && this.token) {
        this.initForm();
      }
    });
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      password: [null, [Validators.required, passwordValidator]],
    });
  }

  resetPassword(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.apiService.resetPassword(this.email ?? '', this.formGroup.value.password, this.token ?? '').subscribe({
      next: () => {
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      },
      error: () => {
        this.error = 'Hiba, az új jelszó beállításra kiküldött link érvénytelen vagy lejárt, kérem próbálja újra!';
        this.isLoading = false;
        this.cdr.detectChanges();
      },
    });
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/bejelentkezes']);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
