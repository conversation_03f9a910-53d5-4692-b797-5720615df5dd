<section>
  <div class="wrapper forgot-form-wrapper">
    <!-- Forgot password form -->
    <ng-container *ngIf="!isSubmitted">
      <div class="forgot-form-header">
        <h1 class="forgot-form-header-title">Elfelejtett jelszó</h1>
        <p class="forgot-form-header-text">
          Elfelejtette jelszavát? <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg regisztrált e-mail-cí<PERSON>t, amely<PERSON> küldünk egy linket. A linkre kattintva továbbnavigáljuk Önt, ahol új
          jelszót adhat meg felhasználói fiókjához.
        </p>
      </div>
      <form *ngIf="formGroup" [formGroup]="formGroup" class="forgot-form" (ngSubmit)="requestPasswordReset()">
        <div class="mandiner-form-row">
          <kesma-form-control>
            <label class="mandiner-form-label" for="email">E-mail-cím</label>
            <input class="mandiner-form-input" type="text" id="email" formControlName="email" />
          </kesma-form-control>
        </div>
        <div class="general-form-error" *ngIf="error">
          {{ error }}
        </div>
        <div class="row forgot-form-button">
          <div class="col-12 col-lg-6">
            <man-simple-button class="w-100" [disabled]="isLoading" [isSubmit]="true">
              {{ isLoading ? 'Kérem, várjon...' : 'Folytatás' }}
            </man-simple-button>
          </div>
          <div class="col-12 col-lg-6">
            <man-simple-button class="w-100" color="outline" [disabled]="isLoading" routerLink="/bejelentkezes">Mégsem </man-simple-button>
          </div>
        </div>
      </form>
    </ng-container>

    <!-- Submitted forgot password form -->
    <ng-container *ngIf="isSubmitted">
      <div class="forgot-form-header">
        <h1 class="forgot-form-header-title">Elfelejtett jelszó</h1>
        <p class="forgot-form-header-text">
          Amennyiben a megadott e-mail-cím létezik rendszerünkben, küldtünk rá egy linket. A linkre kattintva továbbnavigáljuk Önt az oldalunkra, ahol új
          jelszót adhat meg felhasználói fiókjához.
        </p>
        <man-simple-button color="outline" routerLink="/">Tovább a főoldalra</man-simple-button>
      </div>
    </ng-container>
  </div>
</section>
