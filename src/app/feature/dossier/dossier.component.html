<section class="dossier">
  <ng-container *ngIf="dossierCard">
    <man-dossier-card [data]="dossierCard" [hasButton]="false"></man-dossier-card>
  </ng-container>

  <div class="wrapper with-aside">
    <div class="left-column">
      <man-article-card [data]="dossier" [styleID]="2" [isMplus]="false" *ngFor="let dossier of dossiers"></man-article-card>
      <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()" class="more-btn">To<PERSON><PERSON><PERSON><PERSON></man-simple-button>
    </div>

    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
