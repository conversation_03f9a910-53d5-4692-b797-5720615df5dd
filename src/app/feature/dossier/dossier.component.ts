import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import {
  ApiService,
  createMandinerTitle,
  defaultMetaInfo,
  DossierCard,
  ManArticleCardComponent,
  ManDossierCardComponent,
  ManSimpleButtonComponent,
} from '../../shared';
import { ArticleCard, BackendArticleSearchResult, createCanonicalUrlForPageablePage, mapBackendArticleDataToArticleCard } from '@trendency/kesma-ui';
import { takeUntil } from 'rxjs/operators';
import { mapDossierCard } from './utils/dossier.utils';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-dossier',
  templateUrl: './dossier.component.html',
  styleUrls: ['./dossier.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManDossierCardComponent, ManArticleCardComponent, ManSimpleButtonComponent, SidebarComponent, NgForOf],
})
export class DossierComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<boolean>();
  dossiers: ArticleCard[] = [];
  dossierCard: DossierCard;
  slug: string;
  rowAllCount: number;
  pageIndex = 0;
  articlesPerPage: number;

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.articlesPerPage * (this.pageIndex + 1) < this.rowAllCount;
  }

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly cd: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.dossierCard = mapDossierCard(data?.['dossier']);
      this.setMetaData(this.dossierCard.title, data?.['dossier']?.meta.description || '');
      this.dossiers = (data?.['dossier']?.data as BackendArticleSearchResult[]).map((result) => mapBackendArticleDataToArticleCard(result));
      this.rowAllCount = data?.['dossier']?.meta.limitable.rowAllCount || 0;
      this.articlesPerPage = data?.['dossier']?.meta.dataCount;
    });
    this.slug = this.route.snapshot.params['slug'];
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private getDossierBySlug(slug: string, pageIndex: number): void {
    this.apiService
      .getDossier(slug, pageIndex, this.articlesPerPage)
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ data }) => {
        this.dossiers.push(...(data as unknown as BackendArticleSearchResult[]).map((result) => mapBackendArticleDataToArticleCard(result)));
        this.cd.detectChanges();
      });
  }

  loadMoreResults(): void {
    if (!this.canLoadMore) return;

    this.pageIndex += 1;
    this.getDossierBySlug(this.slug, this.pageIndex);
  }

  private setMetaData(title: string, description: string): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    title = createMandinerTitle(title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description: description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }
}
