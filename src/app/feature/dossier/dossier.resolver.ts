import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';

import { Observable, throwError } from 'rxjs';
import { ApiResponseMetaList, ApiR<PERSON>ult, BasicDossier, DossierArticle } from '@trendency/kesma-ui';
import { ApiService } from '../../shared';
import { catchError } from 'rxjs/operators';

@Injectable()
export class DossierResolver {
  public constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    const slug = route.params?.['slug'];
    const articlesPerPage = 13;

    return this.apiService.getDossier(slug, 0, articlesPerPage).pipe(
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(() => err);
      })
    );
  }
}
