@use 'shared' as *;

:host {
  man-dossier-card {
    width: calc(100% + 140px);
    margin-left: -70px;

    @include media-breakpoint-down(md) {
      width: 100%;
      margin-left: 0;
    }
  }

  man-article-card {
    max-width: none;

    &:last-child {
      ::ng-deep {
        mandiner-social-interactions {
          border-bottom: 0;
        }
      }
    }

    ::ng-deep {
      .article-card-figure-image {
        width: 100%;
      }
    }
  }

  @include media-breakpoint-down(md) {
    aside {
      display: none;
    }
  }
}

.more-btn {
  margin-bottom: 20px;
  @include media-breakpoint-down(sm) {
    width: 100%;
  }
}
