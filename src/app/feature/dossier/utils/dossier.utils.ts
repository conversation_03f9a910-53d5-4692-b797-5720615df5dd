import { ApiResult, DossierArticle } from '@trendency/kesma-ui';
import { DossierCard } from '../../../shared';

export const mapDossierCard = (dossier: ApiResult<DossierArticle[]>): DossierCard => {
  return {
    title: dossier?.meta?.['title'],
    slug: '',
    thumbnail: dossier?.meta?.['thumbnail'],
    thumbnailCreatedAt: new Date(),
    articles: mapDossierArticles(dossier.data),
  };
};

export const mapDossierArticles = (articles: Partial<DossierArticle>[]): Partial<DossierArticle>[] => {
  return articles.map((article) => {
    return {
      ...article,
      publishDate: new Date(article.publishDate as unknown as string),
    };
  });
};
