import { Routes } from '@angular/router';
import { AuthorPageResolver } from './api/author-page.resolver';
import { AuthorPageComponent } from './components/author-page/author-page.component';
import { AuthorListComponent } from './components/author-list/author-list.component';
import { AuthorListResolver } from './api/author-list.resolver';

export const authorRoutes: Routes = [
  {
    path: '',
    component: AuthorListComponent,
    pathMatch: 'full',
    resolve: {
      data: AuthorListResolver,
    },
    providers: [AuthorListResolver],
  },
  {
    path: ':authorSlug',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: AuthorPageComponent,
    resolve: {
      data: AuthorPageResolver,
    },
    providers: [AuthorPageResolver],
  },
];
