import { Async<PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  ArticleAuthor,
  ArticleCard,
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  FollowStatus,
  getStructuredDataForProfilePage,
  mapBackendArticleDataToArticleCardWithHideThumbnail,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { environment } from '../../../../../environments/environment';
import {
  ApiService,
  AuthorData,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  FollowHelperService,
  makeBreadcrumbSchema,
  ManArticleCardComponent,
  ManAuthorInfoComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  ManSpinnerComponent,
  Paginator,
  SearchFilterDataService,
  SearchFilterDefinitions,
  TranslatedDatePipe,
} from '../../../../shared';

@Component({
  selector: 'app-author-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    AsyncPipe,
    ManAuthorInfoComponent,
    ManSearchFilterComponent,
    NgIf,
    TranslatedDatePipe,
    ManArticleCardComponent,
    ManSpinnerComponent,
    ManSimpleButtonComponent,
    NgForOf,
    StrossleAdvertComponent,
  ],
})
export class AuthorPageComponent implements OnInit, OnDestroy {
  breadcrumbItems?: BreadcrumbItem[];

  // Author data.
  public author$: Observable<ArticleAuthor | null> = this.activatedRoute.data.pipe(
    tap((res) => {
      const author = res['data'][1];
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(getStructuredDataForProfilePage(author as any, environment?.siteUrl ?? ''));
    }),
    tap((res) => this.setMetaData(res['data'][1].publicAuthorName)),
    map((res) => this.mapAuthorDataToArticleAuthor(res['data'][1]))
  );
  isAuthorFollowed$: Observable<FollowStatus> = this.author$.pipe(switchMap((author) => this.followHelperService.getAuthorFollowedStatus(author?.slug ?? '')));
  isAuthorFollowButtonLoading = false;
  // Search filter columns
  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((res) => res['data'][2]));
  private authorSlug!: string;
  private readonly destroy$: Subject<boolean> = new Subject();
  private filterValues: FilterValues;
  readonly paginator = new Paginator<ArticleCard>(
    (page) => {
      const searchDateRange = this.searchFilterDataService.setSearchQuery(this.filterValues);
      return this.apiService
        .getArticlesByOwnMaterialType(
          this.authorSlug,
          page,
          20,
          this.isOrderByDateAsc(),
          this.filterValues?.keyword ?? '',
          searchDateRange.fromDate,
          searchDateRange.toDate,
          this.filterValues?.contentTypes ?? [],
          this.filterValues?.columns ?? []
        )
        .pipe(
          map(({ data, meta }) => ({
            data: data.map(mapBackendArticleDataToArticleCardWithHideThumbnail),
            meta,
          }))
        );
    },
    this.activatedRoute.data.pipe(
      map(({ data: result }) => ({
        data: result[0].data.map(mapBackendArticleDataToArticleCardWithHideThumbnail),
        meta: result[0].meta,
      }))
    )
  );

  constructor(
    private readonly apiService: ApiService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly activatedRoute: ActivatedRoute,
    private readonly seoService: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly route: ActivatedRoute,
    private readonly searchFilterDataService: SearchFilterDataService,
    private readonly followHelperService: FollowHelperService
  ) {}

  ngOnInit(): void {
    this.authorSlug = this.activatedRoute.snapshot.paramMap.get('authorSlug') as string;

    this.breadcrumbItems = [
      {
        label: 'Szerzőink',
        url: ['/', 'szerzo'],
      },
      {
        label: this.activatedRoute.snapshot.data['data'][1].publicAuthorName,
      },
    ];

    const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
    this.schemaService.insertSchema(breadcrumbSchema);

    this.activatedRoute.data.pipe(takeUntil(this.destroy$)).subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  isEqualsCurrentDateWithPreviousDate(index: number): boolean {
    this.paginator.data;
    const currentDate = (this.paginator.data?.[index]?.publishDate as string)?.substring(0, 7);
    const previousDate = (this.paginator.data?.[index - 1]?.publishDate as string)?.substring(0, 7);
    return currentDate !== previousDate;
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.paginator.reset();
  }

  isOrderByDateAsc(): boolean {
    if (!this.filterValues?.sort?.length) {
      return false;
    }

    const sortFields = this.filterValues.sort.split('_');

    return sortFields[0] === 'date' && sortFields[1] === 'asc';
  }

  handleAuthorFollow(authorSlug: string): void {
    this.isAuthorFollowButtonLoading = true;
    this.followHelperService.handleAuthorFollow(authorSlug).subscribe(() => {
      this.isAuthorFollowButtonLoading = false;
      this.changeDetector.detectChanges();
    });
  }

  private setMetaData(authorName: string): void {
    if (!authorName) {
      return;
    }

    const canonical = createCanonicalUrlForPageablePage('szerzo', this.activatedRoute.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createMandinerTitle(`${authorName} cikkei`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }

  private mapAuthorDataToArticleAuthor(data: AuthorData): ArticleAuthor | null {
    if (!data.publicAuthorName) {
      return null;
    }

    return {
      name: data.publicAuthorName,
      description: data.publicAuthorDescription,
      facebook: data.facebook,
      instagram: data.instagram,
      tiktok: data.tiktok,
      avatarUrl: data.avatar.fullSizeUrl,
      avatar: data.avatar.fullSizeUrl,
      title: data.rank,
      slug: data.slug,
    };
  }
}
