<section>
  <div class="wrapper">
    <div class="left-column">
      <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems"></man-breadcrumb>

      <ng-container *ngIf="author$ | async as author">
        <man-author-info
          (followEvent)="handleAuthorFollow(author?.slug ?? '')"
          [data]="author"
          [isAuthorPage]="true"
          [isFollowLoading]="isAuthorFollowButtonLoading"
          [isFollowed]="isAuthorFollowed$ | async"
          [isInnerAuthor]="true"
        ></man-author-info>
      </ng-container>

      <man-search-filter (filterEvent)="onFilter($event)" [data]="(searchFilterColumns$ | async) || undefined" [filterOnSide]="true" [showSearchHeader]="false">
        <strong>{{ (paginator.meta$ | async)?.limitable?.rowAllCount }} db</strong> cikk <PERSON>
      </man-search-filter>

      <ng-container *ngIf="paginator.data$ | async as articles">
        <ng-container *ngFor="let article of articles; index as index">
          <ng-container *ngIf="article?.publishDate as date">
            <div *ngIf="isEqualsCurrentDateWithPreviousDate(index)" class="article-date">
              <strong>{{ date | manTranslatedDatePipe: 'yyyy. LLLL' }}</strong>
            </div>
          </ng-container>

          <man-article-card [data]="article" [styleID]="0"></man-article-card>

          <div *ngIf="index === 3" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
          </div>

          <div *ngIf="index === 7" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
          </div>

          <div *ngIf="index === 11" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
          </div>

          <div *ngIf="index === 15" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>

  <man-spinner *ngIf="paginator.isLoading$ | async"></man-spinner>

  <man-simple-button (click)="paginator.next()" *ngIf="paginator.hasMore$ | async" [disabled]="paginator.isLoading">
    <strong>Mutass többet</strong>
  </man-simple-button>
</section>
