@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  .article-date {
    margin: 30px 0;

    @include media-breakpoint-down(sm) {
      &:first-of-type {
        margin: 15px 0 30px;
      }
    }

    strong {
      font-weight: 800;
      font-size: 18px;
      line-height: 18px;
    }
  }

  man-spinner {
    display: block;
    margin: 18px auto;
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
