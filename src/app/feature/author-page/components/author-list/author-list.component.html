<man-breadcrumb [items]="[{ label: 'Szerzőink' }]"></man-breadcrumb>

<section class="caption">
  <h1>Szerzőink</h1>
</section>
<section class="content">
  <ng-container *ngFor="let author of authors$ | async; let i = index">
    <man-author-info
      (followEvent)="handleAuthorFollow(author?.slug ?? '')"
      [data]="author"
      [isFollowLoading]="loadingFollowedAuthorSlug === author?.slug"
      [isFollowed]="author.isFollowed"
      [isInnerAuthor]="true"
    ></man-author-info>

    <div *ngIf="i === 3" class="desktop">
      <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
    </div>

    <div *ngIf="i === 7" class="desktop">
      <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
    </div>

    <div *ngIf="i === 11" class="desktop">
      <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
    </div>

    <div *ngIf="i === 15" class="desktop">
      <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
    </div>
  </ng-container>
</section>
<section class="show-more">
  <man-simple-button (click)="loadMore()" *ngIf="page < (maxPage ?? 0)" [disabled]="(isLoading$ | async) ?? false">
    <span>{{ ((isLoading$ | async) ?? false) ? 'Kérem, várjon...' : 'Továbbiak betöltése' }}</span>
  </man-simple-button>
</section>
