import { As<PERSON><PERSON><PERSON><PERSON>, NgForOf, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, ArticleAuthor, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import {
  AuthService,
  BackendAuthorData,
  createMandinerTitle,
  defaultMetaInfo,
  FollowHelperService,
  ManAuthorInfoComponent,
  ManBreadcrumbComponent,
  ManSimpleButtonComponent,
} from '../../../../shared';
import { AuthorService } from '../../api/author.service';

@Component({
  selector: 'app-author-list',
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, AsyncPipe, ManAuthorInfoComponent, NgForOf, NgIf, ManSimpleButtonComponent, StrossleAdvertComponent],
})
export class AuthorListComponent implements OnInit, OnDestroy {
  backendAuthors: <AUTHORS>
  isLoading$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  authors$: Observable<ArticleAuthor[]> = combineLatest([this.isLoading$, this.authService.currentUserExtraDataSubject]).pipe(
    map(() => this.backendAuthors.map((author: BackendAuthorData) => this.authorService.mapAuthorDataToArticleAuthor(author)))
  );
  page = 0;
  maxPage = 0;

  loadingFollowedAuthorSlug: string | null = null;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly authorService: AuthorService,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly followHelperService: FollowHelperService,
    private readonly authService: AuthService
  ) {}

  ngOnInit(): void {
    this.setMetaData();

    this.route.data
      .pipe(
        map((res) => res['data'] as ApiResult<BackendAuthorData[], ApiResponseMetaList>),
        tap((res) => (this.maxPage = res.meta.limitable.pageMax || 0)),
        map((res) => res.data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((data: BackendAuthorData[]) => {
        this.backendAuthors = data;
        this.isLoading$.next(false);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  loadMore(): void {
    if (this.isLoading$.value) {
      return;
    }

    this.isLoading$.next(true);
    this.authorService
      .getAuthorsForPageWithMetaData$(this.page + 1, 10, true)
      .pipe(
        map((res) => res.data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((data: BackendAuthorData[]) => {
        this.backendAuthors = [...this.backendAuthors, ...data];
        this.page = this.page + 1;
        this.isLoading$.next(false);
      });
  }

  handleAuthorFollow(authorSlug: string): void {
    this.loadingFollowedAuthorSlug = authorSlug;
    this.followHelperService.handleAuthorFollow(authorSlug).subscribe(() => {
      this.loadingFollowedAuthorSlug = null;
      this.cd.detectChanges();
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Szerzők');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
