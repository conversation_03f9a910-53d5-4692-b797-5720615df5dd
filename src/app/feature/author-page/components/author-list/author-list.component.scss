@use 'shared' as *;

:host {
  display: flex;
  max-width: 1200px;
  margin: 20px auto 0 auto;
  flex-direction: column;

  @media screen and (max-width: 997px) {
    margin: 20px 20px 0 20px;
  }
}

man-breadcrumb {
  margin-left: 2px;
}

.content {
  margin: 0 auto;
  width: 100%;

  @media screen and (min-width: 1200px) {
    width: 996px;
  }
}

.ad-mid-content {
  width: fit-content;
  margin: 50px auto 55px auto;
}

.caption {
  h1 {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    color: var(--kui-orange-600);
    margin: 20px 0 16px 0;
  }
}

.show-more {
  margin: 30px auto 70px auto;
}
