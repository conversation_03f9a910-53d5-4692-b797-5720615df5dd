import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiService } from '../../../shared';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';

@Injectable()
export class AuthorPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const slug = route.params['authorSlug'];
    const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;

    //Use the given opinion articles for the searched slug and add the author info if it exists for that slug.
    //We need 2 requests as there can be articles from separate authors with the same public author field value in the article.
    return forkJoin([
      this.apiService.getArticlesByOwnMaterialType(slug, pageIndex, 20, false).pipe(
        map((res) => {
          if (pageIndex > 0 && res.data.length === 0) {
            throw new Error('No more items.');
          }
          return res;
        }),
        catchError((error) => {
          this.router
            .navigate(['/', '404'], {
              state: {
                errorResponse: JSON.stringify(error),
              },
              skipLocationChange: true,
            })
            .then();
          return throwError(error);
        })
      ),
      this.apiService.getAuthorFromPublicAuthor(slug).pipe(
        map((res) => res['data']),
        //If there is no author, return null, as we don't need 404 if there are still articles for the given slug.
        catchError(() => {
          return of(null);
        })
      ),
      this.searchFilterDataService.getSearchFilterColoumns(),
    ]);
  }
}
