import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ApiService, BackendAuthorData, FollowHelperService, PortalConfigService } from '../../../shared';
import { ApiResponseMetaList, ApiResult, ArticleAuthor, FollowStatus, PortalConfigSetting } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class AuthorService {
  constructor(
    private readonly api: ApiService,
    private readonly portalConfigService: PortalConfigService,
    private readonly followHelperService: FollowHelperService
  ) {}

  /**
   * Returns the author list for the given page with metadata
   * @param page page number, starting with 0
   * @param perPage number of authors per page, default 10
   */
  getAuthorsForPageWithMetaData$(page: number, perPage = 10, isInner?: boolean): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    return this.api.getAuthors(page, perPage, isInner);
  }

  /**
   * Returns the author list for the given page without metadata
   * @param page page number, starting with 0
   * @param perPage number of authors per page, default 10
   */
  getAuthorsForPage$(page: number, perPage = 10, isInner?: boolean): Observable<ArticleAuthor[]> {
    return this.getAuthorsForPageWithMetaData$(page, perPage, isInner).pipe(map((res) => res.data.map((author) => this.mapAuthorDataToArticleAuthor(author))));
  }

  /**
   * Maps the backend response type (AuthorData) to the frontend model (ArticleAuthor) if possible
   * @param data backend response
   */
  mapAuthorDataToArticleAuthor(data: BackendAuthorData): ArticleAuthor {
    const enabledExternalSlug = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_SLUG);
    const isFollowed: FollowStatus = this.followHelperService.isAuthorFollowed(data.slug ?? '');
    return {
      name: data.public_author_name,
      description: data.public_author_description,
      facebook: data.facebook,
      instagram: data.instagram,
      tiktok: data.tiktok,
      avatar: data.avatar?.fullSizeUrl,
      slug: enabledExternalSlug ? data.slug : data.public_author_name,
      title: data.title,
      isFollowed,
    };
  }

  getAd(): any {
    // TODO: Reklám típus
    return {}; // TODO: Reklámok lekérése
  }
}
