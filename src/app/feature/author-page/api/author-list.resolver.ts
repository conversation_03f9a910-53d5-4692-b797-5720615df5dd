import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AuthorService } from './author.service';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { AuthorData } from '../../../shared';

@Injectable()
export class AuthorListResolver {
  constructor(private readonly service: AuthorService) {}

  resolve(): Observable<ApiResult<AuthorData[], ApiResponseMetaList>> {
    return this.service.getAuthorsForPageWithMetaData$(0, 10, true);
  }
}
