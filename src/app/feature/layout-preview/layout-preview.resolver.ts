import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Layout } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { makeMobileLayout, searchPreviewLayoutContentElements } from '../../shared';
import { Request } from 'express';
import { REQUEST, UtilService } from '@trendency/kesma-core';

@Injectable()
export class LayoutPreviewResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly utils: UtilService,
    @Inject(REQUEST) @Optional() private readonly request: Request
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<Layout> {
    return this.apiService.getLayoutPreview(route.params['layoutHash']).pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => {
        const mobileLayout = [makeMobileLayout(searchPreviewLayoutContentElements(data.struct))];
        let isSsrMobile = !!(this.request?.headers as unknown as Record<string, string>)?.['user-agent']?.match(/android|mobile|ios/i);
        if (this.utils.isBrowser() && !isSsrMobile) {
          isSsrMobile = window.innerWidth < 900;
        }
        return {
          ...data,
          struct: isSsrMobile ? mobileLayout : data.struct, // Struct will be shown before angular is loaded
        } as Layout;
      })
    );
  }
}
