@use 'shared' as *;

.register-form {
  max-width: 384px;
  margin: auto;

  &-wrapper {
    max-width: 792px;
    padding: 50px 0 70px;
    margin: auto;

    @include media-breakpoint-down(md) {
      max-width: calc(100% - 30px);
      padding: 20px 0 50px;
    }
  }

  &-header {
    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      line-height: 24px;
      margin-bottom: 20px;
    }

    &-text {
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 20px;

      &-link {
        color: var(--kui-orange-600);
        text-decoration: underline;
      }
    }
  }

  &-button {
    margin-top: 20px;
  }

  &-or {
    display: block;
    text-align: center;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 12px;
    margin: 10px 0;
  }

  &-social {
    man-simple-button {
      margin-bottom: 5px;
    }
  }
}

.resend-button {
  margin-bottom: 20px;

  &::ng-deep button:disabled {
    background-color: var(--kui-gray-700);
  }
}

.general-form-success {
  margin: 20px auto;
  width: fit-content;
  text-align: center;
}
