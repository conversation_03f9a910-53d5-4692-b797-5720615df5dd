<section>
  <div class="wrapper register-form-wrapper">
    <!-- Registration form -->
    <ng-container *ngIf="!isSubmitted">
      <div class="register-form-header">
        <h1 class="register-form-header-title">Regisztráció</h1>
        <p class="register-form-header-text">
          Hozzon létre egy új <PERSON>-felhasználói fiókot, és használja ki a regisztrált olvasóknak járó előnyöket! Ha már van <PERSON>r-fiókja,
          <a [routerLink]="['/', 'bejelentkezes']" class="register-form-header-text-link">itt tud bejelentkezni.</a>
        </p>
      </div>
      <ng-container *ngIf="allowedLoginMethods$ | async as allowedLoginMethods">
        <form (ngSubmit)="register()" *ngIf="formGroup" [formGroup]="formGroup" class="register-form">
          <ng-container *ngIf="allowedLoginMethods.email">
            <div class="mandiner-form-row">
              <kesma-form-control>
                <label class="mandiner-form-label" for="lastName">Vezetéknév <strong>*</strong></label>
                <input class="mandiner-form-input" formControlName="lastName" id="lastName" type="text" />
              </kesma-form-control>
            </div>
            <div class="mandiner-form-row">
              <kesma-form-control>
                <label class="mandiner-form-label" for="firstName">Keresztnév <strong>*</strong></label>
                <input class="mandiner-form-input" formControlName="firstName" id="firstName" type="text" />
              </kesma-form-control>
            </div>
            <div class="mandiner-form-row">
              <kesma-form-control>
                <label class="mandiner-form-label" for="username">Felhasználónév <strong>*</strong></label>
                <input class="mandiner-form-input" formControlName="username" id="username" type="text" />
              </kesma-form-control>
              <small class="mandiner-form-small"
                >Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
                tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
              >
            </div>
            <div class="mandiner-form-row">
              <kesma-form-control>
                <label class="mandiner-form-label" for="email">E-mail-cím <strong>*</strong></label>
                <input class="mandiner-form-input" formControlName="email" id="email" type="email" />
              </kesma-form-control>
              <small class="mandiner-form-small">Ezzel a címmel fogja tudni elérni Mandiner-felhasználói fiókját</small>
            </div>
            <div class="mandiner-form-row">
              <kesma-form-control>
                <label class="mandiner-form-label" for="password">Jelszó <strong>*</strong></label>
                <div class="mandiner-form-input-password">
                  <input [type]="showPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="password" id="password" />
                  <img
                    (click)="showPassword = !showPassword"
                    [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
                    alt="Jelszó megtekintése"
                    class="mandiner-form-input-password-img"
                  />
                </div>
              </kesma-form-control>
              <small class="mandiner-form-small"
                >A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot.</small
              >
            </div>
            <div class="mandiner-form-checkboxes">
              <kesma-form-control class="checkbox">
                <div class="mandiner-form-checkbox-item">
                  <input class="mandiner-form-checkbox" formControlName="newsletter" id="newsletter" type="checkbox" />
                  <label class="mandiner-form-checkbox-label" for="newsletter">Hírlevél-feliratkozás.</label>
                </div>
              </kesma-form-control>
              <kesma-form-control class="checkbox">
                <div class="mandiner-form-checkbox-item">
                  <input class="mandiner-form-checkbox" formControlName="terms" id="terms" type="checkbox" />
                  <label class="mandiner-form-checkbox-label" for="terms">
                    Elolvastam és megértettem a Mandiner.hu
                    <a [routerLink]="['/felhasznalasi-feltetelek']" class="mandiner-form-checkbox-label-link" target="_blank">felhasználási feltételeit</a>
                    és a Mandiner Novum Zrt.
                    <a [routerLink]="['/adatvedelmi-tajekoztato']" class="mandiner-form-checkbox-label-link" target="_blank">adatvédelmi tájékoztatóját</a>, és
                    hozzájárulok, hogy a megadott adataimat a szolgáltató mint adatkezelő a szabályzatban foglaltaknak megfelelően kezelje. *
                  </label>
                </div>
              </kesma-form-control>
              <kesma-form-control class="checkbox">
                <div class="mandiner-form-checkbox-item">
                  <input class="mandiner-form-checkbox" formControlName="marketing" id="marketing" type="checkbox" />
                  <label class="mandiner-form-checkbox-label" for="marketing">
                    Hozzájárulok ahhoz, hogy a megadott személyes adataimat a Mandiner Novum Zrt. - hozzájárulásom visszavonásáig - közvetlen üzletszerzési
                    célra (tájékoztatás, közvélemény- vagy piackutatás, illetve egyéb tájékoztatás – sorsolás, tájékoztatók, kereskedelmi és marketing ajánlatok
                    eljuttatására) felhasználja, és ezzel kapcsolatosan engem az általam megadott elérhetőségeken (így elektronikus levelezés útján vagy postai
                    úton vagy telefonon) megkeressen, illetve részemre nyomtatott és/vagy online sajtótermékekkel kapcsolatos gazdasági reklámot küldjön. A
                    hozzájárulás visszavonására az
                    <a [routerLink]="['/adatvedelmi-tajekoztato']" class="mandiner-form-checkbox-label-link" target="_blank">ADATVÉDELMI TÁJÉKOZTATÓBAN</a>
                    foglaltak szerint van lehetőségem.
                  </label>
                </div>
              </kesma-form-control>
            </div>
            <div *ngIf="error" class="general-form-error">
              {{ error }}
            </div>
            <div class="register-form-button">
              <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100"
                >{{ isLoading ? 'Kérem, várjon...' : 'Regisztráció' }}
              </man-simple-button>
            </div>

            <div
              *ngIf="allowedLoginMethods[SocialProvider.GOOGLE] || allowedLoginMethods[SocialProvider.FACEBOOK] || allowedLoginMethods[SocialProvider.APPLE]"
              class="register-form-or"
            >
              - vagy -
            </div>
          </ng-container>

          <div class="register-form-social">
            <man-simple-button
              (click)="registerWithSocialProvider(SocialProvider.GOOGLE)"
              *ngIf="allowedLoginMethods[SocialProvider.GOOGLE]"
              [disabled]="isLoading"
              class="social w-100"
              color="outline"
              icon="google"
              >Folytatás Google fiókkal
            </man-simple-button>
            <man-simple-button
              (click)="registerWithSocialProvider(SocialProvider.FACEBOOK)"
              *ngIf="allowedLoginMethods[SocialProvider.FACEBOOK]"
              [disabled]="isLoading"
              class="social w-100"
              color="outline"
              icon="facebook"
              >Folytatás Facebook fiókkal
            </man-simple-button>
            <man-simple-button
              (click)="registerWithSocialProvider(SocialProvider.APPLE)"
              *ngIf="allowedLoginMethods[SocialProvider.APPLE]"
              [disabled]="isLoading"
              class="social w-100"
              color="outline"
              icon="apple"
              >Folytatás Apple fiókkal
            </man-simple-button>
          </div>
        </form>
      </ng-container>
    </ng-container>

    <!-- Submitted registration form -->
    <ng-container *ngIf="isSubmitted">
      <div class="register-form-header">
        <h1 class="register-form-header-title">E-mail ellenőrzése</h1>
        <p class="register-form-header-text">Az aktiválásához szükséges adatokat e-mailben küldtük el. Kérjük, ellenőrizze e-mail-fiókját!</p>
        <p class="register-form-header-text">Amennyiben nem látja az email-t újra küldheti azt:</p>
        <form [formGroup]="formGroup" class="register-form">
          <div class="mandiner-form-row">
            <kesma-form-control>
              <label class="mandiner-form-label" for="email">E-mail-cím</label>
              <input class="mandiner-form-input" formControlName="email" id="email" type="email" disabled />
            </kesma-form-control>
          </div>
        </form>

        <div *ngIf="error" class="general-form-error">
          {{ error }}
        </div>

        <div *ngIf="isEmailResent" class="general-form-success">Elküldtük az e-mailt, kérjük ellenőrizze a beérkező levelek között!</div>

        <man-simple-button class="resend-button" (click)="resendVerification()" [disabled]="!!(this.timer$ | async) || !this.formGroup.get('email')?.valid">
          @if (timer$ | async; as time) {
            Várjon {{ time }} másodpercet...
          } @else {
            Újra küldés
          }
        </man-simple-button>
        <man-simple-button color="outline" routerLink="/">Tovább a főoldalra</man-simple-button>
      </div>
    </ng-container>
  </div>
</section>
