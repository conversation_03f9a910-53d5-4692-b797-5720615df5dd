<div class="register-form-wrapper">
  <div class="register-form-header">
    <h1 *ngIf="!isLoading" class="register-form-header-title">
      {{ isLoading || (!isLoading && error) ? 'Regisztráció / Bejelentkezés' : 'Regisztráció véglegesítése' }}
    </h1>
    <p *ngIf="isLoading" class="register-form-header-text">Kérem, várjon...</p>
    <div *ngIf="!isLoading && error" class="general-form-error">
      {{ error }}
    </div>
    <div *ngIf="showAppleInstructions" class="apple-instructions">
      <p>
        Ha már korábban a <strong>Hide My Email</strong> (magyarul: e-mail címem elrejtése) funkciót választotta bejelentkezéshez, az alábbi módokon tudja a
        beállítását megváltoztatni:
      </p>

      <h5>iPhone / iPad eszköz hasz<PERSON></h5>
      <ol>
        <li>Nyissa meg a <strong>⚙️Beállítások</strong> applikációt.</li>
        <li><PERSON><PERSON><PERSON><PERSON> a saját profiljára, ez felül található.</li>
        <li>Válassza a <strong>Jelszó és biztonság</strong> menüpontra.</li>
        <li>Válassza a <strong>Bejelentkezés az Apple-lel</strong> menüpontot.</li>
        <li>Válassza ki a listából a <strong>mandiner</strong> alkalmazást.</li>
        <li>Kattintson az <strong>Apple ID használatának leállítása</strong> gombra.</li>
        <li>Erősítse meg a műveletet a <strong>Használat leállítása</strong> gombra kattintva.</li>
        <li>
          Ezután próbálja újra az oldalunkon az Apple regisztráció / bejelentkezés folyamatot a valós e-mail címe kiválasztásával (<strong
            >Share My Email</strong
          >).
        </li>
      </ol>

      <h5>Böngésző használatával</h5>
      <ol>
        <li>
          Keresse fel az Apple hivatalos weboldalát az
          <a class="register-form-header-text-link" href="https://appleid.apple.com">appleid.apple.com</a> címen és jelentkezzen be a
          <strong>Sign In</strong> gomb segítségével.
        </li>
        <li>
          A <strong>Sign-In and Security</strong> szekcióban válassza ki a <strong>Sign In with Apple</strong>
          kártyát.
        </li>
        <li>A felugró ablakban található listában válassza ki a <strong>mandiner</strong> alkalmazást.</li>
        <li>Törölje az alkalmazást a <strong>Stop using Sign in with Apple</strong> gomb segítségével.</li>
        <li>Erősítse meg törlési szándékát a <strong>Stop using</strong> gomb segítségével.</li>
        <li>
          Ezután próbálja újra az oldalunkon az Apple regisztráció / bejelentkezés folyamatot a valós e-mail címe kiválasztásával (<strong
            >Share My Email</strong
          >).
        </li>
      </ol>
    </div>
    <p *ngIf="!isLoading && error" class="register-form-error-navigation">
      Vissza a <a [routerLink]="['/', 'bejelentkezes']" class="register-form-header-text-link">bejelentkezésre</a> vagy a
      <a [routerLink]="['/', 'regisztracio']" class="register-form-header-text-link">regisztrációs oldalra</a>.
    </p>
    <ng-container *ngIf="!isLoading && !error">
      <p class="register-form-header-text">
        Mandiner fiókját sikeresen összekapcsoltuk közösségi profiljával! Kérjük, a regisztráció véglegesítéséhez ellenőrizze az alábbi adatokat!
      </p>
      <form (ngSubmit)="finalizeRegistration()" *ngIf="formGroup" [formGroup]="formGroup" class="register-form">
        <div class="mandiner-form-row">
          <kesma-form-control>
            <label class="mandiner-form-label" for="lastName">Vezetéknév <strong>*</strong></label>
            <input class="mandiner-form-input" formControlName="lastName" id="lastName" type="text" />
          </kesma-form-control>
        </div>
        <div class="mandiner-form-row">
          <kesma-form-control>
            <label class="mandiner-form-label" for="firstName">Keresztnév <strong>*</strong></label>
            <input class="mandiner-form-input" formControlName="firstName" id="firstName" type="text" />
          </kesma-form-control>
        </div>
        <div class="mandiner-form-row">
          <kesma-form-control>
            <label class="mandiner-form-label" for="username">Felhasználónév <strong>*</strong></label>
            <input class="mandiner-form-input" formControlName="username" id="username" type="text" />
          </kesma-form-control>
          <small class="mandiner-form-small"
            >Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
            tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
          >
        </div>
        <div class="mandiner-form-checkboxes">
          <kesma-form-control class="checkbox">
            <div class="mandiner-form-checkbox-item">
              <input class="mandiner-form-checkbox" formControlName="newsletter" id="newsletter" type="checkbox" />
              <label class="mandiner-form-checkbox-label" for="newsletter">Hírlevél-feliratkozás.</label>
            </div>
          </kesma-form-control>
          <kesma-form-control class="checkbox">
            <div class="mandiner-form-checkbox-item">
              <input class="mandiner-form-checkbox" formControlName="terms" id="terms" type="checkbox" />
              <label class="mandiner-form-checkbox-label" for="terms">
                Elolvastattam és megértettem a Mandiner.hu
                <a class="mandiner-form-checkbox-label-link" href="https://mediaworks.hu/elofizetoi-aszf" target="_blank">felhasználási feltételeit</a>
                és a Mandiner Novum Zrt.
                <a class="mandiner-form-checkbox-label-link" href="https://mediaworks.hu/adatvedelem" target="_blank">adatvédelmi tájékoztatóját</a>, és
                hozzájárulok, hogy a megadott adataimat a szolgáltató mint adatkezelő a szabályzatban foglaltaknak megfelelően kezelje. *
              </label>
            </div>
          </kesma-form-control>
          <kesma-form-control class="checkbox">
            <div class="mandiner-form-checkbox-item">
              <input class="mandiner-form-checkbox" formControlName="marketing" id="marketing" type="checkbox" />
              <label class="mandiner-form-checkbox-label" for="marketing">
                Hozzájárulok ahhoz, hogy a megadott személyes adataimat a Mandiner Novum Zrt. - hozzájárulásom visszavonásáig - közvetlen üzletszerzési célra
                (tájékoztatás, közvélemény- vagy piackutatás, illetve egyéb tájékoztatás – sorsolás, tájékoztatók, kereskedelmi és marketing ajánlatok
                eljuttatására) felhasználja, és ezzel kapcsolatosan engem az általam megadott elérhetőségeken (így elektronikus levelezés útján vagy postai úton
                vagy telefonon) megkeressen, illetve részemre nyomtatott és/vagy online sajtótermékekkel kapcsolatos gazdasági reklámot küldjön. A hozzájárulás
                visszavonására az
                <a [routerLink]="['/adatvedelmi-tajekoztato']" class="mandiner-form-checkbox-label-link" target="_blank">ADATVÉDELMI TÁJÉKOZTATÓBAN</a>
                foglaltak szerint van lehetőségem.
              </label>
            </div>
          </kesma-form-control>
        </div>
        <div *ngIf="formError" class="general-form-error">
          {{ formError }}
        </div>
        <div class="register-form-button">
          <man-simple-button [disabled]="isFormLoading" [isSubmit]="true" class="w-100"
            >{{ isFormLoading ? 'Kérem, várjon...' : 'Regisztráció véglegesítése' }}
          </man-simple-button>
        </div>
      </form>
    </ng-container>
  </div>
</div>
