import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, RouterLink } from '@angular/router';
import { takeUntil, finalize } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ApiService, ManSimpleButtonComponent } from '../../../shared';
import { NgIf } from '@angular/common';
import { BackendFormErrors, KesmaFormControlComponent, markControlsTouched, passwordValidator, usernameValidator } from '@trendency/kesma-ui';
import { FormBuilder, ReactiveFormsModule, ValidatorFn, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-confirm-registration',
  templateUrl: './confirm-registration.component.html',
  styleUrls: ['./confirm-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, ManSimpleButtonComponent, KesmaFormControlComponent, ReactiveFormsModule],
})
export class ConfirmRegistrationComponent implements OnInit, OnDestroy {
  isLoading = true;
  showPassword = false;
  error: string | null = null;
  savePasswordUrl: string | null = null;

  readonly unsubscribe$: Subject<void> = new Subject<void>();

  readonly formGroup = inject(FormBuilder).group({
    username: ['', [Validators.required, usernameValidator as ValidatorFn]],
    password: ['', [Validators.required, passwordValidator as ValidatorFn]],
  });

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      const id = params['id'];
      const hashedEmail = params['hashedEmail'];
      const expiration = params['expiration'];
      const signature = params['signature'];

      if (!(id && hashedEmail && expiration && signature)) {
        this.isLoading = false;
        this.error = 'Hiba, érvénytelen link, kérjük ellenőrizze a böngészőben megadott hivatkozást!';
        this.cdr.detectChanges();
        return;
      }

      this.verifyRegister(id, hashedEmail, expiration, signature);
    });
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): void {
    this.apiService
      .verifyRegister(id, hashedEmail, expiration, signature)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        })
      )
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          this.error = null;
          if (response.needToSetPassword) {
            this.savePasswordUrl = response.savePasswordUrl;
            this.formGroup.get('username')?.setValue(response.userData.userName);
          }
          this.cdr.detectChanges();
        },
        error: (response: HttpErrorResponse) => {
          this.handleVerifyError(response);
        },
      });
  }

  savePassword(): void {
    markControlsTouched(this.formGroup);
    const username = this.formGroup.get('username')?.value;
    const password = this.formGroup.get('password')?.value;

    if (this.formGroup.invalid || !this.savePasswordUrl || !username || !password) {
      return;
    }

    this.isLoading = true;
    this.error = null;

    this.apiService
      .savePassword(this.savePasswordUrl, username, password)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        })
      )
      .subscribe({
        next: () => {
          this.savePasswordUrl = null;
        },
        error: (response: HttpErrorResponse) => {
          this.handleSavePasswordError(response); // BE Gives different error response here
          this.isLoading = false;
        },
      });
  }

  private handleVerifyError(response: HttpErrorResponse): void {
    const backendErrors = response.error as BackendFormErrors;
    let isErrorHandled = false;
    if (backendErrors?.form?.errors?.children) {
      for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
        // User with the same username is already registered
        if (errorKey === 'userName' && !!value.errors) {
          if (value.errors.find((error) => error?.includes('100'))) {
            this.formGroup.get('username')?.setErrors({ usernameMaxLength: true });
          } else {
            this.formGroup.get('username')?.setErrors({ usernameInUse: true });
          }
          isErrorHandled = true;
        }
      }
    }
    if (!isErrorHandled) {
      this.error = 'Hiba, a regisztráció megerősítésére kiküldött link érvénytelen vagy lejárt, kérem próbálja újra!';
    }
  }

  private handleSavePasswordError(response: HttpErrorResponse): void {
    const backendErrors = (response.error as { errors: Record<string, string[]> })?.errors;
    let isErrorHandled = false;
    if (backendErrors) {
      for (const [errorKey, value] of Object.entries(backendErrors)) {
        // User with the same username is already registered
        if (errorKey === 'userName' && !!value) {
          if (value.find((error) => error?.includes('100'))) {
            this.formGroup.get('username')?.setErrors({ usernameMaxLength: true });
          } else {
            this.formGroup.get('username')?.setErrors({ usernameInUse: true });
          }
          isErrorHandled = true;
        }
      }
    }
    if (!isErrorHandled) {
      this.error = 'Ismeretlen hiba! Kérjük próbálja újra később!';
    }
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
