<div class="register-form-wrapper">
  <div class="register-form-header">
    <h1 class="register-form-header-title">Regisztrá<PERSON>ó véglegesítése</h1>
    <p class="register-form-header-text" *ngIf="isLoading">Kérem, várjon...</p>
    <div class="general-form-error" *ngIf="!isLoading && error">
      {{ error }}
    </div>
    <p class="register-form-error-navigation" *ngIf="!isLoading && error">
      Vissza a <a class="register-form-header-text-link" [routerLink]="['/', 'bejelentkezes']">bejelentkezésre</a> vagy a
      <a class="register-form-header-text-link" [routerLink]="['/', 'regisztracio']">regisztr<PERSON><PERSON><PERSON> oldalra</a>.
    </p>
    <ng-container *ngIf="!isLoading && !error && !savePasswordUrl">
      <p class="register-form-header-text">Regisztrációja sikeres. Kellemes olvasást kívánunk!</p>
      <man-simple-button color="outline" routerLink="/bejelentkezes">Tovább a bejelentkezésre</man-simple-button>
    </ng-container>

    <form [formGroup]="formGroup" class="save-password-form" *ngIf="!error && savePasswordUrl">
      <p class="register-form-header-text">Utolsó lépésként, Kérjük, adja meg a felhasználónevét és jelszavát.</p>
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="username">Felhasználónév <strong>*</strong></label>
          <input class="mandiner-form-input" formControlName="username" id="username" type="text" />
        </kesma-form-control>
        <small class="mandiner-form-small"
          >Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
          tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
        >
      </div>

      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="password">Jelszó <strong>*</strong></label>
          <div class="mandiner-form-input-password">
            <input [type]="showPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="password" id="password" />
            <img
              (click)="showPassword = !showPassword"
              [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small"
          >A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot.</small
        >
      </div>

      <div class="register-form-button">
        <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" (click)="savePassword()"
          >{{ isLoading ? 'Kérem, várjon...' : 'Mentés' }}
        </man-simple-button>
      </div>
    </form>
  </div>
</div>
