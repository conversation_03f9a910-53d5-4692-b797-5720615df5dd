import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { AsyncPipe, DOCUMENT, NgIf, ViewportScroller } from '@angular/common';
import { environment } from '../../../environments/environment';
import { ReCaptchaV3Service } from 'ngx-captcha';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManSimpleButtonComponent,
  SocialProvider,
} from '../../shared';
import { IMetaData, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { Observable, Subject, throwError, timer } from 'rxjs';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  emailValidator,
  KesmaFormControlComponent,
  markControlsTouched,
  nonWhitespaceOnlyValidator,
  passwordValidator,
  usernameValidator,
} from '@trendency/kesma-ui';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { catchError, map, startWith, switchMap, share, finalize, distinctUntilChanged } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

const RESEND_TIMEOUT_SECONDS = 30;

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, AsyncPipe, ReactiveFormsModule, KesmaFormControlComponent, ManSimpleButtonComponent],
})
export class RegistrationComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  isSubmitted = false;
  isEmailResent = false;
  error: string | null = null;
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();
  private readonly timerRestartTrigger$ = new Subject<void>();
  readonly timer$ = this.timerRestartTrigger$.pipe(
    startWith(null),
    takeUntilDestroyed(),
    switchMap(() => timer(0, 1000)),
    map((i) => Math.max(RESEND_TIMEOUT_SECONDS - i, 0)),
    // Note: Timer will not stop at 0, because completing it will cause the async pipe to unsubscribe
    distinctUntilChanged(),
    share()
  );

  constructor(
    private readonly apiService: ApiService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly viewportScroller: ViewportScroller,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    private readonly authService: AuthService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
    this.saveArticleUrlIntoStorage();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      lastName: [null, [Validators.required, nonWhitespaceOnlyValidator]],
      firstName: [null, [Validators.required, nonWhitespaceOnlyValidator]],
      username: [null, [Validators.required, usernameValidator]],
      email: [null, [Validators.required, emailValidator]],
      password: [null, [Validators.required, passwordValidator]],
      newsletter: [false],
      terms: [false, Validators.requiredTrue],
      marketing: [false],
    });
  }

  register(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_register',
      (recaptchaToken: string) => {
        this.apiService.register(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isSubmitted = true;
            this.isLoading = false;
            const email = this.formGroup.get('email')?.value;
            this.formGroup.reset();
            this.formGroup.get('email')?.setValue(email);
            this.viewportScroller.scrollToPosition([0, 0]);
            this.cdr.detectChanges();
          },
          error: (response: HttpErrorResponse) => {
            const backendErrors = response.error as BackendFormErrors;
            let isErrorHandled = false;
            if (backendErrors?.form?.errors?.children) {
              for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
                // User with the same email is already registered
                if (errorKey === 'email' && !!value.errors) {
                  this.formGroup.get('email')?.setErrors({ emailInUse: true });
                  isErrorHandled = true;
                }
                // User with the same username is already registered
                if (errorKey === 'userName' && !!value.errors) {
                  if (value.errors.find((error) => error?.includes('100'))) {
                    this.formGroup.get('username')?.setErrors({ usernameMaxLength: true });
                  } else {
                    this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                  }
                  isErrorHandled = true;
                }
              }
            }
            if (!isErrorHandled) {
              this.error = 'Ismeretlen hiba!';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  registerWithSocialProvider(provider: SocialProvider): void {
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Regisztráció');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  private saveArticleUrlIntoStorage(): void {
    const redirectUrl = this.route.snapshot.queryParamMap.get('redirect');

    if (redirectUrl) {
      this.storageService.setLocalStorageData('loginRedirectUrl', redirectUrl);
    }
  }

  resendVerification(): void {
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;
    this.timerRestartTrigger$.next();
    this.apiService
      .resendVerificationEmail(this.formGroup.get('email')?.value)
      .pipe(
        finalize(() => (this.isLoading = false)),
        catchError((err) => {
          console.error(err);
          this.error = 'Hiba történt az email újraküldése során!';
          this.cdr.detectChanges();
          return throwError(() => err);
        })
      )
      .subscribe(() => {
        this.isEmailResent = true;
        this.cdr.detectChanges();
      });
  }
}
