import { ArticleCard } from '@trendency/kesma-ui';

export interface ZoeOttBox {
  createdAt: Date | string;
  dataOrigLapId: string;
  imagePath: string;
  imagePathOriginal: string;
  isAdult: string;
  lead: string;
  ottboxCreteadTime: Date | string;
  position: string;
  rssContentId: string;
  salesElementType: string;
  siteName: string;
  spr: string;
  title: string;
  url: string;
  urlOriginalnal: string;
}

export const mapBackendZoeOttBoxToArticleCard = (data: ZoeOttBox): ArticleCard =>
  data && {
    id: data.position, // Sorting prop
    title: data.title,
    url: data.url,
    lead: data.lead,
    thumbnail: {
      url: data.imagePath ?? '',
    },
    isAdultsOnly: !!+data.isAdult,
    articleMedium: data.siteName,
  };
