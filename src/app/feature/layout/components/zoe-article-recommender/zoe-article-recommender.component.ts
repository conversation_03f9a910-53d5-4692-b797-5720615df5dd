import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResult, ArticleCard } from '@trendency/kesma-ui';
import { mapBackendZoeOttBoxToArticleCard, ZoeOttBox } from './zoe-article-recommender.definitions';
import { AsyncPipe, NgForOf } from '@angular/common';
import { ArticleCardType, ManArticleCardComponent } from '../../../../shared';

@Component({
  selector: 'app-zoe-article-recommender',
  templateUrl: './zoe-article-recommender.component.html',
  styleUrls: ['./zoe-article-recommender.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [NgForOf, AsyncPipe, ManArticleCardComponent],
})
export class ZoeArticleRecommenderComponent implements OnInit {
  public readonly ArticleCardType = ArticleCardType;
  articles$: Observable<ArticleCard[]>;
  ARTICLES_COUNT_LIMIT = 8;

  constructor(private readonly reqService: ReqService) {}

  ngOnInit(): void {
    this.getData();
  }

  getData(): void {
    this.articles$ = this.reqService.get<ApiResult<ZoeOttBox[]>>('mediaworks/ottbox-top').pipe(
      map((res: ApiResult<ZoeOttBox[]>) =>
        res.data
          .map((data: ZoeOttBox) => mapBackendZoeOttBoxToArticleCard(data))
          .map((data) => ({
            ...data,
            lead: data.lead?.trim().replace('[...]', '').replace('Bővebben!', ''),
          }))
          .sort((a, b) => Number(a.id) - Number(b.id))
          .slice(0, this.ARTICLES_COUNT_LIMIT)
      )
    );
  }
}
