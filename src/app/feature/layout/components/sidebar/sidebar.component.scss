@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: flex-start;
  height: 100%;
  ::ng-deep {
    man-article-card {
      max-width: none !important;

      .article-card-figure-image {
        width: 100%;
      }
    }
    .sidebar-main {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      justify-content: flex-start;
      height: 100%;
      app-layout,
      kesma-layout {
        height: 100%;
      }
      kesma-layout > .row-element {
        height: 100%;
        .column-element > .content-element:last-child {
          @include stickyLayoutElement();
        }
      }
    }
  }
}
