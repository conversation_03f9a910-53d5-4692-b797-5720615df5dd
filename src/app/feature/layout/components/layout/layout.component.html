<kesma-layout
  [blockTitleRef]="blockTitles"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [contentComponentsRef]="contentComponents"
  [layoutType]="layoutType"
  [structure]="structure"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement" let-layoutType="layoutType">
  <man-block-title-row *ngIf="layoutType !== LayoutPageType.SIDEBAR; else sidebarTitle" [data]="layoutElement.blockTitle"> </man-block-title-row>
  <ng-template #sidebarTitle>
    <man-block-title-sidebar [data]="layoutElement.blockTitle"></man-block-title-sidebar>
  </ng-template>
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-extractor="extractor" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
      <app-mandiner-branding-box-ex [brand]="layoutElement.brand" [desktopWidth]="desktopWidth"></app-mandiner-branding-box-ex>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <man-article-card
        (socialInteraction)="onSocialInteraction($event)"
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [fontSize]="data.fontSize"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
        [isLive]="layoutElement.isLive || layoutElement.live"
        [isMplus]="data.isPaywalled"
        [styleID]="layoutElement.styleId"
      >
      </man-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking">
      <man-breaking-strip
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageType.SIDEBAR"
        [desktopWidth]="desktopWidth"
        [type]="BreakingType.Default"
      >
      </man-breaking-strip>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DossierList">
      <man-dossier-list *ngIf="layoutElement.extractorData as data" [data]="data"> </man-dossier-list>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad">
      <div id="Mandiner_egyedi_normal_content_nyito"></div>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Gallery && index === 0">
      <man-gallery-list *ngIf="layoutElement.extractorData as data" [data]="data"></man-gallery-list>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FastNews">
      <man-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [fontSize]="layoutElement.fontSize"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
        [isMplus]="data.isPaywalled"
        [styleID]="layoutElement.styleId"
      ></man-article-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.WeeklyNewspaperBox && index === 0">
      <mandiner-man-weekly-newspaper
        (socialInteraction)="onSocialInteraction($event)"
        *ngIf="layoutElement.extractorData as data"
        [data]="data"
        [journalData]="layoutElement?.config?.selectedJournal"
        [styleIDs]="layoutElement?.previewImages"
      >
      </mandiner-man-weekly-newspaper>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NEWS_FEED">
      <man-news-feed *ngIf="layoutElement.extractorData?.[index] as data" [data]="data"></man-news-feed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NewsletterBlock">
      <man-newsletter-diverter-card (subscribeClicked)="onSubscribeClicked()" [styleID]="NewsletterDiverterCardType.Layout"> </man-newsletter-diverter-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MANUAL_ARTICLE">
      <man-article-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [caption]="layoutElement.caption"
        [class.has-related-article]="layoutElement?.config?.selectedArticles?.length > 1"
        [data]="data"
        [fontSize]="layoutElement.fontSize"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
        [highlight]="layoutElement.highlight"
        [isLive]="layoutElement.live"
        [isManual]="true"
        [isMplus]="data.isPaywalled"
        [italic]="layoutElement.italic"
        [styleID]="layoutElement.styleId"
      >
      </man-article-card>

      <ng-container *ngFor="let relatedArticle of layoutElement.extractorData | slice: 1; last as last">
        <man-article-card
          *ngIf="relatedArticle"
          [class.last]="last"
          [data]="relatedArticle"
          [fontSize]="layoutElement.fontSize"
          [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
          [isManual]="true"
          [styleID]="MANUAL_ARTICLE_RELATED_ARTICLE_STYLE"
          class="manual-article-related"
        >
        </man-article-card>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MANUAL_OPINION">
      <man-opinion-card
        *ngIf="layoutElement.extractorData?.[index] as data"
        [caption]="layoutElement.caption"
        [data]="data"
        [desktopFooter]="layoutElement.desktopFooter"
        [desktopHeader]="layoutElement.desktopHeader"
        [fontSize]="layoutElement.fontSize"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
        [highlight]="layoutElement.highlight"
        [italic]="layoutElement.italic"
        [mobileFooter]="layoutElement.mobileFooter"
        [mobileHeader]="layoutElement.mobileHeader"
        [styleID]="layoutElement.styleId"
      >
      </man-opinion-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock">
      <man-sponsored-content *ngIf="layoutElement.extractorData as data" [blockTitle]="layoutElement?.config?.title" [data]="data"> </man-sponsored-content>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CategoryStepper && index === 0">
      <man-category-stepper
        (socialInteraction)="onSocialInteraction($event)"
        *ngIf="layoutElement.extractorData as data"
        [data]="data.content"
        [timeToNextSlide]="data.interval"
      >
      </man-category-stepper>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <man-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      }
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TrendingTagsBlock">
      <man-trending-tags *ngIf="layoutElement?.config?.trendingTagsMain as data" [data]="data"></man-trending-tags>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Opinion">
      <ng-container *ngIf="layoutElement.extractorData?.[0] && layoutElement.showHeader && index === 0">
        <man-opinion-header [styleID]="layoutElement.styleId"></man-opinion-header>
      </ng-container>
      <man-opinion-card
        (socialInteraction)="onSocialInteraction($event)"
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [fontSize]="layoutElement.fontSize"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
        [styleID]="layoutElement.styleId"
      >
      </man-opinion-card>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FreshNews">
      <app-fresh-news-adapter [autoFill]="layoutElement?.config?.autoFill"></app-fresh-news-adapter>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.OpinionList">
      <man-opinion-layout
        *ngIf="getOpinionListData() | async as data"
        [data]="data"
        [hideDate]="layoutType === LayoutPageType.HOME || layoutType === LayoutPageType.COLUMN"
      >
      </man-opinion-layout>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
      <man-wysiwyg-box *ngIf="layoutElement.extractorData as data" [htmlArray]="data"></man-wysiwyg-box>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed && index === 0">
      <kesma-html-embed *ngIf="layoutElement?.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [utmSource]="layoutElement.config.utmSource"
      >
      </kesma-real-estate-bazaar-search-block>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Quiz">
      <mandiner-man-quiz *ngIf="layoutElement.extractorData as data" [data]="data"></mandiner-man-quiz>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DossierRepeater">
      <man-dossier-repeater *ngIf="layoutElement.extractorData as data">
        <man-dossier-repeater-card *ngFor="let dossier of data" [data]="dossier"></man-dossier-repeater-card>
      </man-dossier-repeater>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="onRealEstateInit()"
      [data]="realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    >
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PdfBox">
    <man-pdf-box *ngIf="layoutElement.extractorData as data" [data]="data"> </man-pdf-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block (initEvent)="onRealEstateInit()" [data]="realEstateData" [itemsToShow]="1" [showHeader]="true">
    </kesma-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT && index === 0">
    <mandiner-man-video-list (socialInteraction)="onSocialInteraction($event)" *ngIf="layoutElement.extractorData as data" [data]="data">
    </mandiner-man-video-list>
  </ng-container>
</ng-template>
