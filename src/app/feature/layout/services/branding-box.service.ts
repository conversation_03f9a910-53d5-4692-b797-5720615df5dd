import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ApiResult, BackendExternalFeedData, BrandingBoxArticle, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { CleanHttpService, mapTrafficDeflectorArticlesToBrandingBoxArticle } from '../../../shared';

const NUMBERS_OF_ARTICLE = 7;

@Injectable({
  providedIn: 'root',
})
export class BrandingBoxService {
  constructor(
    private readonly httpService: CleanHttpService,
    private readonly reqService: ReqService
  ) {}

  get deflectorApiUrl(): string {
    return environment.type === 'beta' ? 'https://terelo.app.content.private/api' : (environment.personalizedRecommendationApiUrl as string);
  }

  getTrafficDeflectorData(traffickingPlatforms: string, articleLimit: number): Observable<BrandingBoxArticle[] | undefined> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource: 'mandiner.hu',
          withoutPos: '1',
        },
      })
      .pipe(
        map((data) => data?.[traffickingPlatforms]?.map(mapTrafficDeflectorArticlesToBrandingBoxArticle)?.slice(0, articleLimit)),
        catchError(() => {
          return of(undefined);
        })
      );
  }

  public fetchExternalFeedData(externalUrl: string, numbersOfArticle: number = NUMBERS_OF_ARTICLE): Observable<BrandingBoxArticle[]> {
    return this.reqService.get<ApiResult<BackendExternalFeedData>>(`/external-rss-feed?url=${externalUrl}`).pipe(
      map(({ data: { items } }) => {
        return items
          ? items
              .map((item) => ({
                ...item,
                thumbnail: item?.imageUrl,
                lead: item?.description,
                url: item?.link,
              }))
              .slice(0, numbersOfArticle)
          : [];
      })
    );
  }
}
