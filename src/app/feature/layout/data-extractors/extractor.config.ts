import { ArticleArrayExtractor, DataExtractor, DossierExtractor, LayoutElementContentType, OpinionWithMultipleAuthorsExtractor } from '@trendency/kesma-ui';
import { CategoryStepperExtractor } from './category-stepper-extractor';
import { PdfBoxExtractor } from './pdf-box-extractor';

export const MANDINER_EXTRACTORS_CONFIG: DataExtractor<unknown>[] = [
  {
    extractor: CategoryStepperExtractor,
    supportedContentTypes: [LayoutElementContentType.CategoryStepper],
  },
  {
    extractor: PdfBoxExtractor,
    supportedContentTypes: [LayoutElementContentType.PdfBox],
  },
  {
    extractor: ArticleArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.WeeklyNewspaperBox, LayoutElementContentType.MANUAL_ARTICLE],
  },
  {
    extractor: OpinionWithMultipleAuthorsExtractor,
    supportedContentTypes: [LayoutElementContentType.MANUAL_OPINION],
  },
  {
    extractor: CategoryStepperExtractor,
    supportedContentTypes: [LayoutElementContentType.CategoryStepper],
  },
  {
    extractor: DossierExtractor,
    supportedContentTypes: [LayoutElementContentType.DossierRepeater],
  },
  {
    extractor: PdfBoxExtractor,
    supportedContentTypes: [LayoutElementContentType.PdfBox],
  },
];
