import { Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService, LayoutElementContent, LayoutElementContentConfigurationPdfBox } from '@trendency/kesma-ui';
import { PdfBoxDefinitions } from 'src/app/shared/components/pdf-box/man-pdf-box.definitions';

@Injectable()
export class PdfBoxExtractor implements LayoutDataExtractorService<PdfBoxDefinitions | undefined> {
  extractData: DataExtractorFunction<PdfBoxDefinitions | undefined> = (element: LayoutElementContent) => {
    const config = element.config as LayoutElementContentConfigurationPdfBox;

    if (!config) {
      return undefined;
    }

    return {
      data: {
        boxTitle: config.boxTitle,
        title: config.title,
        lead: config.lead,
        btnUrl: config.btnUrl,
        target: config.target,
      },
      meta: {
        extractedBy: PdfBoxExtractor.name,
      },
    };
  };
}
