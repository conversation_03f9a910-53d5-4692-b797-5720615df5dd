import { Injectable } from '@angular/core';
import {
  ArticleCard,
  buildArticleUrl,
  CategoryStepperInput,
  DataExtractorFunction,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationCategoryStepper,
} from '@trendency/kesma-ui';

type CategoryStepper = { content: CategoryStepperInput[]; interval: number };

const DEFAULT_DATA = { data: { content: [], interval: 999_999 } };

@Injectable()
export class CategoryStepperExtractor implements LayoutDataExtractorService<CategoryStepper | undefined> {
  extractData: DataExtractorFunction<CategoryStepper | undefined> = (element: LayoutElementContent) => {
    const config = element.config as LayoutElementContentConfigurationCategoryStepper;

    if (!config) {
      return DEFAULT_DATA;
    }

    const { selectedColumns, tabs, interval } = config;

    if (!selectedColumns?.length || !tabs?.length) {
      return DEFAULT_DATA;
    }

    const articles = tabs.flatMap((tab) => {
      const filtered = tab?.selectedArticles?.filter((article) => !!article?.data);
      return filtered.map((article) => article?.data as unknown as ArticleCard);
    });

    const content = selectedColumns.map((columnConf) => {
      return {
        column: {
          id: columnConf.id,
          title: columnConf.title,
        },
        selectedArticles: articles
          .filter((article) => article.columnTitle === columnConf.title)
          .map((article) => ({
            ...article,
            thumbnail: {
              url: article.thumbnailUrl ?? '',
            },
            link: buildArticleUrl(article),
          })),
      };
    });

    return {
      data: { interval, content },
      meta: {
        extractedBy: CategoryStepperExtractor.name,
      },
    };
  };
}
