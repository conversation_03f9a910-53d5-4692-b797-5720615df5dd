import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { ApiListResult, OlimpiaHungarianTeam } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class OlympicsHungarianTeamService {
  constructor(private readonly reqService: ReqService) {}

  getOlympicParticipants(year: number, page = 0, rowCount_limit = 20, countryCode_filter?: string): Observable<ApiListResult<OlimpiaHungarianTeam>> {
    let { params }: IHttpOptions = {
      params: {
        page_limit: page.toString(),
        rowCount_limit: rowCount_limit.toString(),
      },
    };
    params = countryCode_filter ? { ...params, countryCode_filter } : params;

    return this.reqService.get<any>(`/olympics/participants/${year}`, { params }).pipe(
      map(({ data, meta }) => ({
        meta,
        data: data.map((participant: OlimpiaHungarianTeam) => ({
          ...participant,
          avatar: {
            thumbnailUrl: participant?.avatar?.thumbnailUrl ?? '/assets/images/logo-small.svg',
          },
        })),
      }))
    );
  }
}
