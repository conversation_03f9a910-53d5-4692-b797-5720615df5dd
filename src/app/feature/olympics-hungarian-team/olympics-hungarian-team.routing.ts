import { Routes } from '@angular/router';
import { PagerValidatorGuard } from 'src/app/shared/guards/pager-validator.guard';
import { OlympicsHungarianTeamComponent } from './components/olympics-hungarian-team/olympics-hungarian-team.component';
import { olympicsParticipantsResolver } from './resolvers/olympics-hungarian-team.resolver';

export const OlympicsHungarianTeamRoutes: Routes = [
  {
    path: '',
    component: OlympicsHungarianTeamComponent,
    resolve: { data: olympicsParticipantsResolver },
    canActivate: [PagerValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
  },
];
