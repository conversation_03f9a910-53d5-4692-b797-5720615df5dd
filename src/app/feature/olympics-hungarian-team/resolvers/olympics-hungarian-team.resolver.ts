import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { Layout } from '@trendency/kesma-ui';
import { forkJoin, Observable, take } from 'rxjs';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';
import { OlympicsHungarianTeamService } from '../services/olympics-hungarian-team.service';

// eslint-disable-next-line max-len
export const olympicsParticipantsResolver: ResolveFn<Observable<{ olympicsParticipants: any; olympicImportantSidebar: Layout | null }>> = (route) => {
  const YEAR = 2024;
  const MAXRESULTSPERPAGE = 20;
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
  const COUNTRYCODE = 'HUN';

  return forkJoin({
    olympicsParticipants: inject(OlympicsHungarianTeamService).getOlympicParticipants(YEAR, currentPage, MAXRESULTSPERPAGE, COUNTRYCODE),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
};
