<section>
  <div class="wrapper with-aside">
    <div class="left-column" *ngIf="paginator.data$ | async as data">
      <kesma-olimpia-page-banner [styleID]="OlimpicPortalEnum.OlimpicMANDINER"></kesma-olimpia-page-banner>
      <h1 class="table-title">Magyar olimpiai csapat</h1>
      <div class="participants">
        @for (participant of data; track participant.slug) {
          <kesma-olimpia-participant [styleID]="OlimpicPortalEnum.OlimpicMANDINER" [data]="participant"></kesma-olimpia-participant>
        }
      </div>
      <man-simple-button class="load-more-btn" [wide]="false" (click)="next()" color="primary" [disabled]="(paginator.hasMore$ | async) === false"
        >Mutass többet</man-simple-button
      >
      <kesma-olimpia-navigator [navigationLink]="['/', 'olimpia-2024']" [styleID]="OlimpicPortalEnum.OlimpicMANDINER"> </kesma-olimpia-navigator>

      <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
      ></app-layout>
    </aside>
  </div>
</section>
