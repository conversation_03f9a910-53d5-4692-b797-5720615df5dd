import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Layout,
  LayoutPageType,
  LoadMorePaginator,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpiaParticipantComponent,
  OlimpicPortalEnum,
} from '@trendency/kesma-ui';
import { map, Observable, Subject } from 'rxjs';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { ManSimpleButtonComponent } from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { OlympicsHungarianTeamService } from '../../services/olympics-hungarian-team.service';

@Component({
  selector: 'app-olympics-hungarian-team',
  templateUrl: './olympics-hungarian-team.component.html',
  styleUrl: './olympics-hungarian-team.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AsyncPipe,
    ManSimpleButtonComponent,
    OlimpiaNavigatorComponent,
    LayoutComponent,
    OlimpiaPageBannerComponent,
    OlimpiaParticipantComponent,
    StrossleAdvertComponent,
  ],
})
export class OlympicsHungarianTeamComponent {
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;
  readonly unsubscribe$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,

    private readonly olympicsHungarianTeamService: OlympicsHungarianTeamService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));

  readonly paginator = new LoadMorePaginator(
    (page) => this.olympicsHungarianTeamService.getOlympicParticipants(2024, page, 20, 'HUN'),
    this.route.data.pipe(map(({ data }) => data['olympicsParticipants']))
  );

  next(): void {
    this.paginator.next();
  }
}
