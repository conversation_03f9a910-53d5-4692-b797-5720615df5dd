@use 'shared' as *;

.olympic-important-sidebar {
  ::ng-deep {
    .layout-element {
      margin-bottom: 0 !important;
    }
  }
}

.left-column {
  row-gap: 24px;

  .table-title {
    line-height: 40px;
    margin: 18px 0;
    color: var(--kui-orange-600);
    font-family: var(--kui-font-primary);
  }

  .participants {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 16px;

    @include media-breakpoint-down(lg) {
      row-gap: 8px;
      grid-template-columns: repeat(1, 1fr);
    }
  }

  kesma-olimpia-page-banner {
    width: 100%;
  }
}

mno-pager {
  margin-bottom: 24px;
}

kesma-olimpia-navigator {
  margin-top: 16px;
  margin-bottom: 24px;
}

.load-more-btn {
  margin-top: 24px;

  @include media-breakpoint-down(md) {
    margin-top: 37px;
    width: 100%;
  }
}
