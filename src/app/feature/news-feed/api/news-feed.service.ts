import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { forkJoin, Observable, of, switchMap, throwError } from 'rxjs';
import { ApiResult, Article, ArticleCard, ArticleSearchResult, buildArticleUrl, Tag } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { SecureApiService } from '../../../shared/services/secure-api.service';
import { AuthService } from '../../../shared/services/auth.service';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { NewsFeedApiResponseMeta } from '../news-feed.definitions';
import { ApiResponseMetaList } from '@trendency/kesma-ui/lib/definitions/api-result';
import { searchResultToArticleCard } from '../../tags-page/tags-page.utils';
import { TagsPageService } from '../../tags-page/tags-page.service';

@Injectable({
  providedIn: 'root',
})
export class NewsFeedService {
  private meta = {};

  constructor(
    private readonly router: Router,
    private readonly reqService: ReqService,
    private readonly secureApi: SecureApiService,
    private readonly authService: AuthService,
    private readonly tagsService: TagsPageService
  ) {}

  getNewsFeed(slug: string, pageLimit = 0): Observable<ApiResult<Article[], NewsFeedApiResponseMeta>> {
    const params: IHttpOptions = {
      params: {
        page_limit: pageLimit.toString(),
      },
    };
    return this.reqService.get<ApiResult<Article[]>>(`/content-group/news-feed/${slug}`, { ...params }).pipe(
      map((result) => {
        if (!result.data.length) {
          this.navigateTo404();
        }
        return result;
      }),
      catchError((error: HttpErrorResponse | Error) => {
        this.navigateTo404(error);
        return throwError(() => error);
      }),
      switchMap((result: ApiResult<Article[]>) => {
        this.meta = result?.meta;
        return forkJoin([...this.makeArticleRequests$(result?.data)]);
      }),
      map((articles: Article[]) => {
        return {
          data: articles,
          meta: this.meta,
        } as ApiResult<Article[], NewsFeedApiResponseMeta>;
      })
    );
  }

  getRelatedArticles(request$: Observable<ApiResult<Article[]>>): Observable<Array<ArticleCard[]>> {
    return request$.pipe(
      switchMap((result: ApiResult<Article[]>) => {
        const relatedArticles$ = [];
        for (const article of result?.data || []) {
          const slugs = article?.tags?.map((tag: Tag) => tag?.slug);
          relatedArticles$.push(this.tagsService.searchArticleByTags(slugs, 0, 9));
        }
        return forkJoin(relatedArticles$);
      }),
      map((relatedArticles) => {
        const articles = relatedArticles as ApiResult<ArticleSearchResult[], ApiResponseMetaList>[];
        return [...articles.map((article) => article?.data.map(searchResultToArticleCard))];
      })
    );
  }

  private makeArticleRequests$(articles: Article[]): Observable<Article>[] {
    const requests$: Observable<Article>[] = [];
    for (const article of articles) {
      article?.isPaywalled ? requests$.push(this.getPaidContent(article)) : requests$.push(of(article));
    }
    return requests$;
  }

  private getPaidContent(article: Article): Observable<Article> {
    const articleLink = buildArticleUrl(article);
    return this.authService.isAuthenticated().pipe(
      switchMap((isAuthenticated: boolean) => {
        const user = this.authService.currentUser;
        if (!isAuthenticated || !user?.hasValidSubscription) {
          return of(article);
        }

        return this.secureApi
          .getArticleBody(
            // articleLink[0] = "/",
            articleLink[1], //columnSlug as string,
            articleLink[2], //year.toString(),
            articleLink[3], //month.toString(),
            articleLink[4] //articleSlug
          )
          .pipe(
            map((result) => {
              article = {
                ...article,
                body: result.data.body,
              };
              return article;
            })
          );
      })
    );
  }

  private navigateTo404(error?: HttpErrorResponse | Error): void {
    this.router
      .navigate(['/', '404'], {
        state: { errorResponse: JSON.stringify(error || {}) },
        skipLocationChange: true,
      })
      .then();
  }
}
