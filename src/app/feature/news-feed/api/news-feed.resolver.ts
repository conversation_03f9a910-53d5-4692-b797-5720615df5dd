import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, Observable, share, switchMap } from 'rxjs';
import { NewsFeedService } from './news-feed.service';
import { ApiResult, Article, ArticleRouteParams } from '@trendency/kesma-ui';
import { NewsFeedApiResponse } from '../news-feed.definitions';
import { ArticleService } from '../../../shared';

@Injectable()
export class NewsFeedResolver {
  constructor(
    private readonly newsFeedService: NewsFeedService,
    private readonly articleService: ArticleService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<NewsFeedApiResponse> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;
    const slug = params.articleSlug;
    const newsFeed$ = this.newsFeedService.getNewsFeed(slug).pipe(share());

    return forkJoin({
      result: newsFeed$,
      recommendations: newsFeed$.pipe(
        switchMap((result: ApiResult<Article[]>) => {
          const latestArticleSlug = result?.data?.[0]?.slug;
          return this.articleService.getArticleRecommendations(latestArticleSlug as string);
        })
      ),
      // relatedArticles: this.newsFeedService.getRelatedArticles(newsFeed$),
    });
  }
}
