<ng-container *ngIf="!isUserAdultChoice && isAdultsOnly; else adultContent" class="article-page">
  <man-adult (isUserAdult)="onIsUserAdultChoose($event)"></man-adult>
</ng-container>

<ng-template #adultContent>
  <section>
    <div class="wrapper">
      <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems"></man-breadcrumb>
      <div class="dossier">
        <div class="left-column">
          <a [routerLink]="getDossierLink()">
            <h1 class="dossier-title">{{ dossier?.title }}</h1>
          </a>
          <div class="dossier-lead">{{ dossier?.description }}</div>
          <div class="dossier-date" *ngIf="dossier?.createdAt as date">
            {{ stringToDate(date) | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
          </div>
        </div>
        <figure>
          <a [routerLink]="getDossierLink()">
            <img class="dossier-thumbnail" [src]="dossier?.coverImage?.thumbnail || '/assets/images/placeholder-1-1.svg'" [alt]="dossier?.description" />
          </a>
          <figcaption>
            <div class="dossier-caption">{{ dossier?.coverImage?.caption }}</div>
            <div class="dossier-source">{{ dossier?.coverImage?.source }}</div>
          </figcaption>
        </figure>
      </div>
    </div>
    <div class="wrapper with-aside">
      <div class="left-column">
        <ng-container *ngFor="let article of articles; let index = index">
          <a [routerLink]="getArticleLink(article)">
            <h2 class="title">{{ article?.title }}</h2>
          </a>
          <div class="lead">{{ article?.lead }}</div>
          <ng-container [ngTemplateOutlet]="bodyContent" [ngTemplateOutletContext]="{ article: article }"> </ng-container>

          <div class="meta">
            <div class="meta-date">
              {{ stringToDate(article.publishDate) | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
            </div>
            <mandiner-social-share-modal
              [isShowShareText]="true"
              [title]="article.title"
              (socialInteraction)="onSocialInteraction($event)"
              [link]="getArticleLink(article)"
            >
            </mandiner-social-share-modal>
          </div>
          <div class="divider"></div>
        </ng-container>

        <ng-container *ngIf="isLoading; else loadMoreTemplate">
          <man-spinner></man-spinner>
        </ng-container>

        <ng-template #loadMoreTemplate>
          <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()">
            <strong>Mutass többet</strong>
          </man-simple-button>
        </ng-template>
      </div>
      <aside>
        <div class="news-feed">
          <div class="news-feed-title">{{ dossier?.newsFeedTitle }}</div>
          <div class="news-feed-lead">
            {{ dossier?.newsFeedDescription }}
            <div class="news-feed-share">
              <mandiner-social-share-modal
                [isShowShareText]="true"
                (socialInteraction)="onSocialInteraction($event)"
                [title]="$any(dossier?.title)"
                [link]="getDossierLink(true)"
              >
              </mandiner-social-share-modal>
            </div>
          </div>
        </div>
      </aside>
    </div>
    <div class="wrapper">
      <man-block-title-small [data]="{ text: 'Ez is érdekelheti' }"></man-block-title-small>
      <div class="recommendation-block recommendation-block-external">
        <man-article-card
          *ngFor="let article of externalRecommendation"
          [styleID]="ArticleCardType.ExternalRecommendation"
          (socialInteraction)="onSocialInteraction($event)"
          [data]="article"
        >
        </man-article-card>
      </div>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-article="article">
  <ng-container *ngFor="let element of article?.body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <man-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></man-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div class="voting-block" *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <man-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [styleID]="votingStyle.GRAY" [voteId]="voteData.votedId" />
        }
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <app-article-dossier-recommender
          *ngIf="element?.details[0]?.value?.isActive && element?.details[0]?.value?.relatedArticles?.length"
          [excludedArticleSlug]="article?.slug"
          [subsequentDossier]="element?.details[0]?.value"
          class="dossier-recommender-wrapper"
        >
        </app-article-dossier-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <mandiner-man-quiz [data]="element?.details[0]?.value"></mandiner-man-quiz>
      </ng-container>

      <div class="block-video" *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div class="block-gallery" *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id] as gallery">
          <man-gallery-card [data]="gallery" [routerLink]="['/galeria', gallery?.slug]"></man-gallery-card>
        </ng-container>
      </div>

      <div class="recommendation-block" *ngSwitchCase="ArticleBodyType.Article">
        <ng-container *ngIf="element?.details[0]?.value?.isOpinion; else articleCard">
          <div class="opinion-card-wrapper">
            <p class="opinion-card-title">Kapcsolódó vélemény</p>
            <man-opinion-card
              class="opinion-card"
              [data]="element?.details[0]?.value"
              (socialInteraction)="onSocialInteraction($event)"
              [styleID]="ManOpinionCardType.NO_BACKGROUND_NO_DATE_META"
            >
            </man-opinion-card>
          </div>
        </ng-container>

        <ng-template #articleCard>
          <man-article-card [styleID]="ArticleCardType.ImgRightTitleLead" (socialInteraction)="onSocialInteraction($event)" [data]="element?.details[0]?.value">
          </man-article-card>
        </ng-template>
      </div>

      <div class="wrapper" *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendation">
        <man-block-title-small [data]="{ text: 'Kapcsolódó cikkek' }" [isFullWidth]="true"></man-block-title-small>
        <div id="related-articles" class="double-recommendations">
          <man-article-card
            *ngFor="let article of doubleArticleRecommendations(element.details); let i = index"
            [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
            [data]="doubleArticleRecommendation(article.value)"
            (socialInteraction)="onSocialInteraction($event)"
            [isCategoryVisible]="true"
            [isMaxWidth]="true"
          >
          </man-article-card>
        </div>
      </div>
    </ng-container>
  </ng-container>
</ng-template>
