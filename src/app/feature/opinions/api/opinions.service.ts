import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';

import { OpinionTypeParams, OpinionTypeResponse } from './opinions.definitions';

@Injectable({
  providedIn: 'root',
})
export class OpinionsService {
  constructor(private readonly reqService: ReqService) {}

  public getOpinions(
    page = 0,
    itemsPerPage = 20,
    author?: string,
    orderByAsc?: boolean,
    searchQuery?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    material_types_only?: string
  ): Observable<OpinionTypeResponse> {
    let params: OpinionTypeParams = {
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      global_filter: searchQuery || '',
      'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    } as OpinionTypeParams;

    params = author ? { ...params, author: author } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;

    return this.reqService.get(`content-page/articles-by-opinion-type`, { params });
  }

  public getAuthors(searchTerm?: string): Observable<any> {
    const params: any = {};
    if (searchTerm) {
      params.global_filter = searchTerm;
    }

    return this.reqService.get(`public-authors/source`, { params });
  }
}
