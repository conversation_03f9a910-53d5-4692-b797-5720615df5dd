import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import { OpinionTypeResolverResponse } from './opinions.definitions';
import { OpinionsService } from './opinions.service';

@Injectable()
export class OpinionsResolver {
  constructor(
    private readonly opinionsService: OpinionsService,
    private readonly router: Router,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<OpinionTypeResolverResponse> {
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const author = route.queryParams['author'];
    const maxResultsPerPage = 20;

    return forkJoin({
      opinions: this.opinionsService.getOpinions(currentPage, maxResultsPerPage, author, false).pipe(
        map((res) => {
          if (currentPage > 0 && res.data.length === 0) {
            throw new Error('No more items');
          }
          return res;
        }),
        catchError((err) => {
          this.router
            .navigate(['/', '404'], {
              skipLocationChange: true,
            })
            .then();
          return throwError(err);
        })
      ),
      searchFilterData: this.searchFilterDataService.getSearchFilterColoumns(),
    });
  }
}
