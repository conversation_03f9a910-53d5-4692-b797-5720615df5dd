import { ArticleCard } from '@trendency/kesma-ui';
import { OpinionTypeArticleData } from './opinions.definitions';

export const backendOpinionToArticleCard = ({
  id,
  title,
  slug,
  publishDate,
  columnSlug,
  author,
  recommendedTitle,
  chExcerpt,
  chLead,
  avatarImage,
  articleSource,
  articleMedium,
  likeCount,
  dislikeCount,
  commentCount,
  isLikesAndDislikesDisabled,
  isCommentsDisabled,
}: OpinionTypeArticleData): ArticleCard => {
  const [publishYear, publishMonth] = publishDate?.split('-') || '';
  return {
    id,
    articleSource: articleSource ? articleSource : '',
    articleMedium: articleMedium ? articleMedium : '',
    title: recommendedTitle ? recommendedTitle : title,
    slug,
    category: {
      slug: columnSlug,
      name: author,
    },
    publishDate,
    publishYear,
    publishMonth,
    columnSlug,
    lead: chExcerpt ? chExcerpt : chLead,
    thumbnail: {
      url: avatarImage ?? '',
    },
    author: {
      name: author,
      avatarUrl: avatarImage,
    },
    likeCount,
    dislikeCount,
    commentCount,
    isLikesAndDislikesDisabled,
    isCommentsDisabled,
  };
};
