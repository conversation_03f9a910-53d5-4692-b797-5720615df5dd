@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  man-search-filter {
    max-width: none;
    margin-bottom: 50px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
      padding-bottom: 10px;
    }
  }

  hr {
    height: 1px;
    background-color: var(--kui-gray-100);
    border: none;
    margin: 10px 0;
  }

  .opinion-result-counter {
    margin: 20px 0;
  }

  .title {
    margin: 10px 0;
    color: var(--kui-orange-600);
  }

  .lead {
    font-family: var(--kui-font-secondary);
    line-height: 30px;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 24px;
  }

  .article {
    &-date {
      margin-bottom: 20px;

      strong {
        font-weight: 800;
      }
    }

    &-wrapper {
      display: flex;
      justify-content: center;
    }

    &-card {
      width: 996px;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--kui-gray-50);
    }
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
