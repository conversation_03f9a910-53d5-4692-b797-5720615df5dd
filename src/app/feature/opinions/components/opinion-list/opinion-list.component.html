<section>
  <div class="wrapper">
    <man-breadcrumb [items]="[{ label: 'Vélemények' }]"></man-breadcrumb>

    <h2 class="title">Vélemények</h2>
    <hr />
    <p class="lead">A Mandiner szemlerovata a nyilvánosságban megjelenő legújabb és legfontosabb véleménycikkekről, publicisztikákról.</p>
    <hr />

    <div class="opinion-result-counter">
      <strong>{{ rowAllCount }} db</strong> vélemény összesen
    </div>

    <man-search-filter [data]="(searchFilterColumns$ | async) || undefined" (filterEvent)="onFilter($event)" [showSearchHeader]="false" [filterOnSide]="true">
    </man-search-filter>

    <ng-container *ngFor="let opinion of articleList; index as index; first as first">
      <ng-container *ngIf="opinion?.publishDate as date">
        <div class="article-date" *ngIf="isEqualsCurrentDateWithPreviousDate(index)">
          <strong>{{ date | manTranslatedDatePipe }}&nbsp;</strong>
          <span>{{ date | manTranslatedDatePipe: '(EEEE)' }}</span>
        </div>
      </ng-container>

      <div class="article-wrapper">
        <man-opinion-card class="article-card" [fontSize]="24" [socialInteractions]="getSocialData(opinion)" [data]="opinion"></man-opinion-card>
      </div>

      <div *ngIf="index === 3">
        <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
      </div>

      <div *ngIf="index === 7">
        <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
      </div>

      <div *ngIf="index === 11">
        <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
      </div>

      <div *ngIf="index === 15">
        <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
      </div>
    </ng-container>

    <man-simple-button *ngIf="canLoadMore" (click)="loadMoreOpinion()">
      <strong>Mutass többet</strong>
    </man-simple-button>
  </div>
</section>
