import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orO<PERSON>, NgI<PERSON> } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { ArticleAuthor, ArticleCard, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { finalize, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ApiService,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  LikeButtons,
  ManBreadcrumbComponent,
  ManOpinionCardComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
  TranslatedDatePipe,
} from '../../../../shared';
import { articlesPageSize } from '../../../category/category.definitions';
import { OpinionTypeArticleData, PublicAuthorData } from '../../api/opinions.definitions';
import { OpinionsService } from '../../api/opinions.service';
import { backendOpinionToArticleCard } from '../../api/opinions.utils';

const DEFAULT_PAGE_TITLE = 'Vélemények';

@Component({
  selector: 'app-opinion-list',
  templateUrl: './opinion-list.component.html',
  styleUrls: ['./opinion-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    ManSearchFilterComponent,
    AsyncPipe,
    NgForOf,
    NgIf,
    ManOpinionCardComponent,
    ManSimpleButtonComponent,
    TranslatedDatePipe,
    StrossleAdvertComponent,
  ],
})
export class OpinionListComponent implements OnInit {
  authorList: any[];
  rowAllCount = 0;
  articleList: ArticleCard[] = [];
  filterValues: FilterValues = {};
  titleText = DEFAULT_PAGE_TITLE;
  author: ArticleAuthor | null;

  private currentPage = 1;

  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(
    map((res) => res['opinions']?.['searchFilterData']),
    map((res) => this.mapSearchContentTypes(res))
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly opinionsService: OpinionsService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly apiService: ApiService,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  ngOnInit(): void {
    this.initAuthors();
    this.setMetaData();
    this.initializeAuthor();
    this.subscribeToResolverDataChange();
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.rowAllCount > this.articleList.length;
  }

  getSocialData(opinion: ArticleCard): LikeButtons {
    return {
      areReactionsHidden: opinion.isLikesAndDislikesDisabled,
      areCommentsHidden: opinion.isCommentsDisabled,
    } as LikeButtons;
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.articleList = [];
    this.currentPage = 0;
    this.loadMoreOpinion();
  }

  isOrderByDateAsc(): boolean {
    if (!this.filterValues?.sort?.length) {
      return false;
    }
    const sortFields = this.filterValues.sort.split('_');
    return sortFields[0] === 'date' && sortFields[1] === 'asc';
  }

  loadMoreOpinion(): void {
    const seachDateRange = this.searchFilterDataService.setSearchQuery(this.filterValues);
    this.opinionsService
      .getOpinions(
        this.currentPage,
        articlesPageSize,
        '',
        this.isOrderByDateAsc(),
        this.filterValues?.keyword ?? '',
        seachDateRange.fromDate,
        seachDateRange.toDate,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).contentTypes,
        this.filterValues?.columns ?? [],
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).ownMaterial
      )
      .pipe(finalize(() => this.currentPage++))
      .subscribe((res) => {
        this.rowAllCount = res?.meta?.limitable?.rowAllCount || 0;
        this.articleList = this.articleList.concat(res?.data.map((item: OpinionTypeArticleData) => backendOpinionToArticleCard(item)));
        this.changeDetectorRef.detectChanges();
      });
  }

  isEqualsCurrentDateWithPreviousDate(index: number): boolean {
    const currentDate = (this.articleList?.[index]?.publishDate as string)?.substring(0, 10);
    const previousDate = (this.articleList?.[index - 1]?.publishDate as string)?.substring(0, 10);
    return currentDate !== previousDate;
  }

  initializeAuthor(): void {
    this.route.queryParams.subscribe((params) => {
      this.initializeAuthorData(params['author']);
    });
  }

  initializeAuthorData(author: string): void {
    this.apiService.getAuthorFromPublicAuthor(author).subscribe((res) => {
      this.author = this.mapPublicAuthorDataToArticleAuthor(res.data);
      this.titleText = author && author !== null ? author + ' véleményei' : DEFAULT_PAGE_TITLE;
      this.setMetaData();
      this.cd.detectChanges();
    });
  }

  mapPublicAuthorDataToArticleAuthor(data: PublicAuthorData): ArticleAuthor | null {
    if (!data.publicAuthorName) {
      return null;
    } else {
      return {
        name: data.publicAuthorName,
        description: data.publicAuthorDescription,
        facebook: data.facebook,
        instagram: data.instagram,
        tiktok: data.tiktok,
        avatarUrl: data?.avatar?.fullSizeUrl,
      };
    }
  }

  private initAuthors(): void {
    this.opinionsService.getAuthors().subscribe((res: any) => {
      this.authorList = res?.data;
      this.changeDetectorRef.detectChanges();
    });
  }

  private subscribeToResolverDataChange(): void {
    this.route.data.subscribe((res: any) => {
      this.articleList = res?.opinions?.opinions?.data.map((item: OpinionTypeArticleData) => backendOpinionToArticleCard(item));
      this.rowAllCount = res?.opinions?.opinions?.meta.limitable.rowAllCount;
      this.changeDetectorRef.detectChanges();
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('velemeny');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Vélemények');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description: 'A Mandiner szemlerovata a nyilvánosságban megjelenő legújabb és legfontosabb véleménycikkekről, publicisztikákról.',
      ogDescription: 'A Mandiner szemlerovata a nyilvánosságban megjelenő legújabb és legfontosabb véleménycikkekről, publicisztikákról.',
    });
  }

  private mapSearchContentTypes(filterData: SearchFilterDefinitions): SearchFilterDefinitions {
    const unnecessaryFilters = ['article', 'articleVideo'];
    return {
      ...filterData,
      tipus: filterData.tipus.filter((type) => !unnecessaryFilters.includes(type.slug)),
    };
  }
}
