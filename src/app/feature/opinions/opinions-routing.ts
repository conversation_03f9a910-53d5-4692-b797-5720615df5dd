import { Routes } from '@angular/router';
import { OpinionListComponent } from './components/opinion-list/opinion-list.component';
import { OpinionsResolver } from './api/opinions.resolver';

export const opinionsRouting: Routes = [
  {
    path: '',
    pathMatch: 'full',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: OpinionListComponent,
    resolve: {
      opinions: OpinionsResolver,
    },
    providers: [OpinionsResolver],
  },
];
