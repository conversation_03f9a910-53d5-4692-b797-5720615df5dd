import { Routes } from '@angular/router';
import { PagerValidatorGuard } from '../../../shared/guards/pager-validator.guard';
import { olympicsAthleteProfileResolver } from './resolvers/olympics-athlete-profile.resolver';

export const olympicsAthleteProfileRoutes: Routes = [
  {
    path: 'olimpia-2024/:athleteSlug',
    loadComponent: () =>
      import('./components/olympics-athlete-profile/olympics-athlete-profile.component').then(
        ({ OlympicsAthleteProfileComponent }) => OlympicsAthleteProfileComponent
      ),
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      data: olympicsAthleteProfileResolver,
    },
    canActivate: [PagerValidatorGuard],
  },
];
