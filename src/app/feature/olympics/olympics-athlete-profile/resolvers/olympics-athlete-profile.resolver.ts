import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, map, switchMap, take } from 'rxjs/operators';
import { forkJoin, Observable, share, throwError } from 'rxjs';
import { OlympicsAthleteProfileService } from '../services/olympics-athlete-profile.service';
import { ApiResponseMetaList, ApiResult, BackendArticleSearchResult, Layout, LimitableMeta, OlimpiaHungarianTeam, Tag } from '@trendency/kesma-ui';
import { ReqService } from '@trendency/kesma-core';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';

interface ResolveData {
  profileData: {
    articles: ApiResult<BackendArticleSearchResult[], LimitableMeta>;
    athleteProfile: OlimpiaHungarianTeam;
  };
  olympicImportantSidebar: Layout | null;
}

export const olympicsAthleteProfileResolver: ResolveFn<Observable<ResolveData>> = (route: ActivatedRouteSnapshot) => {
  const athleteSlug = route.params?.['athleteSlug'];
  const router = inject(Router);
  const reqService = inject(ReqService);

  return forkJoin({
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
    profileData: inject(OlympicsAthleteProfileService)
      .getAthleteProfile(athleteSlug)
      .pipe(
        map(({ data }) => data),
        share(),
        switchMap((member: OlimpiaHungarianTeam) => {
          const tagSlugs = member?.tags?.map((tag: Tag) => tag?.slug) || [];

          if (!tagSlugs.includes('olimpia_2024')) {
            tagSlugs.push('olimpia_2024');
          }

          return reqService
            .get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>('/content-page/search', {
              params: {
                page_limit: '0',
                rowCount_limit: '5',
                'tagSlugs[]': tagSlugs,
              },
            })
            .pipe(
              map(({ data, meta }) => ({
                articles: { data, meta: meta.limitable },
                athleteProfile: member,
              }))
            );
        })
      ),
  }).pipe(
    catchError((err) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => err);
    })
  );
};
