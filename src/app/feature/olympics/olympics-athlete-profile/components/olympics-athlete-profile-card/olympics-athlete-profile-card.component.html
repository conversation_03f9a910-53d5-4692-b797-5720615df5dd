<div class="athlete-profile">
  <div class="left-side">
    <img [alt]="data?.avatar?.altText" [src]="data?.avatar?.thumbnailUrl || '/assets/images/logo-small.svg'" class="athlete-profile-img" />
    <h2 class="athlete-profile-name mobile">{{ data?.name }}</h2>
  </div>

  <div class="right-side">
    <h2 class="athlete-profile-name desktop-athlete">{{ data?.name }}</h2>

    @if (data?.sports?.[0] || data?.competitionRaces) {
      <div class="athlete-profile-details">
        <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'sportág:', data: data?.sports?.[0], isHighlighted: true }">
        </ng-container>
        <ng-container
          [ngTemplateOutlet]="detailTmp"
          [ngTemplateOutletContext]="{ label: 'versenyszám:', data: data?.competitionRaces, isHighlighted: true, isLight: true }"
        >
        </ng-container>
      </div>
    }

    @if (data?.age || data?.club || data?.coach) {
      <div class="athlete-profile-details">
        <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'életkor:', data: data?.age }"> </ng-container>
        <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'klub:', data: data?.club }"> </ng-container>
        <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'edző:', data: data?.coach }"> </ng-container>
      </div>
    }

    @if (data?.results) {
      <div class="athlete-profile-details">
        <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'eredmények:', data: data?.results }"> </ng-container>
      </div>
    }
  </div>
</div>

<ng-template #detailTmp let-data="data" let-isHighlighted="isHighlighted" let-isLight="isLight" let-label="label">
  @if (data) {
    <div class="athlete-profile-detail">
      <div class="athlete-profile-detail-label">{{ label }}</div>
      <div class="athlete-profile-detail-value" [class.highlighted]="isHighlighted" [class.light]="isLight">{{ data }}</div>
    </div>
  }
</ng-template>
