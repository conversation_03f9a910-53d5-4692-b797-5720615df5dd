import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, OlimpiaHungarianTeam } from '@trendency/kesma-ui';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-olympics-athlete-profile-card',
  templateUrl: './olympics-athlete-profile-card.component.html',
  styleUrl: './olympics-athlete-profile-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],
})
export class OlympicsAthleteProfileCardComponent extends BaseComponent<OlimpiaHungarianTeam> {}
