@use 'shared' as *;

$olympics-border-color: #74d3cf;
$olympics-label-color: #0c7c84;
$olympics-color: #020617;

:host {
  display: block;
  width: 100%;
  border-bottom: 2px solid $olympics-border-color;

  @include media-breakpoint-down(sm) {
    border: none;
  }

  .athlete-profile {
    display: flex;
    gap: 16px;

    @include media-breakpoint-between(md, lg) {
      flex-direction: column;
      align-items: flex-start;
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
      padding-bottom: 16px;
    }

    &-img {
      width: 300px;
      height: 300px;
      object-fit: cover;

      @include media-breakpoint-down(sm) {
        width: 140px;
        height: 140px;
      }
    }

    .left-side {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 0 0 auto;

      @include media-breakpoint-down(sm) {
        width: 100%;
        border-bottom: 2px solid $olympics-border-color;
      }
    }

    &-name {
      color: $olympics-color;
      font-size: 26px;
      font-weight: 700;
      line-height: 32px;
      font-family: var(--kui-font-opensans);

      @include media-breakpoint-down(sm) {
        font-size: 18px;
        line-height: 24px;
      }

      &.mobile {
        @include media-breakpoint-up(lg) {
          display: none;
        }
      }

      &.desktop-athlete {
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    .right-side {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 48px;

      @include media-breakpoint-down(sm) {
        gap: 8px;
        align-items: center;
        width: 100%;
      }
    }

    &-details {
      display: flex;
      gap: 4px;
      flex-direction: column;

      @include media-breakpoint-down(md) {
        gap: 2px;
        width: 100%;
      }
    }

    &-detail {
      display: flex;
      align-items: center;
      gap: 16px;

      &-label,
      &-value {
        flex: 1;
      }

      &-label {
        max-width: 96px;
        height: 24px;
        text-align: right;
        color: #64748b;
        font-size: 14px;
        font-weight: 600;
        line-height: 24px;

        @include media-breakpoint-down(lg) {
          max-width: 140px;
        }

        @include media-breakpoint-down(sm) {
          width: auto;
          height: 20px;
        }
      }

      &-value {
        color: $olympics-color;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;

        &.highlighted {
          color: $olympics-label-color;
          font-size: 16px;
          line-height: 24px;

          &.light {
            font-weight: 400;
          }
        }
      }
    }
  }
}
