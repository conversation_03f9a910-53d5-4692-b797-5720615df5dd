import { Async<PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { ReqService } from '@trendency/kesma-core';
import {
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleVideoComponent,
  BackendArticleSearchResult,
  IconComponent,
  Layout,
  LayoutPageType,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
  OlimpiaHungarianTeam,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
  Tag,
  VideoComponentObject,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ArticleCardType, ManArticleCardComponent, ManSimpleButtonComponent, ManSpinnerComponent } from 'src/app/shared';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { OlimpiaFooterNavigatorComponent } from '../../../../../shared/components/olimpia/olimpia-footer-navigator/olimpia-footer-navigator.component';
import { articlesPageSize } from '../../../../category/category.definitions';
import { LayoutComponent } from '../../../../layout/components/layout/layout.component';
import { OlympicsAthleteProfileCardComponent } from '../olympics-athlete-profile-card/olympics-athlete-profile-card.component';

@Component({
  selector: 'app-sport-olympics-athlete-profile',
  templateUrl: './olympics-athlete-profile.component.html',
  styleUrl: './olympics-athlete-profile.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OlympicsAthleteProfileCardComponent,
    ManArticleCardComponent,
    ManSpinnerComponent,
    ManSimpleButtonComponent,
    OlimpiaFooterNavigatorComponent,
    LayoutComponent,
    OlimpiaPageBannerComponent,
    IconComponent,
    NgTemplateOutlet,
    NgIf,
    ArticleVideoComponent,
    AsyncPipe,
    StrossleAdvertComponent,
  ],
})
export class OlympicsAthleteProfileComponent implements OnInit {
  articles = signal<ArticleCard[]>([]);
  limitable = signal<LimitableMeta>({} as LimitableMeta);
  athleteProfile = signal<OlimpiaHungarianTeam>({} as OlimpiaHungarianTeam);
  athleteVideo = signal<VideoComponentObject>({} as VideoComponentObject);

  page = signal<number>(0);
  loader = signal<boolean>(false);

  readonly ArticleCardType = ArticleCardType;
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;

  private readonly destroyRef = inject(DestroyRef);
  private readonly route = inject(ActivatedRoute);
  private readonly reqService = inject(ReqService);

  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));

  ngOnInit(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ data }) => {
      this.articles.set(data['profileData']?.articles?.data);
      this.limitable.set(data['profileData']?.articles?.meta);
      this.athleteProfile.set(data['profileData']?.athleteProfile);

      this.athleteVideo.set({
        isActive: true,
        isDeleted: false,
        isPublic: true,
        videaUrl: this.athleteProfile()?.videoUrl || '',
      } as VideoComponentObject);
    });
  }

  get canLoadMore(): boolean {
    return this.limitable()?.rowAllCount !== 0 && articlesPageSize * (this.page() + 1) < Number(this.limitable()?.rowAllCount);
  }

  loadMore(): void {
    if (!this.canLoadMore) {
      return;
    }

    this.page.update((page) => page + 1);
    this.loader.set(true);

    this.reqService
      .get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>('/content-page/search', {
        params: {
          page_limit: this.page(),
          rowCount_limit: '5',
          'tagSlugs[]': this.athleteProfile().tags?.map((tag: Tag) => tag?.slug) || [],
        },
      })
      .pipe(
        tap(() => this.loader.set(false)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ data }) => {
        this.articles.update((articles) => [...articles, ...data.map(mapBackendArticleDataToArticleCard)]);
      });
  }
}
