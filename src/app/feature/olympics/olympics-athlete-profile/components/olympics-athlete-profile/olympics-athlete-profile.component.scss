@use 'shared' as *;

:host {
  margin-bottom: 30px;

  &,
  man-spinner {
    display: block;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .block {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 32px 0;
  }

  .related-articles {
    margin-top: 10px;

    @include media-breakpoint-down(md) {
      margin-top: 24px;
    }
  }

  .related-articles-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--kui-brand-olimpic-dark-gold);
    border-bottom: 1px solid var(--kui-brand-olimpic-dark-gold);
    margin: 16px 12px;
    padding-bottom: 16px;
    line-height: 24px;
    letter-spacing: 0.2px;
  }

  kesma-article-video {
    max-width: 596px;
    display: block;
    margin: 24px auto;

    @include media-breakpoint-down(md) {
      margin: 16px auto;
    }
  }

  man-article-card {
    margin-bottom: 30px;

    @include media-breakpoint-down(md) {
      margin-bottom: 20px;
    }
  }

  kesma-olimpia-page-banner {
    .olimpia-page-banner {
      display: flex;
      gap: 4px;
      align-items: center;

      @include media-breakpoint-down(sm) {
        gap: 0;
      }

      .hun-flag {
        width: 32px;
        height: 24px;
        border-radius: 4px;
        border: 2px solid var(--kui-white);

        @include media-breakpoint-down(sm) {
          width: 24px;
          height: 18px;
          border-radius: 3px;
          margin-right: 4px;
        }
      }

      span {
        font-size: 20px;
        font-weight: 700;
        line-height: 24px;
        color: var(--kui-white);

        @include media-breakpoint-down(md) {
          letter-spacing: -0.4px;
        }
      }

      kesma-icon {
        color: var(--kui-white);
      }
    }
  }

  @include media-breakpoint-down(md) {
    man-simple-button {
      width: 100%;
    }
  }
}
