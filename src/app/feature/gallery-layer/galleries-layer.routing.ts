import { Routes } from '@angular/router';
import { GalleryLayerComponent } from './gallery-layer.component';
import { GalleryLayerResolver } from './gallery-layer.resolver';

export const galleryLayerRoutes: Routes = [
  {
    path: ':slug',
    redirectTo: ':slug/',
    pathMatch: 'full',
  },
  {
    path: ':slug',
    resolve: {
      pageData: GalleryLayerResolver,
    },
    providers: [GalleryLayerResolver],
    children: [
      {
        path: ':index',
        component: GalleryLayerComponent,
      },
    ],
  },
  { path: '**', redirectTo: '/404' },
];
