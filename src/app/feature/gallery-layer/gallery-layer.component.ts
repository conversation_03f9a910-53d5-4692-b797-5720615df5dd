import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ApiResult, createCanonicalUrlForPageablePage, GalleryData, GalleryRecommendationData } from '@trendency/kesma-ui';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FullscreenDirective, IMetaData, SeoService, StorageService } from '@trendency/kesma-core';
import { tap } from 'rxjs';
import {
  createMandinerTitle,
  defaultMetaInfo,
  GalleryCardTypes,
  ManAdultComponent,
  ManGalleryCardComponent,
  ManSocialShareModalComponent,
  SocialInteractionEvent,
  UrlService,
} from '../../shared';
import { Gallery } from '@trendency/kesma-ui/lib/definitions/gallery.definitions';
import { Location, NgForOf, NgIf, SlicePipe } from '@angular/common';
import { HistoryState } from '@trendency/kesma-ui/lib/components/adult/adult.definitions';
import { FormatPipeModule } from 'ngx-date-fns';
import { GalleryLayerThumbnailSwiperComponent } from './components/gallery-layer-thumbnail-swiper/gallery-layer-thumbnail-swiper.component';

@Component({
  selector: 'app-gallery-layer',
  templateUrl: './gallery-layer.component.html',
  styleUrls: ['./gallery-layer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManAdultComponent,
    FullscreenDirective,
    FormatPipeModule,
    NgIf,
    ManSocialShareModalComponent,
    GalleryLayerThumbnailSwiperComponent,
    ManGalleryCardComponent,
    RouterLink,
    NgForOf,
    SlicePipe,
  ],
})
export class GalleryLayerComponent implements OnInit {
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  gallery: GalleryData;
  recommendations: GalleryData[];
  currentId = 0;
  isFullScreen = true;
  previousUrl = '';
  isUserAdultChoice: boolean;

  readonly GalleryCardTypes = GalleryCardTypes;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly location: Location,
    private readonly urlService: UrlService,
    private readonly storage: StorageService
  ) {}

  ngOnInit(): void {
    this.route.parent?.data
      .pipe(
        tap((result) => {
          const {
            galleryDetails,
            recommended,
          }: {
            galleryDetails: GalleryData;
            recommended: ApiResult<GalleryRecommendationData[]>;
          } = result['pageData'];

          this.gallery = galleryDetails;
          this.isUserAdultChoice = this.isUserAdultChoiceFromStorage;
          this.recommendations = recommended.data as unknown as GalleryData[];
          this.setMetaData();
          this.currentId = 0;
          this.changeDetector.detectChanges();
        })
      )
      .subscribe();

    this.urlService.previousUrl$.subscribe((previousUrl) => {
      if (previousUrl) {
        this.previousUrl = previousUrl;
      }
    });
  }

  get isUserAdultChoiceFromStorage(): boolean {
    return this.storage.getSessionStorageData('isAdultChoice', false) ?? false;
  }

  onUserAdultChoose(isAdult: boolean): void {
    this.isUserAdultChoice = isAdult;
  }

  public goBack(): void {
    this.isFullScreen = false;
    const hasLocation = +(this.location.getState() as HistoryState)?.navigationId || 0;
    if (hasLocation > 1) {
      this.location.back();
    } else {
      this.router.navigate(['/']).then();
    }
  }

  public onPreviousClick(): void {
    this.currentId = this.currentId - 1 > -1 ? --this.currentId : this.gallery.images.length;
    this.router
      .navigate([`../${this.currentId + 1}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  public onNextClick(): void {
    this.currentId = this.currentId < this.gallery?.images.length ? ++this.currentId : 0;
    this.router
      .navigate([`../${this.currentId + 1}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  public onThumbnailClick(index: number): void {
    this.currentId = index;
    this.router
      .navigate([`../${++index}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  public onToggleFullScreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullScreen = !this.isFullScreen;
    fullScreenDirective.toggleFullScreen();
  }

  public get title(): string {
    return this.isRecommendedPage ? 'Mandiner Galériák' : this.gallery.title;
  }

  public get isRecommendedPage(): boolean {
    return this.currentId >= this.gallery.images.length;
  }

  private setMetaData(): void {
    const title = createMandinerTitle(this.gallery.title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      ogImage: (this.gallery as unknown as Gallery).highlightedImage.url,
    };
    this.seo.setMetaData(metaData);

    const canonical = createCanonicalUrlForPageablePage('galeria', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }
}
