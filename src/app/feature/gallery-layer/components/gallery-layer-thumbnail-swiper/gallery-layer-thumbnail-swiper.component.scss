@use 'shared' as *;

:host {
  display: block;
  position: relative;
  .gallery-layer-swiper {
    position: relative;
    display: flex;
    align-items: center;

    swiper-container {
      width: 100%;
    }

    &-box {
      margin-top: 20px;
      padding-right: 20px;
      cursor: pointer;

      @include media-breakpoint-down(md) {
        margin-top: 10px;
        padding-right: 10px;
      }
    }

    &-image {
      object-fit: cover;
      height: 112.5px;
    }

    &-arrow {
      height: 100%;
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
      top: 0;

      @include media-breakpoint-down(md) {
        position: absolute;
      }

      &.next {
        right: 0;
      }

      .icon {
        width: 10px;
        height: 20px;
      }
    }

    @include media-breakpoint-down(md) {
      margin: 0 -15px;
    }
  }
}
