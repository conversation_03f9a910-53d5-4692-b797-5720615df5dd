<div *ngIf="data" class="gallery-layer-swiper">
  <button class="gallery-layer-swiper-arrow prev" (click)="swipePrev()">
    <i class="icon arrow-left"></i>
  </button>
  <swiper-container
    #swiper
    [breakpoints]="swiperBreakpoints"
    slides-per-view="2.4"
    slides-per-group="3"
    speed="1000"
    rewind="true"
    center-insufficient-slides="true"
  >
    <swiper-slide *ngFor="let gallery of data?.images; index as index">
      <div class="gallery-layer-swiper-box">
        <img class="gallery-layer-swiper-image" [alt]="gallery?.altText" [src]="gallery?.url?.thumbnail" (click)="handleThumbnailClick(index)" />
      </div>
    </swiper-slide>
  </swiper-container>
  <button class="gallery-layer-swiper-arrow next" (click)="swipeNext()">
    <i class="icon arrow-right"></i>
  </button>
</div>
