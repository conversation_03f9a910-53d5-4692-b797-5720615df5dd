import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, Output } from '@angular/core';
import { GalleryData, SwiperBaseComponent } from '@trendency/kesma-ui';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-gallery-layer-thumbnail-swiper',
  templateUrl: './gallery-layer-thumbnail-swiper.component.html',
  styleUrls: ['./gallery-layer-thumbnail-swiper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class GalleryLayerThumbnailSwiperComponent extends SwiperBaseComponent<GalleryData> {
  swiperBreakpoints = {
    576: {
      slidesPerView: 4,
      slidesPerGroup: 4,
      speed: 1500,
    },
    1024: {
      slidesPerView: 5,
      slidesPerGroup: 5,
    },
  };
  @Output() thumbnailClick = new EventEmitter<number>();
  handleThumbnailClick(index: number): void {
    this.thumbnailClick.emit(index);
  }
}
