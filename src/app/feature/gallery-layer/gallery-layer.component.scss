@use 'shared' as *;

:host {
  display: block;

  .icon {
    width: 100%;
  }

  .gallery-layer {
    padding-top: 20px;
    color: var(--kui-white);
    background-color: var(--kui-black);
    position: fixed;
    overflow: scroll;
    width: 100%;
    height: 100%;
    z-index: 9999;
    top: 0;
    left: 0;

    @include media-breakpoint-down(md) {
      overflow-x: hidden;
    }

    &-top-icons {
      position: absolute;
      display: flex;
      right: 20px;
      gap: 10px;

      &-item {
        min-width: 40px;
        min-height: 40px;
        background-color: var(--kui-orange-600);
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        display: flex;
        cursor: pointer;

        &:not(:has(.close)) {
          @include media-breakpoint-down(md) {
            display: none;
          }
        }

        .close {
          width: 16px;
          height: 16px;
        }

        .fullscreen {
          width: 14px;
          height: 14px;
        }
      }
    }

    &-header {
      display: flex;
      align-items: center;
      gap: 10px;

      @include media-breakpoint-down(md) {
        display: block;
      }

      &-title {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        line-height: 30px;
        font-size: 24px;

        @include media-breakpoint-down(md) {
          margin-right: 50px;
        }
      }

      &-item {
        display: flex;
        align-items: center;
        gap: 10px;

        &.end {
          margin-left: auto;
          margin-right: 115px;
          display: block;
        }
      }

      &-publish {
        font-weight: 400;
        line-height: 21px;
        font-size: 16px;
        margin-bottom: 6px;
        white-space: nowrap;

        @include media-breakpoint-down(md) {
          margin-top: 10px;
        }
      }

      &-meta {
        display: flex;
        justify-content: space-between;
        gap: 20px;
        font-size: 16px;
        line-height: 21px;
        white-space: nowrap;

        @include media-breakpoint-down(md) {
          justify-content: flex-start;
        }
      }

      &-icon {
        min-width: 36px;
        min-height: 36px;
        background-color: var(--kui-orange-600);
        display: flex;
        align-items: center;
        cursor: pointer;

        @include media-breakpoint-down(md) {
          display: none;
        }

        .arrow-left {
          height: 12px;
        }
      }
    }

    &-content {
      margin: 30px 0;
      text-align: center;
      line-height: 22px;
      font-size: 16px;
      position: relative;

      .recommended {
        flex: 0 0 calc(50% - 10px);
        color: var(--kui-black);
        text-align: left;
      }

      &.recommended-block {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          margin: 30px -15px;
        }
      }

      &-card {
        width: 100%;
        height: 100%;
        background-color: rgba(#fff, 0.1); // Don't work with var(--kui-white)
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        margin-top: 10px;
      }

      &-thumbnail {
        max-height: 580px;
        object-fit: scale-down;
      }
    }

    &-main-thumbnail {
      display: flex;
      align-items: center;
      gap: 20px;

      @include media-breakpoint-down(md) {
        margin: 0 -15px;
      }
    }

    &-arrow {
      .icon {
        width: 15px;
        height: 30px;
      }

      &.next {
        right: 0;
      }

      @include media-breakpoint-down(md) {
        position: absolute;

        &.prev {
          left: 0;
        }
      }
    }

    &-socials {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      gap: 20px;

      @include media-breakpoint-down(md) {
        flex-direction: column-reverse;
        row-gap: 15px;
      }
    }
  }

  mandiner-social-share-modal {
    color: var(--kui-black);
    text-align: left;
    cursor: pointer;
    display: flex;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    gap: 10px;
    flex: 0 0 auto;

    span {
      color: var(--kui-white);
    }
  }

  ::ng-deep {
    .swiper-button-next,
    .swiper-button-prev {
      display: none;
    }

    .swiper-slide:last-child {
      .gallery-layer-swiper-box {
        padding-right: 0;
      }
    }

    .social-icon.share {
      &:hover {
        content: unset;
      }
    }
  }

  ::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
}
