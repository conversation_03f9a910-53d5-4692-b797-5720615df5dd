import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import {
  ApiResponseMetaList,
  ApiResult,
  BackendGalleryDetails,
  GalleryDetails,
  GalleryRecommendationData,
  mapBackendGalleryDetailsResultToGalleryDetails,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class GalleryApiService {
  public constructor(private readonly reqService: ReqService) {}

  public getGalleryDetails(slug: string): Observable<GalleryDetails> {
    return this.reqService
      .get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`)
      .pipe(map(mapBackendGalleryDetailsResultToGalleryDetails));
  }

  public getGalleryRecommendations(slug: string): Observable<ApiResult<GalleryRecommendationData[]>> {
    return this.reqService.get<ApiResult<GalleryRecommendationData[]>>(`media/gallery/${slug}/recommendation`);
  }
}
