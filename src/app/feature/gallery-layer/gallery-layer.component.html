<ng-container *ngIf="!isUserAdultChoice && gallery?.isAdult; else galleryContent">
  <man-adult (isUserAdult)="onUserAdultChoose($event)"></man-adult>
</ng-container>

<ng-template #galleryContent>
  <section class="gallery-layer" trFullscreen #fullScreen="trFullscreen">
    <div class="gallery-layer-top-icons">
      <div class="gallery-layer-top-icons-item" (click)="goBack()">
        <i class="icon close"></i>
      </div>
      <div class="gallery-layer-top-icons-item" (click)="onToggleFullScreen(fullScreen)">
        <i class="icon fullscreen"></i>
      </div>
    </div>

    <div class="wrapper">
      <div class="gallery-layer-header">
        <div class="gallery-layer-header-item">
          <div class="gallery-layer-header-icon" (click)="goBack()">
            <i class="icon arrow-left"></i>
          </div>
          <span class="gallery-layer-header-title">{{ title }}</span>
        </div>
        <div class="gallery-layer-header-item end" *ngIf="!isRecommendedPage">
          <div class="gallery-layer-header-publish">
            {{ gallery.publishDate | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
          </div>
          <div class="gallery-layer-header-meta">
            <strong>{{ gallery.images.length }} fotó</strong>
            Fotók: {{ gallery.photographer }}
          </div>
        </div>
      </div>

      <div class="gallery-layer-content" [class.recommended-block]="isRecommendedPage">
        <ng-container *ngIf="!isRecommendedPage; else recommendedTemplate">
          <div class="gallery-layer-socials">
            <div class="gallery-layer-socials-item">
              <strong>{{ currentId + 1 }} / {{ gallery.images.length }}</strong>
              <ng-container *ngIf="gallery?.images?.[currentId]?.caption; else galleryDescription">
                <div>{{ gallery?.images?.[currentId]?.caption }}</div>
              </ng-container>
              <ng-template #galleryDescription>
                <div>{{ gallery.description }}</div>
              </ng-template>
            </div>

            <mandiner-social-share-modal
              [isShowShareText]="true"
              [title]="gallery.title"
              [link]="['/', 'galeria', gallery.slug]"
              (socialInteraction)="socialInteraction.emit($event)"
            >
              <span>Megosztás</span>
            </mandiner-social-share-modal>
          </div>

          <div class="gallery-layer-main-thumbnail">
            <button class="gallery-layer-arrow prev" (click)="onPreviousClick()">
              <i class="icon arrow-left"></i>
            </button>
            <div class="gallery-layer-content-card">
              <img class="gallery-layer-content-thumbnail" [src]="gallery.images[currentId]?.url?.fullSize" [alt]="gallery.images[currentId]?.altText" />
            </div>
            <button class="gallery-layer-arrow next" (click)="onNextClick()">
              <i class="icon arrow-right"></i>
            </button>
          </div>
          <app-gallery-layer-thumbnail-swiper
            *ngIf="gallery?.images"
            [data]="gallery"
            (thumbnailClick)="onThumbnailClick($event)"
          ></app-gallery-layer-thumbnail-swiper>
        </ng-container>
      </div>
    </div>
  </section>
</ng-template>

<ng-template #recommendedTemplate>
  <man-gallery-card
    [isInsideAdultArticleBody]="gallery?.isAdult"
    [routerLink]="['/', 'galeria', recommendation.slug, 1]"
    *ngFor="let recommendation of recommendations | slice: 0 : 4"
    [styleID]="GalleryCardTypes.RECOMMENDED"
    [isShowTag]="true"
    [data]="recommendation"
  >
  </man-gallery-card>
</ng-template>
