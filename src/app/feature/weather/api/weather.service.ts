import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BackendWeatherData, BackendWeatherForecast, WeatherCity } from '@trendency/kesma-ui';
import { backendWeatherForecastToWeatherForecast, translateWeatherIcon } from './weather.utils';

export const WEATHER_REGIONS: WeatherCity[] = [
  'Budapest',
  'Debrecen',
  'Eger',
  'Győr',
  'Kaposvár',
  '<PERSON><PERSON>ke<PERSON>t',
  'Miskolc',
  'Nyíregyháza',
  'Pécs',
  '<PERSON>g<PERSON><PERSON>j<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>fehérvár',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  'Szolnok',
  'Szombathely',
  'Tatabánya',
  '<PERSON><PERSON>zprém',
  'Zalaegerszeg',
];

@Injectable({
  providedIn: 'root',
})
export class WeatherService {
  constructor(private readonly reqService: ReqService) {}

  getWeather(params?: Record<string, string>): Observable<BackendWeatherData> {
    const options: IHttpOptions = { params };

    return this.reqService.get('/mediaworks/weather', options).pipe(map((result: any) => this.processResult(result.data)));
  }

  getFullWeatherData(): Observable<BackendWeatherData> {
    return this.getWeather();
  }

  private processResult(data: BackendWeatherData): BackendWeatherData {
    return <BackendWeatherData>{
      ...data,
      current: data.current?.map((c) => ({ ...c, icon2: translateWeatherIcon(c.icon2) })),
      forecast: data.forecast
        ? {
            ...(Object.keys(data.forecast).reduce(
              (all, city) => ({
                ...all,
                [city]: data.forecast[city as WeatherCity].map(backendWeatherForecastToWeatherForecast),
              }),
              {}
            ) as Record<WeatherCity, BackendWeatherForecast[]>),
          }
        : null,
    };
  }
}
