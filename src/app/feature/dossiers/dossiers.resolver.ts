import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, DossierArticle } from '@trendency/kesma-ui';
import { ApiService, DossierCard } from '../../shared';
import { tap } from 'rxjs/operators';

@Injectable()
export class DossiersResolver {
  constructor(private readonly apiService: ApiService) {}

  resolve(): Observable<ApiResult<DossierCard[], ApiResponseMetaList>> {
    const dossierArticlesCount = 10;

    return this.apiService.getDossiers().pipe(
      tap((res: ApiResult<DossierCard[], ApiResponseMetaList>) => {
        res.data.forEach((data: DossierCard) => {
          (data.articles as Partial<DossierArticle>) = data.articles.slice(0, dossierArticlesCount);
        });
      })
    );
  }
}
