import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { createCanonicalUrlForPageablePage, DossierArticle } from '@trendency/kesma-ui';
import { createMandinerTitle, defaultMetaInfo, DossierCard, ManDossierCardComponent } from '../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgFor } from '@angular/common';

@Component({
  selector: 'app-dossiers',
  templateUrl: './dossiers.component.html',
  styleUrls: ['./dossiers.component.scss'],
  imports: [NgFor, ManDossierCardComponent],
})
export class DossiersComponent implements OnInit {
  dossiers: DossierCard[];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.dossiers = this.mapDossiers(this.route.snapshot.data?.['dossiers']?.data);
    this.setMetaData();
  }

  mapDossiers(dossiers: DossierCard[]): DossierCard[] {
    return dossiers.map((dossier: DossierCard) => ({
      ...dossier,
      articles: dossier.articles.map(
        (article: Partial<DossierArticle>) => ({ ...article, publishDate: new Date(article.publishDate as Date) }) as DossierArticle
      ),
    }));
  }
  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('dossziek');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Dossziék');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
