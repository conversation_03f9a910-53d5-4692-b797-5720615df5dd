import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orO<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { FormatDatePipe, IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, mapBackendArticleDataToArticleCardWithHideThumbnail } from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ApiService,
  ArticleCardType,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  ManArticleCardComponent,
  ManBread<PERSON>rumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
} from '../../shared';

const MAX_RESULTS_PER_PAGE = 20;

@Component({
  selector: 'app-search-page',
  templateUrl: './search-page.component.html',
  styleUrls: ['./search-page.component.scss'],
  providers: [FormatDatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    ManSearchFilterComponent,
    AsyncPipe,
    NgIf,
    NgForOf,
    ManArticleCardComponent,
    ManSimpleButtonComponent,
    StrossleAdvertComponent,
  ],
})
export class SearchPageComponent implements OnInit, OnDestroy {
  readonly ArticleCardType = ArticleCardType;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((data) => data['searchData']));
  MAX_RESULTS_PER_PAGE = MAX_RESULTS_PER_PAGE;
  searchResults: ArticleCard[] = [];
  isLoading = false;
  page = 0;
  rowAllCount? = 0;
  filterValues: FilterValues = {};
  showedSearchedKeyWord?: string;

  constructor(
    private readonly apiService: ApiService,
    private readonly cd: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  ngOnInit(): void {
    this.subscribeToResolverDataChange();
  }

  private subscribeToResolverDataChange(): void {
    this.filterValues = { ...this.filterValues, keyword: this.route.snapshot.queryParamMap.get('global_filter') as string };
    this.search();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  get canLoadMore(): boolean | void {
    if (this.rowAllCount) {
      return this.rowAllCount !== 0 && this.MAX_RESULTS_PER_PAGE * (this.page + 1) < this.rowAllCount;
    }
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.searchResults = [];
    this.page = 0;
    this.search();
  }

  loadMoreResults(): void {
    if (this.canLoadMore) {
      this.page += 1;
      this.search();
    }
  }

  search(): void {
    this.isLoading = true;
    const searchQuery = this.searchFilterDataService.setSearchQuery(this.filterValues);
    this.apiService
      .searchByKeyword(
        this.filterValues.keyword,
        this.filterValues.sort,
        searchQuery.fromDate,
        searchQuery.toDate,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).contentTypes,
        this.filterValues.columns,
        this.page,
        this.MAX_RESULTS_PER_PAGE,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).ownMaterial
      )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((result) => {
        this.rowAllCount = result.meta['limitable'].rowAllCount;
        this.showedSearchedKeyWord = this.filterValues.keyword;
        this.searchResults = this.searchResults.concat(
          result.data
            .map((data) => mapBackendArticleDataToArticleCardWithHideThumbnail(data))
            .map((data) => ({
              ...data,
              thumbnail: {
                url: data.thumbnail?.url || '/assets/images/placeholder-1-1.svg',
                alt: data.thumbnail?.alt,
              },
            }))
        );
        this.setUrl(searchQuery.fromDate, searchQuery.toDate);
        this.isLoading = false;
        this.cd.detectChanges();
        this.setMetaData();
      });
  }

  private setMetaData(): void {
    const title = createMandinerTitle(this.filterValues?.keyword ? `Keresés: ${this.filterValues.keyword}` : 'Keresés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  private setUrl(from_date?: string | Date, to_date?: string | Date): void {
    let queryParams: Params = {};
    queryParams = this.filterValues.keyword ? { ...queryParams, global_filter: this.filterValues.keyword } : queryParams;
    queryParams = from_date ? { ...queryParams, from_date } : queryParams;
    queryParams = to_date ? { ...queryParams, to_date } : queryParams;
    queryParams = this.filterValues?.contentTypes ? { ...queryParams, 'contentTypes[]': this.filterValues.contentTypes } : queryParams;
    queryParams = this.filterValues?.columns?.length ? { ...queryParams, 'columns[]': this.filterValues.columns } : queryParams;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: queryParams,
    });
  }
}
