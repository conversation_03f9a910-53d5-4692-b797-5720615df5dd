<section>
  <div class="wrapper">
    <man-breadcrumb [items]="[{ label: 'Keresés eredménye' }]"></man-breadcrumb>

    <man-search-filter
      [data]="(searchFilterColumns$ | async) || undefined"
      [resultCount]="rowAllCount"
      [defaultFilterValues]="filterValues"
      (filterEvent)="onFilter($event)"
      [filterOnSide]="true"
      [showedSearchedKeyWord]="showedSearchedKeyWord"
      articleTitle="Keresés"
    ></man-search-filter>

    <section class="results">
      <ng-container *ngIf="searchResults.length > 0">
        <div *ngFor="let result of searchResults; let i = index">
          <man-article-card
            class="article-card"
            [data]="result"
            [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
            [isMaxWidth]="true"
            [isTagVisible]="true"
            [isMplus]="result.isPaywalled"
          ></man-article-card>

          <div *ngIf="i === 3" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
          </div>

          <div *ngIf="i === 7" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
          </div>

          <div *ngIf="i === 11" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
          </div>

          <div *ngIf="i === 15" class="desktop">
            <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="isLoading">
        <div class="no-results-text">
          <p>Betöltés...</p>
        </div>
      </ng-container>
    </section>

    <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()"> Mutass többet </man-simple-button>
  </div>
</section>
