@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  man-breadcrumb {
    margin-bottom: 20px;
  }

  man-search-filter {
    margin-bottom: 50px;

    @include media-breakpoint-down(md) {
      padding-bottom: 20px;
    }

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;

      &::ng-deep {
        .search-bar {
          margin-bottom: 0 !important;
        }
      }
    }
  }

  .ad {
    margin-bottom: 30px;
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
