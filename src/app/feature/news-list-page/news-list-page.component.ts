import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import { BreadcrumbItem, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { fromEvent, Observable, of, startWith, Subject } from 'rxjs';
import { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';
import {
  ApiService,
  ArticleCardType,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  makeBreadcrumbSchema,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  ManSpinnerComponent,
  Paginator,
  SearchFilterDataService,
  SearchFilterDefinitions,
  TranslatedDatePipe,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';

const MAX_RESULTS_PER_PAGE = 20;

@Component({
  selector: 'app-news-list-page',
  templateUrl: './news-list-page.component.html',
  styleUrls: ['./news-list-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    ManBreadcrumbComponent,
    ManSearchFilterComponent,
    NgIf,
    NgForOf,
    DatePipe,
    ManArticleCardComponent,
    ManSpinnerComponent,
    ManSimpleButtonComponent,
    SidebarComponent,
    TranslatedDatePipe,
    StrossleAdvertComponent,
  ],
})
export class NewsListPageComponent implements OnInit, OnDestroy {
  currentWindowWidth$ = this.utilsService.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth)
      )
    : of(0);
  isMobile$ = this.currentWindowWidth$.pipe(map((width: number) => width < 992));
  displaySidebar = true;
  destroy$ = new Subject<void>();

  readonly articleCardType = ArticleCardType.ImgRightTitleLeadDateMeta;
  readonly breadcrumbItems: BreadcrumbItem[] = [{ label: 'Hírek', url: ['/', 'hirek'] }];
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  filterValues: FilterValues = {};

  readonly searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((data) => data['searchData']));

  readonly paginator = new Paginator((page) => {
    const searchDateRange = this.searchFilterDataService.setSearchQuery(this.filterValues);
    return this.apiService.getNewsArticles(
      page,
      MAX_RESULTS_PER_PAGE,
      this.isOrderByDateAsc(),
      searchDateRange.fromDate,
      searchDateRange.toDate,
      undefined,
      this.filterValues.columns
    );
  });
  readonly sidebarExcludedIds$ = this.paginator.data$.pipe(map((articles) => articles?.map((article) => article.id || '') || []));

  constructor(
    private readonly apiService: ApiService,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly searchFilterDataService: SearchFilterDataService,
    private readonly route: ActivatedRoute,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      this.isMobile$.pipe(distinctUntilChanged(), takeUntil(this.destroy$)).subscribe((isMobile: boolean) => {
        if (isMobile) {
          this.displaySidebar = false;
        } else {
          this.displaySidebar = true;
        }
      });
    }

    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.paginator.reset();
  }

  loadMore(): void {
    this.paginator.next();
  }

  isOrderByDateAsc(): boolean {
    if (!this.filterValues?.sort?.length) {
      return false;
    }
    const sortFields = this.filterValues.sort.split('_');
    return sortFields[0] === 'date' && sortFields[1] === 'asc';
  }

  private setMetaData(): void {
    const title = createMandinerTitle('Hírek');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      // eslint-disable-next-line max-len
      description: `Hírek rovat - Friss hírek, aktuális események itthonról és a világból egyaránt. Belföld, Külföld, Sport vagy
        Kultúra, a legfontosabb híreket megtalálja a Mandiner.hu oldalán.`,
      // eslint-disable-next-line max-len
      ogDescription: `Hírek rovat - Friss hírek, aktuális események itthonról és a világból egyaránt. Belföld, Külföld, Sport vagy Kultúra,
         a legfontosabb híreket megtalálja a Mandiner.hu oldalán.`,
    };
    this.seo.setMetaData(metaData);

    const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
    this.schemaService.insertSchema(breadcrumbSchema);

    const canonical = createCanonicalUrlForPageablePage('hirek');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
