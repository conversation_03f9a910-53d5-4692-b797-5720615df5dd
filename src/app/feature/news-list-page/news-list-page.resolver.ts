import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import { SearchFilterDefinitions } from '../../shared';

@Injectable()
export class NewsListPageResolver {
  constructor(private readonly searchFilterDataService: SearchFilterDataService) {}

  resolve(): Observable<SearchFilterDefinitions> {
    return this.searchFilterDataService.getSearchFilterColoumns();
  }
}
