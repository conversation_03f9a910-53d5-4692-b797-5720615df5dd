<section>
  <div class="wrapper" *ngIf="paginator.meta$ | async as meta">
    <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems"></man-breadcrumb>
    <h1 class="man-article-title">H<PERSON>rek időrendben</h1>
    <div class="tag-results-meta">
      <div class="tag-results-counter">
        <strong>{{ meta.limitable?.rowAllCount }} db</strong> cikk ö<PERSON>en
      </div>
      <man-search-filter
        [data]="(searchFilterColumns$ | async) || undefined"
        [resultCount]="meta?.limitable?.rowAllCount"
        (filterEvent)="onFilter($event)"
        [showSearchBar]="false"
        [showSearchHeader]="false"
        [isShowContentTypes]="false"
        articleTitle="Hírek"
      ></man-search-filter>
    </div>
  </div>
</section>

<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <section class="results">
        <ng-container *ngIf="paginator.data$ | async as articleResults">
          <div *ngFor="let result of articleResults; let i = index">
            <div class="date" *ngIf="result.publishDate && (articleResults?.[i - 1]?.publishDate | date) !== (articleResults?.[i]?.publishDate | date)">
              <strong> {{ result.publishDate | manTranslatedDatePipe }} </strong>
              <span> {{ result.publishDate | manTranslatedDatePipe: '(EEEE)' }} </span>
            </div>
            <man-article-card class="article-card" [data]="result" [styleID]="articleCardType" [isMplus]="result.isPaywalled"></man-article-card>
            <div *ngIf="i === 3" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>
            </div>

            <div *ngIf="i === 7" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_2"></app-strossle-advert>
            </div>

            <div *ngIf="i === 11" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_3"></app-strossle-advert>
            </div>

            <div *ngIf="i === 15" class="desktop">
              <app-strossle-advert advertId="Mandiner_normal_content_4"></app-strossle-advert>
            </div>
          </div>
        </ng-container>
        <div *ngIf="paginator.isLoading$ | async" class="no-results">
          <man-spinner></man-spinner>
        </div>
      </section>
      <man-simple-button *ngIf="paginator.hasMore$ | async" (click)="loadMore()"> Mutass többet </man-simple-button>
    </div>
    <aside *ngIf="displaySidebar">
      <app-sidebar *ngIf="sidebarExcludedIds$ | async as excludedIds" [excludedIds]="excludedIds"></app-sidebar>
    </aside>
  </div>
</section>
