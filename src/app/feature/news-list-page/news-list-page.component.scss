@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  .man-article-title {
    font-family: var(--kui-font-primary);
  }

  .tag-results-meta {
    margin-top: 20px;
  }

  .tag-results-counter {
    font-size: 14px;
    margin-bottom: 10px;
  }

  man-search-filter {
    &::ng-deep {
      .search-bar.without-search {
        margin-bottom: 0 !important;
      }
    }
  }

  man-breadcrumb {
    margin-bottom: 20px;
  }

  .ad {
    margin-bottom: 30px;
  }

  .date {
    font-size: 18px;
    margin-bottom: 30px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
    }

    strong {
      font-weight: 800;
    }
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}

.no-results {
  margin: auto;
  width: fit-content;
}
