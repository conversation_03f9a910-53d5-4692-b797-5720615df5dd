<section>
  <div class="wrapper login-wrapper">
    <div class="row">
      <div class="col-12 col-lg-6">
        <div class="login-form input-form">
          <div class="login-form-header">
            <h2 class="login-form-header-title">Belépés</h2>
            <p class="login-form-header-text">
              <PERSON><PERSON><PERSON><PERSON>n be <PERSON>r-felhasz<PERSON>ló<PERSON> profil<PERSON>, és használja ki a regisztrált olvasóknak járó előnyöket! Ha még nincs <PERSON>-fió<PERSON>,
              <a class="login-form-header-text-link" routerLink="/regisztracio"> itt ingyenesen regisztrálthat!</a>
            </p>
          </div>

          <ng-container *ngIf="allowedLoginMethods$ | async as allowedLoginMethods">
            <form (ngSubmit)="login()" *ngIf="formGroup" [formGroup]="formGroup">
              <ng-container *ngIf="allowedLoginMethods?.email">
                <div class="mandiner-form-row">
                  <kesma-form-control>
                    <label class="mandiner-form-label" for="emailOrUsername">E-mail-cím</label>
                    <input class="mandiner-form-input" formControlName="emailOrUsername" id="emailOrUsername" tabindex="1" type="text" />
                  </kesma-form-control>
                </div>
                <div class="mandiner-form-row">
                  <kesma-form-control class="only-border">
                    <label class="mandiner-form-label mandiner-form-label-password" for="password">
                      Jelszó
                      <a class="mandiner-form-label-password-link" routerLink="/elfelejtett-jelszo">Elfelejtette a jelszavát?</a>
                    </label>
                    <div class="mandiner-form-input-password">
                      <input [type]="showPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="password" id="password" tabindex="2" />
                      <img
                        (click)="showPassword = !showPassword"
                        [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
                        alt="Jelszó megtekintése"
                        class="mandiner-form-input-password-img"
                      />
                    </div>
                  </kesma-form-control>
                </div>
                <div *ngIf="error" class="general-form-error">
                  {{ error }}
                </div>
                <div class="row login-form-button">
                  <div class="col-12 col-lg-6">
                    <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100"
                      >{{ isLoading ? 'Kérem, várjon...' : 'Bejelentkezem' }}
                    </man-simple-button>
                  </div>
                  <div class="col-12 col-lg-6">
                    <kesma-form-control class="checkbox">
                      <div class="mandiner-form-checkbox-item">
                        <input class="mandiner-form-checkbox" formControlName="rememberMe" id="rememberMe" type="checkbox" />
                        <label class="mandiner-form-checkbox-label" for="rememberMe">Maradjon belépve</label>
                      </div>
                    </kesma-form-control>
                  </div>
                </div>

                <div
                  *ngIf="
                    allowedLoginMethods?.[SocialProvider.GOOGLE] ||
                    allowedLoginMethods?.[SocialProvider.FACEBOOK] ||
                    allowedLoginMethods?.[SocialProvider.APPLE]
                  "
                  class="login-form-or"
                >
                  - vagy -
                </div>
              </ng-container>

              <div class="login-form-social">
                <man-simple-button
                  (click)="loginWithSocialProvider(SocialProvider.GOOGLE)"
                  *ngIf="allowedLoginMethods?.[SocialProvider.GOOGLE]"
                  [disabled]="isLoading"
                  class="social w-100"
                  color="outline"
                  icon="google"
                  >Belépés Google fiókkal
                </man-simple-button>
                <man-simple-button
                  (click)="loginWithSocialProvider(SocialProvider.FACEBOOK)"
                  *ngIf="allowedLoginMethods?.[SocialProvider.FACEBOOK]"
                  [disabled]="isLoading"
                  class="social w-100"
                  color="outline"
                  icon="facebook"
                  >Belépés Facebook fiókkal
                </man-simple-button>
                <man-simple-button
                  (click)="loginWithSocialProvider(SocialProvider.APPLE)"
                  *ngIf="allowedLoginMethods?.[SocialProvider.APPLE]"
                  [disabled]="isLoading"
                  class="social w-100"
                  color="outline"
                  icon="apple"
                  >Belépés Apple fiókkal
                </man-simple-button>
              </div>
            </form>
          </ng-container>
        </div>
      </div>
      <div class="col-12 col-lg-6">
        <div class="login-form">
          <h3 class="login-form-header-title">Még nem regisztált?</h3>
          <p class="login-form-text">
            Ha még nincs Mandiner-felhasználói profilja, most díjmentesen létrehozhat magának egyet, hogy ki tudja használni a regisztrációval járó előnyöket!
          </p>
          <div class="login-form-media-list">
            <div class="login-form-media">
              <img alt="" class="login-form-media-image" src="/assets/images/icons/icon-comment-bubble.svg" />
              <div class="login-form-media-body">
                <h4 class="login-form-media-title">Szóljon hozzá a cikkekhez</h4>
                <p class="login-form-media-text">Kapcsolódjon be az izgalmas beszélgetésekbe, és ossza meg véleményét a többi olvasóval!</p>
              </div>
            </div>
            <div class="login-form-media">
              <img alt="" class="login-form-media-image" src="/assets/images/icons/icon-checked.svg" />
              <div class="login-form-media-body extra-ml">
                <h4 class="login-form-media-title">Mentse el későbbre</h4>
                <p class="login-form-media-text">Nincs ideje elolvasni egy hosszabb elemző cikket? Egyszerűen mentse el, és olvassa el később!</p>
              </div>
            </div>
            <div class="login-form-media">
              <img alt="" class="login-form-media-image" src="/assets/images/icons/icon-clock-reverse.svg" />
              <div class="login-form-media-body">
                <h4 class="login-form-media-title">Nézze vissza az olvasási előzményeit</h4>
                <p class="login-form-media-text">Tekintse át egyszerűen, hogy milyen cikkeket olvasott el a Mandineren!</p>
              </div>
            </div>
            <div class="login-form-media">
              <img alt="" class="login-form-media-image" src="/assets/images/icons/icon-person.svg" />
              <div class="login-form-media-body">
                <h4 class="login-form-media-title">Személyre szabott ajánlások</h4>
                <p class="login-form-media-text">Minél jobban megismerjük Önt, annál jobb ajánlásokat fog kapni tőlünk!</p>
              </div>
            </div>
            <div class="login-form-media">
              <img alt="" class="login-form-media-image" src="/assets/images/icons/icon-envelope.svg" />
              <div class="login-form-media-body">
                <h4 class="login-form-media-title">Értesítések a postafiókjában</h4>
                <p class="login-form-media-text">Az Ön számára érdekes témákat kérésre e-mailben is elküldjük Önnek!</p>
              </div>
            </div>
          </div>
          <man-simple-button class="w-100" routerLink="/regisztracio">Tovább az ingyenes regisztrációhoz »</man-simple-button>
        </div>
      </div>
    </div>
  </div>
</section>
