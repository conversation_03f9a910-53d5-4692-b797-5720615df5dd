@use 'shared' as *;

.login-wrapper {
  padding: 50px 0 70px;

  @include media-breakpoint-down(md) {
    padding: 20px 0 50px;
  }

  > .row > div:first-child {
    border-right: 1px solid var(--kui-gray-100);

    @include media-breakpoint-down(md) {
      border-right: 0;
      margin-bottom: 20px;
    }
  }
}

.login-form {
  max-width: 384px;
  margin: auto;

  @include media-breakpoint-down(md) {
    &.input-form {
      border-bottom: 1px solid var(--kui-gray-100);
      padding-bottom: 20px;
    }
  }

  &-header {
    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      line-height: 24px;
      margin-bottom: 20px;
    }

    &-text {
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 20px;

      &-link {
        color: var(--kui-orange-600);
      }
    }
  }

  .mandiner-form-label-password {
    display: flex;
    justify-content: space-between;

    &-link {
      color: var(--kui-black-700);
      font-weight: 400;
      font-size: 12px;
      text-decoration: underline;
      transition: all 0.3s ease-in-out;

      &:hover {
        color: var(--kui-orange-600);
      }
    }
  }

  &-button {
    margin-top: 20px;
    align-items: center;

    @include media-breakpoint-down(md) {
      man-simple-button {
        margin-bottom: 10px;
      }
    }
  }

  .mandiner-form-checkbox {
    &-label {
      margin-bottom: 0;
    }
  }

  &-or {
    display: block;
    text-align: center;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 12px;
    margin: 20px 0;
  }

  &-social {
    man-simple-button {
      margin-bottom: 5px;
    }
  }

  &-text {
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 10px;
  }

  &-media {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    &-list {
      margin: 25px 0 20px;
    }

    &-body {
      &.extra-ml {
        margin-left: 4px;
      }
    }

    &-image {
      width: 20px;
      margin-right: 10px;
    }

    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 5px;
    }

    &-text {
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
    }
  }
}
