import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleAuthor, createCanonicalUrlForPageablePage, FollowedColumn, User } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import {
  AuthService,
  createMandinerTitle,
  defaultMetaInfo,
  FollowHelperService,
  ManBreadcrumbComponent,
  ManProfileAuthorsComponent,
  ManProfileCategoriesComponent,
  ManProfileCommentsComponent,
  ManProfileReadingHistoryComponent,
  ManProfileSavedArticlesComponent,
  ManSimpleButtonComponent,
} from '../../shared';
import { getPeriodForPrice } from '../subscription/subscription.utils';
import { ProfilePageData } from './profile.definitions';
import { AsyncPipe, NgIf } from '@angular/common';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    NgIf,
    AsyncPipe,
    ManSimpleButtonComponent,
    RouterLink,
    ManProfileAuthorsComponent,
    ManProfileCategoriesComponent,
    ManProfileReadingHistoryComponent,
    ManProfileSavedArticlesComponent,
    ManProfileCommentsComponent,
  ],
})
export class ProfileComponent implements OnInit {
  data$: Observable<ProfilePageData> = this.route.data.pipe(map((data) => data['data']));
  getPeriodForPrice = getPeriodForPrice;

  loadingFollowedAuthorSlug: string | null = null;
  loadingFollowedCategorySlug: string | null = null;

  followedAuthors$: Observable<ArticleAuthor[]> = this.authService.currentUserExtraDataSubject.pipe(
    switchMap(() =>
      this.data$.pipe(
        map((data: ProfilePageData) =>
          [
            ...data.followedAuthors,
            ...data.firstThreeAuthors.filter((a: ArticleAuthor) => !data.followedAuthors.map((fa: ArticleAuthor) => fa.slug).includes(a.slug)),
          ].map((author: ArticleAuthor) => ({
            ...author,
            isFollowed: this.followHelperService.isAuthorFollowed(author.slug ?? ''),
          }))
        )
      )
    )
  );

  constructor(
    private readonly authService: AuthService,
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute,
    private readonly followHelperService: FollowHelperService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  onChangeFollowedAuthor(author: ArticleAuthor): void {
    if (author.slug) {
      this.loadingFollowedAuthorSlug = author.slug;
      this.followHelperService.handleAuthorFollow(author.slug).subscribe(() => {
        this.loadingFollowedAuthorSlug = null;
        this.cdr.detectChanges();
      });
    }
  }

  onChangeFollowedColumn(column: FollowedColumn): void {
    if (column.slug) {
      this.loadingFollowedCategorySlug = column.slug;
      this.followHelperService.handleColumnFollow(column.slug).subscribe(() => {
        this.loadingFollowedCategorySlug = null;
        this.cdr.detectChanges();
      });
    }
  }

  redirectToSubscriptions(): void {
    window.open(environment.shopUrls.subscriptions);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Profilom');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
