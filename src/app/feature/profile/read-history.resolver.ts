import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { forkJoin, Observable } from 'rxjs';
import { SecureApiService } from 'src/app/shared/services/secure-api.service';

@Injectable({
  providedIn: 'root',
})
export class ReadHistoryResolver implements Resolve<any> {
  public constructor(private readonly secureApiService: SecureApiService) {}

  resolve(): Observable<any> {
    return forkJoin({
      articles: this.secureApiService.getReadingHistory(),
    });
  }
}
