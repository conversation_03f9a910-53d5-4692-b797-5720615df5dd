<section>
  <div class="wrapper profile-settings-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil', url: '/profil' }, { label: 'Beállítások' }]"></man-breadcrumb>

    <h1 class="profile-settings-title">Profilbeállítások</h1>

    <man-profile-settings [user]="user" (legacyCancelSubscription)="showLegacyCancelModal.set(true)" />
  </div>
</section>

@if (showLegacyCancelModal()) {
  <man-popup
    [title]="'Előfizetés lemondása'"
    [acceptButtonLabel]="isLoading ? 'Kérem, várjon...' : 'Lemondás'"
    [cancelButtonLabel]="isLoading ? 'Kérem, várjon...' : 'Mégsem'"
    [disabled]="isLoading"
    (resultEvent)="handleLegacyCancel($event)"
  >
    <PERSON><PERSON><PERSON> abban, hogy lemondja jelenlegi előfizetését?<br /><br />
    Az előfizetési időszak végéig még hozzáférése lesz prémiumtartalmainkhoz, ezt követően már nem újítjuk meg előfizetését automatikusan.
    <div class="general-form-error" *ngIf="error">
      {{ error }}
    </div>
  </man-popup>
}
