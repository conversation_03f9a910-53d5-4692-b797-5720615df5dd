import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, signal } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  BackendUserResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManBreadcrumbComponent,
  ManPopupComponent,
  ManProfileSettingsComponent,
  SecureApiService,
} from '../../../../shared';
import { Router } from '@angular/router';
import { createCanonicalUrlForPageablePage, User } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.component.html',
  styleUrls: ['./profile-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBread<PERSON>rumbComponent, ManProfileSettingsComponent, ManPopupComponent, NgIf],
})
export class ProfileSettingsComponent implements OnInit {
  readonly showLegacyCancelModal = signal(false);
  isLoading = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly secureApiService: SecureApiService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/beallitasok');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Profilbeállítások');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }

  handleLegacyCancel(popupResult: boolean): void {
    if (!popupResult) {
      this.showLegacyCancelModal.set(false);
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .legacyCancelRecurringSubscription()
      .pipe(
        catchError(() => {
          this.error = 'Ismeretlen hiba!';
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser())
      )
      .subscribe((backendUserResponse: BackendUserResponse) => {
        this.authService.currentUserSubject.next(this.authService.mapBackendUserResponseToUser(backendUserResponse));
        this.router.navigate(['/profil']).then();
      });
  }
}
