import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, BreadcrumbItem, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { map } from 'rxjs/operators';
import { SecureApiService } from 'src/app/shared/services/secure-api.service';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ArticleCardType,
  createMandinerTitle,
  FilterValues,
  ITimeframe,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
} from '../../../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-reading-history',
  templateUrl: './reading-history.component.html',
  styleUrls: ['./reading-history.component.scss'],
  imports: [ManBreadcrumbComponent, NgIf, ManSearchFilterComponent, AsyncPipe, NgForOf, ManArticleCardComponent, ManSimpleButtonComponent],
})
export class ReadingHistoryComponent implements OnInit {
  pageTitle: string;
  breadcrumbItems?: BreadcrumbItem[] = [{ label: 'Személyes profil', url: '/profil' }];
  filterValues: FilterValues = {};
  rowAllCount = 0;
  page = 0;
  articleList: ArticleCard[] = [];
  isReadinHistory: boolean;
  readonly cardType = ArticleCardType.ImgRightTitleLeadDateMeta;

  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((data) => data['data']?.['searchData']));

  constructor(
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly secureApiService: SecureApiService,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  ngOnInit(): void {
    this.setDefaultDatas();
    this.isReadinHistory = this.router.url.includes('elozmenyek');

    this.subscribeToResolverDataChange();
    this.setMetaData();
    this.setBreadcrumbItems();
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && 12 * (this.page + 1) < this.rowAllCount;
  }

  setDefaultDatas(): void {
    this.isReadinHistory = this.router.url.includes('elozmenyek');
    this.pageTitle = this.isReadinHistory ? 'Előzmények' : 'Mentett cikkek';
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.page = 0;
    this.loadMoreResults(false);
  }

  loadMoreResults(loadMore = true): void {
    if (loadMore) {
      this.page++;
    } else {
      this.articleList = [];
    }
    const searchQuery = this.searchFilterDataService.setSearchQuery(this.filterValues);
    if (this.isReadinHistory) {
      this.loadMoreReadingHistoryArticles(searchQuery, !loadMore);
    } else {
      this.loadMoreSavedArticles(searchQuery, !loadMore);
    }
  }

  loadMoreReadingHistoryArticles(searchQuery: ITimeframe, skipPageNumIncrement = false): void {
    this.secureApiService
      .getReadingHistory(
        this.page,
        12,
        this.filterValues.sort,
        searchQuery.fromDate,
        searchQuery.toDate,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).contentTypes,
        this.filterValues.columns,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).ownMaterial
      )
      .subscribe((data) => {
        if (!skipPageNumIncrement) {
          this.page += 1;
        }
        this.articleList = [...this.articleList, ...data.data];
        this.changeDetectorRef.markForCheck();
      });
  }

  loadMoreSavedArticles(searchQuery: ITimeframe, skipPageNumIncrement = false): void {
    this.secureApiService
      .getSavedArticlesList(
        this.page,
        12,
        this.filterValues.sort,
        searchQuery.fromDate,
        searchQuery.toDate,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).contentTypes,
        this.filterValues.columns,
        this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).ownMaterial
      )
      .subscribe((data) => {
        if (!skipPageNumIncrement) {
          this.page += 1;
        }
        this.articleList = [...this.articleList, ...data.data];
        this.changeDetectorRef.markForCheck();
      });
  }

  private subscribeToResolverDataChange(): void {
    this.route.data.subscribe((res) => {
      this.articleList = res?.['data']?.articles;
      this.rowAllCount = res?.['data']?.meta['limitable']?.rowAllCount || 0;
      this.changeDetectorRef.markForCheck();
    });
  }

  private setBreadcrumbItems(): void {
    if (this.isReadinHistory) {
      this.breadcrumbItems?.push({ label: 'Előzmények' });
    } else {
      this.breadcrumbItems?.push({ label: 'Mentett cikkek' });
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('elozmenyek');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Előzmények');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
