<section>
  <div class="wrapper">
    <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems" class="reading-history-breadcrumb"> </man-breadcrumb>

    <h1 class="reading-history-title">{{ pageTitle }}</h1>

    <div class="reading-history-search">
      <man-search-filter
        [data]="(searchFilterColumns$ | async) || undefined"
        [showSearchBar]="false"
        (filterEvent)="onFilter($event)"
        [showSearchHeader]="false"
      ></man-search-filter>
    </div>

    <ng-container *ngIf="articleList?.length">
      <div *ngFor="let article of articleList">
        <man-article-card [styleID]="cardType" [data]="article" [isMaxWidth]="true" [isMplus]="article.isPaywalled"></man-article-card>
      </div>
    </ng-container>

    <man-simple-button *ngIf="canLoadMore" (click)="loadMoreResults()">
      <strong>Mutass többet</strong>
    </man-simple-button>
  </div>
</section>
