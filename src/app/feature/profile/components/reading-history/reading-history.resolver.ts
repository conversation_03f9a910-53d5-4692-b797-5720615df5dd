import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, map, Observable } from 'rxjs';
import { SecureApiService } from 'src/app/shared/services/secure-api.service';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
@Injectable()
export class ReadHistoryResolver {
  public constructor(
    private readonly SearchFilterDataService: SearchFilterDataService,
    private readonly secureApiService: SecureApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const method = route?.data?.['resolveMethod'];
    if (method === 'getReadingHistoryArticles') {
      return this.getReadingHistoryArticles();
    } else {
      return this.getSavedArticles();
    }
  }

  getReadingHistoryArticles(): any {
    return forkJoin([this.SearchFilterDataService.getSearchFilterColoumns(), this.secureApiService.getReadingHistory()]).pipe(
      map(([searchData, articles]) => ({
        searchData: searchData,
        articles: articles.data,
        meta: articles.meta,
      }))
    );
  }

  getSavedArticles(): any {
    return forkJoin([this.SearchFilterDataService.getSearchFilterColoumns(), this.secureApiService.getSavedArticlesList()]).pipe(
      map(([searchData, articles]) => ({
        searchData: searchData,
        articles: articles.data,
        meta: articles.meta,
      }))
    );
  }
}
