@use 'shared' as *;

:host {
  display: block;
  padding: 40px 0;

  man-search-filter {
    &::ng-deep {
      .search-bar.without-search {
        margin-bottom: 0 !important;
      }
    }
  }

  .reading-history {
    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-size: 24px;
      padding: 20px 0 40px;
      line-height: 24px;
      font-weight: 700;
    }

    &-lead {
      font-family: var(--kui-font-secondary);
      line-height: 30px;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 24px;
    }

    &-breadcrumb {
      padding-bottom: 8px;
    }

    &-search {
      margin-bottom: 30px;

      @include media-breakpoint-down(sm) {
        margin-bottom: 20px;
      }

      &-title {
        font-weight: 700;
        font-size: 18px;
        margin-bottom: 10px;
      }
    }

    &-flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    man-breadcrumb {
      margin-bottom: 20px;
      border-bottom: 1px solid var(--kui-gray-100);
      padding-bottom: 8px;
    }
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }
}
