import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  BackendUserResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManBreadcrumbComponent,
  ManPopupComponent,
  ManSimpleButtonComponent,
  SecureApiService,
} from '../../../../shared';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched, User } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile-consent-settings',
  templateUrl: './profile-consent-settings.component.html',
  styleUrls: ['./profile-consent-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, NgIf, ReactiveFormsModule, KesmaFormControlComponent, ManSimpleButtonComponent, ManPopupComponent],
})
export class ProfileConsentSettingsComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showSuccessPopup = false;
  isLoading = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      newsletter: [!!this.user?.newsletter],
      marketing: [!!this.user?.marketingLetter],
    });
  }

  editUserConsent(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .editUserConsent(this.formGroup.value.newsletter, this.formGroup.value.marketing)
      .pipe(
        catchError(() => {
          this.error = 'Ismeretlen hiba!';
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser())
      )
      .subscribe((backendUserResponse: BackendUserResponse) => {
        this.authService.currentUserSubject.next(this.authService.mapBackendUserResponseToUser(backendUserResponse));
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      });
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/profil']);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/beallitasok/nyilatkozatok');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Nyilatkozatok módosítása');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
