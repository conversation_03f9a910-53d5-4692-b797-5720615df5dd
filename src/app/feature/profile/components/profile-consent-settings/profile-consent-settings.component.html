<section>
  <div class="wrapper profile-settings-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil', url: '/profil' }, { label: 'Nyilatkozatok' }]"></man-breadcrumb>

    <h1 class="profile-settings-title">Profilbeállítások</h1>

    <form *ngIf="formGroup" [formGroup]="formGroup" class="profile-settings-form" (ngSubmit)="editUserConsent()">
      <h2 class="profile-settings-form-title">Nyilatkozatok módosítása</h2>

      <div class="mandiner-form-checkboxes">
        <kesma-form-control class="checkbox">
          <div class="mandiner-form-checkbox-item">
            <input class="mandiner-form-checkbox" type="checkbox" id="marketing" formControlName="marketing" />
            <label class="mandiner-form-checkbox-label" for="marketing">
              Hozzájárulok, hogy a megadott személyes adataimat a Mandiner Novum Zrt. tájékoztatás, közvélemény- vag<PERSON>, illetve egyéb tájékoztatás
              céljából felhasználja, és ezzel kapcsolatosan engem az általam megadott elérhetőségeken megkeressen.
            </label>
          </div>
        </kesma-form-control>
        <kesma-form-control class="checkbox">
          <div class="mandiner-form-checkbox-item">
            <input class="mandiner-form-checkbox" type="checkbox" id="newsletter" formControlName="newsletter" />
            <label class="mandiner-form-checkbox-label" for="newsletter">Hírlevél-feliratkozás.</label>
          </div>
        </kesma-form-control>
      </div>
      <div class="general-form-error" *ngIf="error">
        {{ error }}
      </div>
      <div class="profile-settings-form-button">
        <man-simple-button class="w-100" [disabled]="isLoading" [isSubmit]="true">{{ isLoading ? 'Kérem, várjon...' : 'Módosítás' }} </man-simple-button>
        <man-simple-button class="w-100 profile-settings-form-button-cancel" color="light" [disabled]="isLoading" routerLink=".."
          >{{ isLoading ? 'Kérem, várjon...' : 'Mégsem' }}
        </man-simple-button>
      </div>
    </form>
  </div>
</section>

<man-popup
  *ngIf="showSuccessPopup"
  [title]="'Sikeres adatmódosítás'"
  [acceptButtonLabel]="'Tovább a személyes profilhoz'"
  [showCancelButton]="false"
  (resultEvent)="handleSuccessPopupResult()"
>
  Kedves Olvasónk!<br /><br />
  A módosítás sikeres. További kellemes olvasást kívánunk!
</man-popup>
