import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  BackendUserResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManBreadcrumbComponent,
  ManPopupComponent,
  ManSimpleButtonComponent,
  SecureApiService,
} from '../../../../shared';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { Router, RouterLink } from '@angular/router';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  KesmaFormControlComponent,
  markControlsTouched,
  passwordValidator,
  User,
  usernameValidator,
} from '@trendency/kesma-ui';
import { ProfileLoginSettingsErrorValue } from '../../profile.definitions';
import { profileLoginDataFormToBackendRequest } from '../../profile.utils';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile-login-data-settings',
  templateUrl: './profile-login-data-settings.component.html',
  styleUrls: ['./profile-login-data-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, ReactiveFormsModule, NgIf, KesmaFormControlComponent, ManSimpleButtonComponent, RouterLink, ManPopupComponent],
})
export class ProfileLoginDataSettingsComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showNewPassword = false;
  showOldPassword = false;
  showSuccessPopup = false;
  isLoading = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      username: [this.user?.username ?? null, [Validators.required, usernameValidator]],
      newPassword: [null, [passwordValidator]],
      oldPassword: [null, [Validators.required, passwordValidator]],
    });
  }

  editCurrentUser(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .editCurrentUser(profileLoginDataFormToBackendRequest(this.formGroup.value))
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                if (value.errors.find((error) => error?.includes('100'))) {
                  this.formGroup.get('username')?.setErrors({ usernameMaxLength: true });
                } else {
                  this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                }
                isErrorHandled = true;
              }
              // User old password is not correct
              if (errorKey === 'oldPassword' && !!(value as ProfileLoginSettingsErrorValue)?.errors) {
                this.formGroup.get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error = 'Ismeretlen hiba!';
          }
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser())
      )
      .subscribe((backendUserResponse: BackendUserResponse) => {
        this.authService.currentUserSubject.next(this.authService.mapBackendUserResponseToUser(backendUserResponse));
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      });
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/profil']);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/beallitasok/belepesi-adatok');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Belépési adatok módosítása');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
