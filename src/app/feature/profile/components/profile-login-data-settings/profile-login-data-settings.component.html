<section>
  <div class="wrapper profile-settings-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil', url: '/profil' }, { label: 'Belépési adatok' }]"></man-breadcrumb>

    <h1 class="profile-settings-title">Profilbeállítások</h1>

    <form (ngSubmit)="editCurrentUser()" *ngIf="formGroup" [formGroup]="formGroup" class="profile-settings-form">
      <h2 class="profile-settings-form-title">Belépési adatok módosítása</h2>
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="username">Felhasználónév <strong>*</strong></label>
          <input class="mandiner-form-input" formControlName="username" id="username" type="text" />
        </kesma-form-control>
        <small class="mandiner-form-small"
          >Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
          tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
        >
      </div>
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="newPassword">Új jelszó</label>
          <div class="mandiner-form-input-password">
            <input [type]="showNewPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="newPassword" id="newPassword" />
            <img
              (click)="showNewPassword = !showNewPassword"
              [src]="showNewPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small"
          >A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot.</small
        >
      </div>
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="oldPassword">Jelenlegi jelszó <strong>*</strong></label>
          <div class="mandiner-form-input-password">
            <input [type]="showOldPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="oldPassword" id="oldPassword" />
            <img
              (click)="showOldPassword = !showOldPassword"
              [src]="showOldPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small"
          >A felhasználónév megváltoztatása vagy új jelszó beállítása előtt biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</small
        >
      </div>
      <div *ngIf="error" class="general-form-error">
        {{ error }}
      </div>
      <div class="profile-settings-form-button">
        <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100"
          >{{ isLoading ? 'Kérem, várjon...' : 'Adatok módosítása' }}
        </man-simple-button>
        <man-simple-button [disabled]="isLoading" class="w-100 profile-settings-form-button-cancel" color="light" routerLink=".."
          >{{ isLoading ? 'Kérem, várjon...' : 'Mégsem' }}
        </man-simple-button>
      </div>
    </form>
  </div>
</section>

<man-popup
  (resultEvent)="handleSuccessPopupResult()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'Tovább a személyes profilhoz'"
  [showCancelButton]="false"
  [title]="'Sikeres adatmódosítás'"
>
  Kedves Olvasónk!<br /><br />
  A módosítás sikeres. További kellemes olvasást kívánunk!
</man-popup>
