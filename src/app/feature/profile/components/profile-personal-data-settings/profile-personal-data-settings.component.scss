@use 'shared' as *;

.profile-settings {
  &-wrapper {
    padding: 30px 0 50px;

    @include media-breakpoint-down(md) {
      padding: 20px 0 50px;
    }
  }

  &-title {
    font-family: var(--kui-font-primary);
    color: var(--kui-orange-600);
    border-bottom: 1px solid var(--kui-gray-100);
    font-size: 24px;
    padding-bottom: 20px;
    margin: 20px 0 10px;
    line-height: 24px;
    font-weight: 700;
  }

  &-form {
    max-width: 384px;
    margin: auto;

    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 16px;
      line-height: 22px;
      text-transform: uppercase;
      margin: 20px 0;
    }

    &-group {
      margin-bottom: 50px;

      &-title {
        font-family: var(--kui-font-primary);
        font-weight: 700;
        font-size: 18px;
        line-height: 20px;
        margin-bottom: 20px;
      }
    }

    .mandiner-form-checkboxes {
      margin-bottom: 10px;
    }

    &-button {
      margin-top: 20px;
      display: flex;

      &-cancel {
        --button-bg-light: transparent;
        margin-left: 20px;
      }
    }

    &-warning {
      font-size: 14px;
      line-height: 18px;
      font-weight: bold;
      color: var(--kui-red-500);
      padding-bottom: 20px;
    }
  }
}
