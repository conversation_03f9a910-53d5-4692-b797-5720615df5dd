<section>
  <div class="wrapper profile-settings-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil', url: '/profil' }, { label: '<PERSON><PERSON><PERSON><PERSON><PERSON> adatok' }]"></man-breadcrumb>

    <h1 class="profile-settings-title">Profilbeállítások</h1>

    <form (ngSubmit)="editCurrentUser()" *ngIf="formGroup" [formGroup]="formGroup" class="profile-settings-form">
      <h2 class="profile-settings-form-title"><PERSON><PERSON><PERSON><PERSON><PERSON> adatok módosítása</h2>
      <!-- Billing data -->
      <div class="profile-settings-form-group">
        <div class="profile-settings-form-group-title">Számlázási adatok</div>
        <ng-container formGroupName="billingAddress">
          <div class="mandiner-form-row">
            <kesma-form-control>
              <label class="mandiner-form-label" for="billingAddressName">Név <strong>*</strong></label>
              <input class="mandiner-form-input" formControlName="name" id="billingAddressName" type="text" />
            </kesma-form-control>
            <small class="mandiner-form-small">Teljes név vagy jogi személy esetén a cég neve</small>
          </div>
          <div class="row">
            <div class="col-12 col-sm-4">
              <div class="mandiner-form-row">
                <kesma-form-control class="only-border">
                  <label class="mandiner-form-label" for="billingAddressZip">Irányítószám <strong>*</strong></label>
                  <input class="mandiner-form-input" formControlName="zip" id="billingAddressZip" type="text" />
                </kesma-form-control>
              </div>
            </div>
            <div class="col-12 col-sm-8">
              <div class="mandiner-form-row">
                <kesma-form-control>
                  <label class="mandiner-form-label" for="billingAddressCity">Település <strong>*</strong></label>
                  <input class="mandiner-form-input" formControlName="city" id="billingAddressCity" type="text" />
                </kesma-form-control>
              </div>
            </div>
          </div>
          <div class="mandiner-form-row">
            <kesma-form-control>
              <label class="mandiner-form-label" for="billingAddressAddress">Cím <strong>*</strong></label>
              <input class="mandiner-form-input" formControlName="address" id="billingAddressAddress" type="text" />
            </kesma-form-control>
          </div>
        </ng-container>

        <!-- Phone number -->
        <div class="mandiner-form-row">
          <kesma-form-control>
            <label class="mandiner-form-label" for="phoneNumber">Telefonszám <strong>*</strong></label>
            <input class="mandiner-form-input" formControlName="phoneNumber" id="phoneNumber" type="text" />
          </kesma-form-control>
          <small class="mandiner-form-small">Csak számok, például: 0611234567</small>
        </div>

        <!-- Tax info -->
        <div class="mandiner-form-row" formGroupName="billingAddress">
          <kesma-form-control>
            <label class="mandiner-form-label" for="billingAddressTax">Adószám</label>
            <input class="mandiner-form-input" formControlName="tax" id="billingAddressTax" type="text" />
          </kesma-form-control>
          <small class="mandiner-form-small">Opcionális, jogi személy esetén kötelező</small>
        </div>
      </div>

      <!-- Shipping data -->
      <div class="profile-settings-form-group">
        <div class="profile-settings-form-group-title">Előfizetői (szállítási) adatok</div>
        <div class="mandiner-form-checkboxes">
          <kesma-form-control class="checkbox">
            <div class="mandiner-form-checkbox-item">
              <input class="mandiner-form-checkbox" formControlName="isShippingAddressSame" id="isShippingAddressSame" type="checkbox" />
              <label class="mandiner-form-checkbox-label" for="isShippingAddressSame">Az előfizetői adatok megegyeznek a számlázási adatokkal.</label>
            </div>
          </kesma-form-control>
        </div>
        <ng-container *ngIf="!formGroup.get('isShippingAddressSame')?.value" formGroupName="shippingAddress">
          <div class="mandiner-form-row">
            <kesma-form-control>
              <label class="mandiner-form-label" for="shippingAddressName">Név <strong>*</strong></label>
              <input class="mandiner-form-input" formControlName="name" id="shippingAddressName" type="text" />
            </kesma-form-control>
            <small class="mandiner-form-small">Teljes név vagy jogi személy esetén a cég neve</small>
          </div>
          <div class="row">
            <div class="col-12 col-sm-4">
              <div class="mandiner-form-row">
                <kesma-form-control class="only-border">
                  <label class="mandiner-form-label" for="shippingAddressZip">Irányítószám <strong>*</strong></label>
                  <input class="mandiner-form-input" formControlName="zip" id="shippingAddressZip" type="text" />
                </kesma-form-control>
              </div>
            </div>
            <div class="col-12 col-sm-8">
              <div class="mandiner-form-row">
                <kesma-form-control>
                  <label class="mandiner-form-label" for="shippingAddressCity">Település <strong>*</strong></label>
                  <input class="mandiner-form-input" formControlName="city" id="shippingAddressCity" type="text" />
                </kesma-form-control>
              </div>
            </div>
          </div>
          <div class="mandiner-form-row">
            <kesma-form-control>
              <label class="mandiner-form-label" for="shippingAddressAddress">Cím <strong>*</strong></label>
              <input class="mandiner-form-input" formControlName="address" id="shippingAddressAddress" type="text" />
            </kesma-form-control>
          </div>
        </ng-container>
      </div>

      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="oldPassword">Jelenlegi jelszó <strong>*</strong></label>
          <div class="mandiner-form-input-password">
            <input [type]="showOldPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="oldPassword" id="oldPassword" />
            <img
              (click)="showOldPassword = !showOldPassword"
              [src]="showOldPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small">Az adatok megváltoztatása előtt biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</small>
      </div>
      <div *ngIf="error" class="general-form-error">
        {{ error }}
      </div>
      <div class="profile-settings-form-button">
        <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100"
          >{{ isLoading ? 'Kérem, várjon...' : 'Adatok módosítása' }}
        </man-simple-button>
        <man-simple-button [disabled]="isLoading" class="w-100 profile-settings-form-button-cancel" color="light" routerLink=".."
          >{{ isLoading ? 'Kérem, várjon...' : 'Mégsem' }}
        </man-simple-button>
      </div>
    </form>
  </div>
</section>

<man-popup
  (resultEvent)="handleSuccessPopupResult()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'Tovább a személyes profilhoz'"
  [showCancelButton]="false"
  [title]="'Sikeres adatmódosítás'"
>
  Kedves Olvasónk!<br /><br />
  A módosítás sikeres. További kellemes olvasást kívánunk!
</man-popup>
