import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  BackendUserResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManBreadcrumbComponent,
  ManPopupComponent,
  ManSimpleButtonComponent,
  SecureApiService,
} from '../../../../shared';
import { AbstractControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  KesmaFormControlComponent,
  markControlsTouched,
  nonWhitespaceOnlyValidator,
  passwordValidator,
  User,
} from '@trendency/kesma-ui';
import { profilePersonalDataFormToBackendRequest } from '../../profile.utils';
import { HttpErrorResponse } from '@angular/common/http';
import { ProfileLoginSettingsErrorValue } from '../../profile.definitions';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile-personal-data-settings',
  templateUrl: './profile-personal-data-settings.component.html',
  styleUrls: ['./profile-personal-data-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [ManBreadcrumbComponent, ReactiveFormsModule, NgIf, KesmaFormControlComponent, ManSimpleButtonComponent, ManPopupComponent],
})
export class ProfilePersonalDataSettingsComponent implements OnInit {
  formGroup: UntypedFormGroup;
  isLoading = false;
  showOldPassword = false;
  showSuccessPopup = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      billingAddress: this.formBuilder.group({
        name: [this.user?.userDetails?.invoiceName ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        zip: [this.user?.userDetails?.invoiceZip ?? null, [Validators.required, Validators.pattern('^[0-9]{4,5}$')]],
        city: [this.user?.userDetails?.invoiceCity ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        address: [this.user?.userDetails?.invoiceAddress ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        tax: [this.user?.userDetails?.taxNumber ?? null, [Validators.pattern('^[0-9]{8}-[0-9]{1}-[0-9]{2}$')]],
      }),
      shippingAddress: this.formBuilder.group({
        name: [
          {
            value: this.user?.userDetails?.shippingName ?? null,
            disabled: this.determineIfShippingAddressIsSame() ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
        zip: [
          {
            value: this.user?.userDetails?.shippingZip ?? null,
            disabled: this.determineIfShippingAddressIsSame() ?? true,
          },
          [Validators.required, Validators.pattern('^[0-9]{4,5}$')],
        ],
        city: [
          {
            value: this.user?.userDetails?.shippingCity ?? null,
            disabled: this.determineIfShippingAddressIsSame() ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
        address: [
          {
            value: this.user?.userDetails?.shippingAddress ?? null,
            disabled: this.determineIfShippingAddressIsSame() ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
      }),
      isShippingAddressSame: [this.determineIfShippingAddressIsSame() ?? true],
      phoneNumber: [this.user?.userDetails?.phoneNumber ?? null, [Validators.required, Validators.pattern('^[0-9]{9,11}$')]],
      oldPassword: [null, [Validators.required, passwordValidator]],
    });

    this.handleFormValueChanges();
  }

  determineIfShippingAddressIsSame(): boolean {
    return (
      this.user?.userDetails?.invoiceName === this.user?.userDetails?.shippingName &&
      this.user?.userDetails?.invoiceZip === this.user?.userDetails?.shippingZip &&
      this.user?.userDetails?.invoiceCity === this.user?.userDetails?.shippingCity &&
      this.user?.userDetails?.invoiceAddress === this.user?.userDetails?.shippingAddress
    );
  }

  handleFormValueChanges(): void {
    this.formGroup?.get('isShippingAddressSame')?.valueChanges.subscribe((isShippingAddressSame: boolean) => {
      Object.values((this.formGroup?.get('shippingAddress') as UntypedFormGroup)?.controls).forEach((control: AbstractControl) => {
        if (isShippingAddressSame) {
          control.disable();
        } else {
          control.enable();
        }
      });

      this.cdr.detectChanges();
    });
  }

  editCurrentUser(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .editCurrentUser(profilePersonalDataFormToBackendRequest(this.formGroup.value))
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User old password is not correct
              if (errorKey === 'oldPassword' && !!(value as ProfileLoginSettingsErrorValue)?.errors) {
                this.formGroup.get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error = 'Ismeretlen hiba!';
          }
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser())
      )
      .subscribe((backendUserResponse: BackendUserResponse) => {
        this.authService.currentUserSubject.next(this.authService.mapBackendUserResponseToUser(backendUserResponse));
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      });
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/profil']);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/beallitasok/szemelyes-adatok');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Személyes adatok módosítása');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
