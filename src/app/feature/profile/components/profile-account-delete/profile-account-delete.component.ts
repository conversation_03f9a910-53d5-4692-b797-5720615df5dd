import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  createMandinerTitle,
  defaultMetaInfo,
  ManBreadcrumbComponent,
  ManPopupComponent,
  ManSimpleButtonComponent,
  SecureApiService,
} from '../../../../shared';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { BackendFormErrors, createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched, passwordValidator } from '@trendency/kesma-ui';
import { ProfileLoginSettingsErrorValue } from '../../profile.definitions';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile-account-delete',
  templateUrl: './profile-account-delete.component.html',
  styleUrls: ['./profile-account-delete.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBreadcrumbComponent, ReactiveFormsModule, NgIf, KesmaFormControlComponent, ManSimpleButtonComponent, ManPopupComponent],
})
export class ProfileAccountDeleteComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPopup = false;
  showOldPassword = false;
  isLoading = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      oldPassword: [null, [Validators.required, passwordValidator]],
    });
  }

  deleteAccount(popupResult: boolean): void {
    this.showPopup = false;

    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid || !popupResult) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .deleteAccount(this.formGroup.value.oldPassword)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User old password is not correct
              if (errorKey === 'password' && !!(value as ProfileLoginSettingsErrorValue)?.errors) {
                this.formGroup.get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error = 'Ismeretlen hiba!';
          }
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.invalidate())
      )
      .subscribe(() => {
        this.router.navigate(['/']);
      });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/beallitasok/torles');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Profil törlése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
