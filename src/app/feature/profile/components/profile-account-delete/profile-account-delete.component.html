<section>
  <div class="wrapper profile-settings-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil', url: '/profil' }, { label: 'Profil törlése' }]"></man-breadcrumb>

    <h1 class="profile-settings-title">Profilbeállítások</h1>

    <form (ngSubmit)="showPopup = true" *ngIf="formGroup" [formGroup]="formGroup" class="profile-settings-form">
      <h2 class="profile-settings-form-title">Profil törlése</h2>
      <div class="profile-settings-form-warning">
        FIGYELEM! Mandiner-profiljával együtt az összes személyes adatát és esetleges aktív előfizetését is töröljük. A törlés nem visszavonható!
      </div>
      <div class="mandiner-form-row">
        <kesma-form-control>
          <label class="mandiner-form-label" for="oldPassword"><PERSON><PERSON><PERSON><PERSON> j<PERSON> <strong>*</strong></label>
          <div class="mandiner-form-input-password">
            <input [type]="showOldPassword ? 'text' : 'password'" class="mandiner-form-input" formControlName="oldPassword" id="oldPassword" />
            <img
              (click)="showOldPassword = !showOldPassword"
              [src]="showOldPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="mandiner-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="mandiner-form-small">A profil törlése előtt biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</small>
      </div>
      <div *ngIf="error" class="general-form-error">
        {{ error }}
      </div>
      <div class="profile-settings-form-button">
        <man-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100">{{ isLoading ? 'Kérem, várjon...' : 'Profil törlése' }} </man-simple-button>
        <man-simple-button [disabled]="isLoading" class="w-100 profile-settings-form-button-cancel" color="light" routerLink=".."
          >{{ isLoading ? 'Kérem, várjon...' : 'Mégsem' }}
        </man-simple-button>
      </div>
    </form>
  </div>
</section>

<man-popup
  (resultEvent)="deleteAccount($event)"
  *ngIf="showPopup"
  [acceptButtonLabel]="'Profil törlése'"
  [cancelButtonLabel]="'Mégsem'"
  [title]="'Profil törlése'"
>
  Biztos abban, hogy törli Mandiner-profilját és az összes személyes adatát?
</man-popup>
