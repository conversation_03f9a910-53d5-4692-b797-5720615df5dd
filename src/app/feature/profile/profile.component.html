<section>
  <div class="wrapper profile-wrapper">
    <man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON><PERSON> profil' }]"></man-breadcrumb>

    <div class="profile-header">
      <h1 class="profile-header-title"><PERSON><PERSON><PERSON><PERSON><PERSON> profil</h1>
      <div class="profile-header-settings" routerLink="/profil/beallitasok">Beállítások</div>
    </div>
    <ng-container *ngIf="data$ | async as data">
      <div class="profile-container">
        <div class="profile-grid-2">
          <div class="profile-grid-item profile-data">
            <h2 class="profile-title">Adatok</h2>
            <div class="profile-data-box">
              <div class="profile-data-box-wrapper">
                <div *ngIf="user?.lastName && user?.firstName" class="profile-top">
                  {{ user?.lastName + ' ' + user?.firstName }}
                </div>
                <div class="profile-top">
                  <span class="profile-top-nickname">{{ user?.username ?? 'Nincs becenév beállítva' }}</span>
                  <span class="profile-top-divider">|</span>
                  <span class="profile-top-email">{{ user?.email }}</span>
                </div>
                <div class="profile-count">
                  <div class="profile-count-item">
                    <div class="profile-count-item-number">{{ data.readArticleCount }}</div>
                    <div class="profile-count-item-text">elolvasott cikk</div>
                  </div>
                  <div class="profile-count-item">
                    <div class="profile-count-item-number">{{ data.savedArticleCount }}</div>
                    <div class="profile-count-item-text">mentett cikk</div>
                  </div>
                  <div class="profile-count-item">
                    <div class="profile-count-item-number">{{ data.commentCount }}</div>
                    <div class="profile-count-item-text">hozzászólás</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="profile-grid-item profile-subscription">
            <h2 class="profile-title">Mandiner-előfizetés</h2>
            <div *ngIf="user?.hasValidSubscription" class="profile-subscription-box">
              <div class="profile-subscription-box-title">
                <ng-container *ngIf="user?.subscription?.product?.isDigital">Digitális</ng-container>
                <ng-container *ngIf="!user?.subscription?.product?.isDigital">Nyomtatott és digitális</ng-container>
                előfizetés
                <span class="profile-subscription-box-title-span">{{ getPeriodForPrice(user?.subscription?.product?.periodInDay) }}</span>
              </div>
              <div class="profile-subscription-box-list-wrapper">
                <ul class="profile-subscription-box-list">
                  <li class="profile-subscription-box-list-item">Korlátlan online elérés, exkluzív tartalmak</li>
                  <li class="profile-subscription-box-list-item">Személyre szabott értesítések</li>
                  <li class="profile-subscription-box-list-item">Cikkek reklámok nélkül</li>
                </ul>
              </div>
              <div class="profile-subscription-box-deadline">Érvényes {{ user?.subscription?.endDate }}-ig *</div>
              <div class="profile-subscription-box-disclaimer">
                <span *ngIf="user?.subscription?.isRecurring"> * Az előfizetés az időszak végén automatikusan megújul. </span>
                <span *ngIf="!user?.subscription?.isRecurring"> * Az előfizetés lemondva, így az időszak végén nem újul meg. </span>
              </div>
            </div>
            <div *ngIf="!user?.hasValidSubscription" class="profile-subscription-box-not-subscribed">
              <div class="profile-subscription-box-deadline">Jelenleg nincs érvényes előfizetése.</div>
              <div class="profile-subscription-box-deadline">Fizessen elő, hogy Ön is élvezhesse az előfizetés előnyeit:</div>
              <div class="profile-subscription-box-list-wrapper">
                <ul class="profile-subscription-box-list">
                  <li class="profile-subscription-box-list-item">Korlátlan online elérés, exkluzív tartalmak</li>
                  <li class="profile-subscription-box-list-item">Személyre szabott értesítések</li>
                  <li class="profile-subscription-box-list-item">Cikkek reklámok nélkül</li>
                </ul>
              </div>
              <div class="profile-subscription-box-not-subscribed-button-wrapper">
                <man-simple-button (click)="redirectToSubscriptions()" color="dark">Tovább az előfizetésekhez </man-simple-button>
              </div>
            </div>
          </div>
        </div>
        <man-simple-button class="profile-settings-button" color="light" routerLink="/profil/beallitasok">Beállítások módosítása </man-simple-button>
        <div class="profile-grid-3">
          <div class="profile-grid-item profile-authors">
            <man-profile-authors
              (changeAuthor)="onChangeFollowedAuthor($event)"
              [authors]="(followedAuthors$ | async) ?? []"
              [loadingSlug]="loadingFollowedAuthorSlug"
            ></man-profile-authors>
          </div>
          <div class="profile-grid-item profile-categories">
            <man-profile-categories
              (changeCategory)="onChangeFollowedColumn($event)"
              [data]="data.allColumns"
              [loadingSlug]="loadingFollowedCategorySlug"
            ></man-profile-categories>
          </div>
          <div class="profile-grid-item profile-reading-history">
            <man-profile-reading-history [articles]="data.readingHistory"></man-profile-reading-history>
          </div>
        </div>
        <div class="profile-grid-item profile-saved-articles">
          <man-profile-saved-articles [articles]="data.savedArticles"></man-profile-saved-articles>
        </div>
        <div class="profile-grid-item profile-comments">
          <man-profile-comments [commentsWithArticle]="data.lastComments"></man-profile-comments>
        </div>
      </div>
    </ng-container>
  </div>
</section>
