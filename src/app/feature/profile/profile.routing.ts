import { Routes } from '@angular/router';
import { ProfileComponent } from './profile.component';
import { ProfileSettingsComponent } from './components/profile-settings/profile-settings.component';
import { ProfileResolver } from './profile.resolver';
import { ReadingHistoryComponent } from './components/reading-history/reading-history.component';
import { ReadHistoryResolver } from './components/reading-history/reading-history.resolver';
import { ProfileLoginDataSettingsComponent } from './components/profile-login-data-settings/profile-login-data-settings.component';
import { ProfileConsentSettingsComponent } from './components/profile-consent-settings/profile-consent-settings.component';
import { ProfileAccountDeleteComponent } from './components/profile-account-delete/profile-account-delete.component';

export const profileRouting: Routes = [
  {
    path: '',
    component: ProfileComponent,
    resolve: {
      data: ProfileResolver,
    },
    providers: [ProfileResolver],
  },
  {
    path: 'beallitasok',
    children: [
      {
        path: '',
        component: ProfileSettingsComponent,
      },
      {
        path: 'belepesi-adatok',
        component: ProfileLoginDataSettingsComponent,
      },
      {
        path: 'nyilatkozatok',
        component: ProfileConsentSettingsComponent,
      },
      {
        path: 'torles',
        component: ProfileAccountDeleteComponent,
      },
    ],
  },
  {
    path: 'elozmenyek',
    component: ReadingHistoryComponent,
    resolve: {
      data: ReadHistoryResolver,
    },
    data: {
      resolveMethod: 'getReadingHistoryArticles',
    },
    providers: [ReadHistoryResolver],
  },
  {
    path: 'mentett-cikkek',
    component: ReadingHistoryComponent,
    resolve: {
      data: ReadHistoryResolver,
    },
    data: {
      resolveMethod: 'getSavedArticles',
    },
    providers: [ReadHistoryResolver],
  },
  {
    path: 'hozzaszolasok',
    loadChildren: () => import('./user-comments/user-comments.routing').then((m) => m.userCommentsRouting),
  },
];
