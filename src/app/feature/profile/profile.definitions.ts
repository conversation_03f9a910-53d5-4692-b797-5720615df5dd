import { ArticleAuthor, ArticleCard, CommentWithArticle, FollowedColumn } from '@trendency/kesma-ui';

export interface ProfilePageData {
  followedAuthors: <AUTHORS>
  firstThreeAuthors: <AUTHORS>
  allColumns: FollowedColumn[];
  readingHistory: ArticleCard[];
  savedArticles: ArticleCard[];
  savedArticleCount: number;
  readArticleCount: number;
  commentCount: number;
  lastComments: CommentWithArticle[];
}

export interface BackendProfileArticleData {
  column: {
    title: string;
    slug: string;
  };
  date: string;
  excerpt: string;
  lead: string;
  publishDate: string;
  slug: string;
  thumbnailUrl: string;
  title: string;
  isVideo: boolean;
  isAdultsOnly: boolean;
  hasGallery: boolean;
  hasDossiers: string;
  isPaywalled: boolean;
  likeCount: number;
  dislikeCount: number;
  commentCount: number;
  isCommentsDisabled: boolean;
  isLikesAndDislikesDisabled: boolean;
}

export interface ProfileLoginDataFormData {
  username: string;
  newPassword: string;
  oldPassword: string;
}

export interface ProfilePersonalDataFormData {
  billingAddress?: ProfilePersonalDataAddressFormData;
  shippingAddress?: ProfilePersonalDataAddressFormData;
  isShippingAddressSame?: boolean;
  phoneNumber?: string;
  oldPassword: string;
}

export interface ProfilePersonalDataAddressFormData {
  name: string;
  zip: string;
  city: string;
  address: string;
  tax?: string;
}

export interface BackendProfileEditRequest {
  userName?: string;
  passwordNew?: string;
  passwordOld?: string;
  userDetails?: {
    invoiceName?: string;
    invoiceZip?: string;
    invoiceCity?: string;
    invoiceAddress?: string;
    shippingName?: string;
    shippingZip?: string;
    shippingCity?: string;
    shippingAddress?: string;
    taxNumber?: string;
    phoneNumber?: string;
  };
}

export interface ProfileLoginSettingsErrorValue {
  readonly errors: Array<string>;
}
