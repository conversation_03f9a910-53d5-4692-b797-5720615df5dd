import { Injectable } from '@angular/core';

import { forkJoin, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService, AuthorData, AuthService, SecureApiService } from '../../shared';
import { CommentService } from '../comment-section/api/comment.service';
import { ProfilePageData } from './profile.definitions';
import { ArticleAuthor, FollowedColumn, PrimaryColumn } from '@trendency/kesma-ui';
import { AuthorService } from '../author-page/api/author.service';

@Injectable()
export class ProfileResolver {
  public constructor(
    private readonly apiService: ApiService,
    private readonly secureApiService: SecureApiService,
    private readonly commentService: CommentService,
    private readonly authService: AuthService,
    private readonly authorService: AuthorService
  ) {}

  public resolve(): Observable<ProfilePageData> {
    return forkJoin([
      this.secureApiService.getFollowedAuthors(),
      this.authorService.getAuthorsForPage$(0, 3, true),
      this.apiService.getParentColumns(),
      this.secureApiService.getReadingHistory(0, 5),
      this.secureApiService.getSavedArticlesList(0, 3),
      this.secureApiService.getSavedArticleCount(),
      this.secureApiService.getReadArticleCount(),
      this.secureApiService.getMyCommentCount(),
      this.commentService.getMyComments(0, 3),
    ]).pipe(
      map(([followedAuthors, firstThreeAuthors, columns, readingHistory, savedArticles, savedArticleCount, readArticleCount, commentCount, comments]) => ({
        followedAuthors: <AUTHORS>
        firstThreeAuthors: <AUTHORS>
        allColumns: columns.data ? this.mapColumnTree(columns.data) : [],
        readingHistory: readingHistory.data ?? [],
        savedArticles: savedArticles.data ?? [],
        savedArticleCount: savedArticleCount.data?.savedArticleCount ?? 0,
        readArticleCount: readArticleCount.data?.readArticlesCount ?? 0,
        commentCount: commentCount.data?.commentCount ?? 0,
        lastComments: comments.data ?? [],
      }))
    );
  }

  mapColumnTree(columns: PrimaryColumn[]): FollowedColumn[] {
    const followedColumns = this.authService.currentUserExtraData?.followedColumns ?? [];
    return columns.map(
      (column: PrimaryColumn) =>
        ({
          ...column,
          isFollowed: !!followedColumns.find((followedColumnSlug: string) => followedColumnSlug === column.slug),
        }) as FollowedColumn
    );
  }

  mapAuthorDataToArticleAuthor(data: AuthorData): ArticleAuthor {
    return {
      name: data.name ?? '',
      avatar: data.avatar.fullSizeUrl,
      slug: data.slug,
    };
  }
}
