import { ApiResponseMetaList, Api<PERSON><PERSON><PERSON>, FakeBool } from '@trendency/kesma-ui';

export type UserCommentsType = {
  title?: string;
  publishDate?: string;
  fullSizeUrl?: string;
  column: string;
  lead: string;
  slug: string;
  commentCount: string;
  columnSlug: string;
  isPaywalled?: FakeBool;
  hasGallery?: FakeBool;
  isVideo?: FakeBool;
  comments: {
    id: string;
    createdAt: string;
    commentText: string;
  }[];
};

export type UserCommentsResponse = ApiResult<UserCommentsType[], ApiResponseMetaList>;

export type UserStats = {
  areAlertsEnabled?: boolean;
  commentCount: number;
  likeCount: number;
  dislikeCount: number;
};

export type AlertConfig = {
  alertOnLike: boolean;
  alertOnAnswer: boolean;
};

export type PublicUserCommentCount = {
  commentCount: number;
};
