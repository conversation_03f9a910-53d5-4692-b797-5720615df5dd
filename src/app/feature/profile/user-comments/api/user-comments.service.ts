import { Injectable } from '@angular/core';
import { SecureApiService } from '../../../../shared/services/secure-api.service';
import {
  ArticleCard,
  mapBackendArticleDataToArticleCardWithHideThumbnail,
  Ordering,
  UserCommentsResponse,
  UserCommentsType,
  UserStats,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { ApiService, AuthService, getSortingQuery, ISearchParams } from '../../../../shared';
import { map, switchMap } from 'rxjs/operators';
import { CommentService } from '../../../comment-section/api/comment.service';

@Injectable()
export class UserCommentsService {
  constructor(
    private readonly secureApi: SecureApiService,
    private readonly auth: AuthService,
    private readonly publicApi: ApiService,
    private readonly commentService: CommentService
  ) {}

  getMyComments(
    userId?: string,
    global_filter?: string,
    sort?: Ordering,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    page = 0,
    rowCount_limit = 20,
    material_types_only?: string
  ): Observable<UserCommentsResponse> {
    let params: ISearchParams = {
      rowCount_limit: rowCount_limit?.toString(),
      page_limit: page?.toString(),
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    };
    params = global_filter ? { ...params, global_filter } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;

    const apiCall$ = userId
      ? this.secureApi.getArticleComments(
          {
            ...getSortingQuery(sort || 'latest'),
            ...params,
          },
          userId
        )
      : this.secureApi.getMyArticleComments(
          {
            ...getSortingQuery(sort || 'latest'),
            ...params,
          },
          this.auth.currentUser?.uid || ''
        );

    return apiCall$.pipe(
      map((response) => {
        return {
          ...response,
          data: [
            ...response.data.map((commentedArticle: UserCommentsType & { thumbnail?: string | { url: string } }) => ({
              ...commentedArticle,
              thumbnail: {
                url: commentedArticle.thumbnail?.url || (commentedArticle.thumbnail as string) || '',
              },
            })),
          ],
        };
      })
    );
  }

  getCommentWars(days: number): Observable<ArticleCard[]> {
    return this.publicApi.getCommentWars(days).pipe(map((r) => r.map((article) => mapBackendArticleDataToArticleCardWithHideThumbnail(article))));
  }

  getMyStats(): Observable<UserStats> {
    return this.secureApi.getMyStats().pipe(
      switchMap(({ data: likeCount }) =>
        this.commentService.getMyComments(0, 0).pipe(
          map(({ meta: commentMeta }) => ({
            ...likeCount,
            commentCount: commentMeta.limitable?.rowAllCount || 0,
          })),
          switchMap((userData) => {
            return this.getAlertConfig().pipe(
              map((areAlertsEnabled) => ({
                ...userData,
                areAlertsEnabled,
              }))
            );
          })
        )
      )
    );
  }

  getAlertConfig(): Observable<boolean> {
    return this.secureApi.getAlertConfig().pipe(map(({ data }) => data.alertOnAnswer));
  }

  updateAlertConfig(alertOnAnswer: boolean): Observable<never> {
    return this.secureApi.updateAlertConfig({
      alertOnAnswer: alertOnAnswer,
      alertOnLike: false,
    });
  }
}
