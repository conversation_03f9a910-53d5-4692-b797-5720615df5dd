@use '../../../../../../../scss/shared' as *;

:host {
  font-family: var(--kui-font-primary);
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  border: 3px solid var(--kui-orange-600);
  display: flex;
  height: 354px;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding: 15px;

  @include media-breakpoint-up(md) {
    margin-top: 28px;
  }

  & > * {
    width: fit-content;
    text-align: center;
    margin: 0 auto;
  }
}

.username {
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
}

.stat {
  h2 {
    font-weight: 700;
    font-size: 32px;
    line-height: 32px;
  }

  span {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
  }
}
