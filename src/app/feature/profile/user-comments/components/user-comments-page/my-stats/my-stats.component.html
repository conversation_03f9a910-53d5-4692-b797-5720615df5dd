<div class="username">{{ (auth.currentUserSubject | async)?.username }}</div>
<div class="description"><PERSON>zen a néven jelennek meg a hozzászólásai.</div>
<div class="stat">
  <h2>{{ stats.commentCount }}</h2>
  <span>hozzászólás</span>
</div>
<div class="stat">
  <h2>{{ stats.likeCount }}</h2>
  <span>Tetszik száma</span>
</div>
<div class="stat">
  <h2>{{ stats.dislikeCount }}</h2>
  <span>Nem tetszik száma</span>
</div>
