import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { AuthService } from '../../../../../../shared';
import { UserStats } from '../../../api/user-comments.definitions';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-my-stats',
  templateUrl: './my-stats.component.html',
  styleUrls: ['./my-stats.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe],
})
export class MyStatsComponent {
  @Input() stats: UserStats;

  @Input() notifications: boolean;
  @Output() notificationsChanged = new EventEmitter<boolean>();
  constructor(public readonly auth: AuthService) {}

  handleNotificationsChange(): void {
    this.notifications = !this.notifications;
    this.notificationsChanged.emit(this.notifications);
  }
}
