import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { createCanonicalUrlForPageablePage, Ordering, User } from '@trendency/kesma-ui';
import { UserCommentsResponse } from '../../api/user-comments.definitions';
import { Observable } from 'rxjs';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { map, switchMap, tap } from 'rxjs/operators';
import { UserCommentsService } from '../../api/user-comments.service';
import {
  ArticleCardType,
  AuthService,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  ManSpinnerComponent,
  Paginator,
  SearchFilterDataService,
  SearchFilterDefinitions,
} from '../../../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleMyCommentsComponent } from './article-with-my-comments/article-my-comments.component';
import { MyStatsComponent } from './my-stats/my-stats.component';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';

@Component({
  templateUrl: './user-comments-page.component.html',
  styleUrls: ['./user-comments-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    MyStatsComponent,
    NgFor,
    ArticleMyCommentsComponent,
    AsyncPipe,
    ManBreadcrumbComponent,
    ManArticleCardComponent,
    ManSimpleButtonComponent,
    ManSearchFilterComponent,
    ManSpinnerComponent,
  ],
  providers: [UserCommentsService],
})
export class UserCommentsPageComponent implements OnInit {
  readonly COMMENT_WARS_TYPE = ArticleCardType.BlackBgImgTitleMeta as const;
  readonly commentWars$ = this.service.getCommentWars(2);

  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((routeData) => routeData['data']['filterColumns']));
  publicUser$: Observable<User | null> = this.route.data.pipe(map((routeData) => routeData['data']['publicUser']));
  commentCount$: Observable<number> = this.route.data.pipe(map((routeData) => routeData['data']['commentCount']));

  ordering: Ordering = 'latest';
  filterValues: FilterValues = {};
  filterOpen = false;

  readonly paginator = new Paginator((page) => {
    const timeframe = this.searchFilterDataService.setSearchQuery(this.filterValues);
    return this.publicUser$.pipe(
      switchMap((publicUser: User | null) =>
        this.service
          .getMyComments(
            publicUser?.uid || undefined,
            this.filterValues.keyword,
            this.ordering,
            timeframe.fromDate,
            timeframe.toDate,
            this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).contentTypes,
            this.filterValues.columns,
            page,
            20,
            this.searchFilterDataService.setcontentTypes(this.filterValues.contentTypes).ownMaterial
          )
          .pipe(
            tap((data: UserCommentsResponse) => {
              this.setUrl(timeframe.fromDate, timeframe.toDate);
              this.setMetaData(publicUser);
              return data;
            })
          )
      )
    );
  });
  userStats$ = this.service.getMyStats();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly service: UserCommentsService,
    private readonly searchFilterDataService: SearchFilterDataService,
    private readonly seo: SeoService,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  get username(): string {
    return this.authService.currentUser?.username || '';
  }

  ngOnInit(): void {
    this.subscribeToResolverDataChange();
  }

  loadMore(): void {
    this.paginator.next();
  }

  handleOrderChange(): void {
    this.paginator.reset();
  }

  private subscribeToResolverDataChange(): void {
    this.filterValues = {
      ...this.filterValues,
      keyword: this.route.snapshot.queryParamMap.get('global_filter') as string,
    };
    this.paginator.reset();
  }

  private setUrl(from_date?: string | Date, to_date?: string | Date): void {
    let queryParams: Params = {};
    queryParams = this.filterValues.keyword
      ? {
          ...queryParams,
          global_filter: this.filterValues.keyword,
        }
      : queryParams;
    queryParams = from_date ? { ...queryParams, from_date } : queryParams;
    queryParams = to_date ? { ...queryParams, to_date } : queryParams;
    queryParams = this.filterValues?.contentTypes
      ? {
          ...queryParams,
          'contentTypes[]': this.filterValues.contentTypes,
        }
      : queryParams;
    queryParams = this.filterValues?.columns?.length
      ? {
          ...queryParams,
          'columns[]': this.filterValues.columns,
        }
      : queryParams;
    this.router
      .navigate([], {
        relativeTo: this.route,
        queryParams: queryParams,
      })
      .then();
  }

  private setMetaData(publicUser: User | null): void {
    const title = createMandinerTitle(publicUser ? `${publicUser.username} hozzászólásai` : 'Profil - Hozzászólásaim');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage(publicUser ? `profil/hozzaszolasok/${publicUser.uid}` : 'profil/hozzaszolasok');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  handleFilterEvent(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.paginator.reset();
  }

  toggleFilter(): void {
    this.filterOpen = !this.filterOpen;
  }

  handleNotificationsChanged(notifications: boolean): void {
    this.service.updateAlertConfig(notifications).subscribe();
  }
}
