<section>
  <div class="wrapper">
    <ng-container *ngIf="publicUser$ | async as publicUser; else noPublicUser">
      <man-breadcrumb [items]="[{ label: publicUser.username ?? '' }]"></man-breadcrumb>
      <div class="public-user-header">
        <h2>{{ publicUser.username }}</h2>
        <h3>Összesen {{ commentCount$ | async }} komment</h3>
      </div>
    </ng-container>
    <ng-template #noPublicUser>
      <man-breadcrumb [items]="[{ label: 'Személyes profil', url: '/profil' }, { label: 'Hozzászólásaim' }]"></man-breadcrumb>
      <section class="row">
        <div class="col-12 col-lg-3 stats">
          <div class="my-stats">
            <app-my-stats
              *ngIf="userStats$ | async as stats; else loading"
              [stats]="stats"
              (notificationsChanged)="handleNotificationsChanged($event)"
            ></app-my-stats>
          </div>
        </div>
        <div class="col-12 col-lg-9">
          <h2 class="comment-wars-title">Kommentcsata</h2>
          <div class="comment-wars-block">
            <ng-container *ngIf="commentWars$ | async as commentWars; else loading">
              <man-article-card *ngFor="let article of commentWars ?? []" [styleID]="COMMENT_WARS_TYPE" [data]="article"></man-article-card>
            </ng-container>
          </div>
        </div>
      </section>
    </ng-template>

    <section class="col filter">
      <h2 *ngIf="(publicUser$ | async) === null">Hozzászólásaim</h2>
      <div class="row">
        <div class="filter-button">
          <man-simple-button color="link" (click)="toggleFilter()">
            <span>
              Szűrés
              <i class="icon icon-man-filter"></i>
            </span>
          </man-simple-button>
        </div>
      </div>
      <man-search-filter
        *ngIf="filterOpen"
        [showSearchBar]="true"
        [showToggleButton]="false"
        [showSorting]="false"
        [resultCount]="(paginator.data$ | async)?.length ?? 0"
        [data]="(searchFilterColumns$ | async) || undefined"
        [defaultFilterValues]="filterValues"
        [active]="true"
        (filterEvent)="handleFilterEvent($event)"
      ></man-search-filter>
      <div class="col comments">
        <ng-container *ngIf="(paginator.isLoading$ | async) === false; else loading">
          <app-article-my-comments
            *ngFor="let data of (paginator.data$ | async) ?? []"
            [data]="data"
            [isOwnComments]="(publicUser$ | async) === null"
            [username]="(publicUser$ | async)?.username || username"
          ></app-article-my-comments>
          <man-simple-button color="primary" *ngIf="paginator.hasMore$ | async" (click)="loadMore()">Korábbi hozzászolások megjelenítése » </man-simple-button>
        </ng-container>
        <div class="spacer"></div>
      </div>
    </section>

    <ng-template #loading>
      <man-spinner></man-spinner>
    </ng-template>
  </div>
</section>
