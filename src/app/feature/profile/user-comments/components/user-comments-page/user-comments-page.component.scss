@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  margin: 20px;
}

.public-user-header {
  h2 {
    font-family: var(--kui-font-primary);
    color: var(--kui-orange-600);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin: 40px 0 20px;
  }

  h3 {
    font-family: var(--kui-font-primary);
    color: var(--kui-black);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin: 0 0 40px;
  }
}

.filter {
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 16px;
  color: var(--kui-black);

  .row {
    margin-bottom: 20px;
  }

  .col {
    border-top: 1px solid var(--kui-gray-100);
    padding-top: 20px;

    article {
      margin-bottom: 60px;
    }
  }

  h2 {
    color: var(--kui-orange-600);
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 24px;
    margin-bottom: 40px;

    @include media-breakpoint-down(md) {
      margin: 20px 0;
    }
  }

  select {
    @extend .filter;
    background: var(--kui-white);
    border: none;
    margin-left: -4px;
    width: fit-content;
  }

  &-button {
    width: fit-content;

    span {
      @extend .filter;
      display: flex;

      .icon {
        width: 16px;
        height: 16px;
        margin-left: 5px;
      }
    }
  }
}

.comments {
  display: flex;
  flex-direction: column;
  gap: 60px;
  font-size: initial;
  line-height: initial;
}

.my-stats {
  width: fit-content;
  margin: 57px auto 20px auto;
}

.comment-wars {
  &-title {
    margin: 20px 0 10px 0;
    color: var(--kui-orange-600);
    font-family: var(--kui-font-primary);
  }

  &-block {
    padding: 20px;
    display: flex;
    gap: 20px;
    min-height: 354px;
    background-color: var(--kui-black);
    width: 100%;
    max-width: fit-content;
    transition: 0.3s ease-in-out all;

    man-spinner {
      width: 900px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      height: auto;
      justify-content: center;
    }

    man-article-card {
      width: 282px;
      @include media-breakpoint-down(sm) {
        width: 100%;
        min-width: 282px;
      }
    }
  }
}
