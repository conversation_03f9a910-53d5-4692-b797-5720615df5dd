.comment {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;

  h4 {
    font-family: var(--kui-font-primary);
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
  }

  span {
    color: var(--kui-gray-390);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }

  p {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
}

.to-comments {
  color: var(--kui-orange-600);
  font-weight: 700;
  font-size: 12px;
  line-height: 15px;

  i {
    width: 8px;
    height: 8px;
  }
}

man-simple-button {
  margin: 0 0 1rem 0;
}

.rotate-90 {
  transform: rotate(90deg);
}
