import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { UserCommentsType } from '../../../api/user-comments.definitions';
import { BehaviorSubject } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { ArticleCard, backendDateToDate, buildArticleUrl } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';
import { NgIf, NgFor, AsyncPipe } from '@angular/common';
import { ArticleCardType, ManArticleCardComponent, ManSimpleButtonComponent } from '../../../../../../shared';

const COMMENT_LIMIT_STEP = 3;

@Component({
  selector: 'app-article-my-comments',
  templateUrl: './article-my-comments.component.html',
  styleUrls: ['./article-my-comments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, AsyncPipe, FormatPipeModule, ManArticleCardComponent, ManSimpleButtonComponent],
})
export class ArticleMyCommentsComponent {
  readonly ARTICLE_CARD_TYPE = ArticleCardType.ImgRightTitleLeadDateMeta;

  @Input() set data(value: UserCommentsType) {
    this.data$.next(value);
  }

  @Input() isOwnComments = true;
  @Input() username = '';

  data$ = new BehaviorSubject<UserCommentsType | undefined>(undefined);
  commentLimit$ = new BehaviorSubject<number>(COMMENT_LIMIT_STEP);
  hasMoreComments$ = this.data$.pipe(switchMap((data) => this.commentLimit$.pipe(map((limit) => (data?.comments?.length ?? 0) > limit))));
  comments$ = this.data$.pipe(
    switchMap((data) =>
      this.commentLimit$.pipe(
        map(
          (limit) =>
            data?.comments?.slice(0, limit).map((comment) => ({
              ...comment,
              createdAt: backendDateToDate(comment.createdAt),
            })) ?? []
        )
      )
    )
  );

  loadMore(): void {
    if (!this.data$.value) {
      return;
    }

    const newLimit = this.commentLimit$.value + COMMENT_LIMIT_STEP;
    if (newLimit > this.data$.value.comments.length) {
      this.commentLimit$.next(this.data$.value.comments.length);
      return;
    }

    this.commentLimit$.next(newLimit);
  }

  getUrl(): string[] {
    const article = this.toArticleCard(this.data$.value);
    return article ? buildArticleUrl(article, article.columnSlug) : [];
  }

  toArticleCard(userComment?: UserCommentsType): ArticleCard | undefined {
    if (!userComment) {
      return undefined;
    }

    return {
      ...userComment,
      commentCount: +userComment.commentCount,
      isPaywalled: userComment.isPaywalled ? !!+userComment.isPaywalled : false,
      hasGallery: userComment.hasGallery ? !!+userComment.hasGallery : false,
      isVideoType: userComment.isVideo ? !!+userComment.isVideo : false,
    } as ArticleCard;
  }
}
