import { BackendProfileEditRequest, ProfileLoginDataFormData, ProfilePersonalDataFormData } from './profile.definitions';

export function profileLoginDataFormToBackendRequest(formData: ProfileLoginDataFormData): BackendProfileEditRequest {
  return {
    userName: formData.username,
    passwordNew: formData.newPassword,
    passwordOld: formData.oldPassword,
  };
}

export function profilePersonalDataFormToBackendRequest(formData: ProfilePersonalDataFormData): BackendProfileEditRequest {
  return {
    passwordOld: formData.oldPassword,
    userDetails: {
      invoiceName: formData.billingAddress?.name,
      invoiceZip: formData.billingAddress?.zip,
      invoiceCity: formData.billingAddress?.city,
      invoiceAddress: formData.billingAddress?.address,
      shippingName: formData?.isShippingAddressSame ? formData?.billingAddress?.name : formData?.shippingAddress?.name,
      shippingZip: formData?.isShippingAddressSame ? formData?.billingAddress?.zip : formData?.shippingAddress?.zip,
      shippingCity: formData?.isShippingAddressSame ? formData?.billingAddress?.city : formData?.shippingAddress?.city,
      shippingAddress: formData?.isShippingAddressSame ? formData?.billingAddress?.address : formData?.shippingAddress?.address,
      taxNumber: formData?.billingAddress?.tax ?? undefined,
      phoneNumber: formData.phoneNumber,
    },
  };
}
