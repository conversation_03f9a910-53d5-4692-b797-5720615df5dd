import { DragDropModule } from '@angular/cdk/drag-drop';
import { Ng<PERSON>orO<PERSON>, NgIf, NgS<PERSON>, Ng<PERSON><PERSON>Case, NgSwitchDefault, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { SeoService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementPlaceholderComponent,
  LayoutEditorComponent as KesmaLayoutEditorComponent,
  LayoutContentItemWrapperComponent,
  LayoutEditorButtonFactoryService,
  LayoutElementContentType,
  LayoutPageType,
} from '@trendency/kesma-ui';
import { ManDossierRepeaterCardComponent, ManDossierRepeaterComponent } from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-layout-editor',
  templateUrl: './layout-editor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgSelectModule,
    DragDropModule,
    ManDossierRepeaterComponent,
    ManDossierRepeaterCardComponent,
    LayoutComponent,
    KesmaLayoutEditorComponent,
    AdvertisementPlaceholderComponent,
    NgSwitch,
    NgSwitchCase,
    LayoutContentItemWrapperComponent,
    NgForOf,
    NgSwitchDefault,
    NgTemplateOutlet,
    NgIf,
  ],
  providers: [LayoutEditorButtonFactoryService],
})
export class LayoutEditorComponent implements OnInit {
  readonly seoService = inject(SeoService);
  readonly utilService = inject(UtilService);
  readonly buttonFactory = inject(LayoutEditorButtonFactoryService);

  isBrowser = this.utilService.isBrowser();
  contentItemButtons = [this.buttonFactory.editOverrideBtnFactory];

  readonly LayoutElementContentType = LayoutElementContentType;
  readonly LayoutPageType = LayoutPageType;

  ngOnInit(): void {
    this.seoService.setMetaData({
      robots: 'noindex, nofollow',
    });
  }
}
