import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EmbeddingService, SeoService } from '@trendency/kesma-core';
import { ArticleBodyType, ArticleFileLinkDirective, createCanonicalUrlForPageablePage, LayoutApiData } from '@trendency/kesma-ui';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { CustomStaticPageType, IComponentData, StaticPageResponse } from '../../api/static-page.definitions';
import { OlimpiaService } from 'src/app/shared/services/olimpia.service';
import { NgForOf, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { createMandinerTitle, ManWysiwygBoxComponent } from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';

const EB_SLUG = 'foci-eb-2024';
const OLIMPIA_SLUG = 'olimpia-2024';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [NgIf, NgForOf, NgSwitch, NgSwitchCase, ManWysiwygBoxComponent, ArticleFileLinkDirective, SidebarComponent, LayoutComponent],
})
export class StaticPageComponent implements OnInit, AfterViewInit, OnDestroy {
  public title: string;
  public body: IComponentData[];
  public staticPageResponse: StaticPageResponse;
  public customStaticPageType: CustomStaticPageType;
  public layoutApiData: LayoutApiData;
  readonly CustomStaticPageType = CustomStaticPageType;

  isEBCustomPage = false;
  isOlimpiaCustomPage = false;
  readonly ArticleBodyType = ArticleBodyType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly embedding: EmbeddingService,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly olimpiaService: OlimpiaService
  ) {}

  ngOnInit(): void {
    this.route.data.subscribe((result: any) => {
      if (result?.staticPageData?.meta?.customStaticPageType) {
        this.customStaticPageType = result?.staticPageData?.meta?.customStaticPageType;
      } else {
        if (result?.staticPageData?.data?.body) {
          this.customStaticPageType = CustomStaticPageType.StaticPage;
        }
        if (result?.staticPageData?.data?.structure) {
          this.customStaticPageType = CustomStaticPageType.StaticPage;
        }
      }
      if (!this.customStaticPageType || this.customStaticPageType === CustomStaticPageType.StaticPage) {
        this.staticPageResponse = result?.staticPageData?.data;
        this.body = this.staticPageResponse?.body as IComponentData[];
        this.title = this.staticPageResponse?.title;
        this.isEBCustomPage = false;
      } else if (this.customStaticPageType === CustomStaticPageType.CustomPage) {
        this.title = result?.staticPageData?.meta?.customBuiltPageTitle;
        this.isEBCustomPage = result?.staticPageData?.meta?.customBuiltPageSlug === EB_SLUG;
        this.isOlimpiaCustomPage = result?.staticPageData?.meta?.customBuiltPageSlug === OLIMPIA_SLUG;
        this.layoutApiData = result?.staticPageData.data;
      }

      const canonical = createCanonicalUrlForPageablePage('', this.route.snapshot);
      if (canonical) this.seo.updateCanonicalUrl(canonical);

      const title = this.isEBCustomPage ? 'Foci EB 2024' : this.title;
      this.onSetIsOlimpia(this.isOlimpiaCustomPage);
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title: createMandinerTitle(title),
        ogTitle: this.title,
        ...(this.isEBCustomPage
          ? {
              description:
                'Foci EB 2024: Friss hírek és tudósítások a Mandiner oldalán. ' +
                'A Labdarúgó-Európa-bajnokság menetrendje, részletes bemutató a csoportokról és kiesőkről.',
              ogDescription:
                'Foci EB 2024: Friss hírek és tudósítások a Mandiner oldalán. ' +
                'A Labdarúgó-Európa-bajnokság menetrendje, részletes bemutató a csoportokról és kiesőkről.',
            }
          : {}),
      });

      this.cd.markForCheck();
    });
  }

  onSetIsOlimpia(isOlimpiaPage: boolean): void {
    if (!isOlimpiaPage) return;
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(isOlimpiaPage);
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }

  ngOnDestroy(): void {
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(false);
  }
}
