@use 'shared' as *;

.static-page {
  margin: 30px 0;

  .title {
    font-size: 29px;
    line-height: 5rem;
    color: var(--kui-orange-600);
    font-family: var(--kui-font-primary);
  }

  &-container-no-side {
    position: relative;
    background-color: var(--kui-white);

    app-layout {
      kesma-layout {
        .layout-element {
          background: var(--kui-white);
        }

        & > .layout-element:first-child {
          padding-top: 16px;
        }

        kesma-olimpia-hungarian-team {
          z-index: 1;
        }
      }
    }
  }
}

// EB
.eb-static-page {
  position: relative;
  min-height: 1018px;

  .wrapper {
    width: 1440px;
    max-width: calc(100% - 30px) !important;

    @include media-breakpoint-down(md) {
      max-width: calc(100% - 15px) !important;
    }
  }

  .static-page-container-no-side {
    padding: 24px 20px;

    @include media-breakpoint-down(md) {
      padding: 16px 0;
    }

    kesma-layout {
      margin: 0;
    }
  }

  &-background {
    position: absolute;
    border-radius: 50px;
    transform: translate(-50%);
    min-height: 352px;
    object-fit: cover;
    padding: 16px;
    left: 50%;

    &.desktop {
      @media (max-width: 1545px) {
        display: none;
      }
    }

    @include media-breakpoint-down(md) {
      border-radius: 20px;
      padding: 0 8px;
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 10;

    @media (max-width: 1545px) {
      background-image: url('/src/assets/images/eb/eb-static-background.svg');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: top center;
      border-radius: 20px;

      @include media-breakpoint-down(sm) {
        background-image: url('/src/assets/images/eb/eb-static-background-mobile.svg');
      }
    }
  }

  &-icon {
    width: 480px;
    height: 175px;
    margin-top: 30px;

    @include media-breakpoint-down(md) {
      height: 176px;
      width: 208px;
      margin-top: 0;
    }
  }
}

// OLIMPIA SPECIFIC
.olimpia-static-page {
  .wrapper {
    width: 100%;
    max-width: 1920px;
  }

  &-background {
    padding: 0;
    border-radius: unset;
    position: absolute;
    z-index: -1;
    transform: translate(-50%);
    min-height: 345px;
    object-fit: cover;
    left: 50%;

    &.background {
      &-desktop {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &-mobile {
        display: none;

        @include media-breakpoint-down(md) {
          display: block;
        }
      }
    }
  }

  &-content {
    background-image: unset;
    text-align: center;
    margin-bottom: -30px !important;
  }

  &-logo {
    width: 310px;
    height: 48px;
    margin: 66px 0;

    @include media-breakpoint-down(md) {
      width: 180px;
      height: 28px;
      margin: 56px 0;
    }
  }
}
