<section class="static-page" *ngIf="!customStaticPageType || customStaticPageType === CustomStaticPageType.StaticPage">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="heading-line">
        <h1 class="title">{{ title }}</h1>
      </div>
      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
            <ng-container *ngFor="let wysiwygDetail of element?.details">
              <man-wysiwyg-box [html]="$any(wysiwygDetail)?.value || ''" trArticleFileLink></man-wysiwyg-box>
            </ng-container>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>

    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<section *ngIf="customStaticPageType === CustomStaticPageType.CustomPage" [class.eb-static-page]="isEBCustomPage">
  <ng-container *ngIf="isEBCustomPage">
    <img class="eb-static-page-background desktop" src="../../../../../assets/images/eb/eb-static-background.svg" alt="" />
    <div class="wrapper eb-static-page-content">
      <img class="eb-static-page-icon" src="../../../../../assets/images/eb/eb-logo.svg" alt="EB 2024" />
    </div>
  </ng-container>

  <ng-container *ngIf="isOlimpiaCustomPage">
    <img
      src="../../../../../assets/images/olimpia/olimpia-static-background.png"
      alt="Olimpia 2024"
      class="olimpia-static-page-background background-desktop"
    />
    <img
      src="../../../../../assets/images/olimpia/olimpia-static-background-mobile.png"
      alt="Olimpia 2024"
      class="olimpia-static-page-background background-mobile"
    />

    <div class="wrapper olimpia-static-page-content">
      <img src="../../../../../assets/images/olimpia/olimpia-logo-white.svg" alt="Olimpia 2024" class="olimpia-static-page-logo" />
    </div>
  </ng-container>

  <div class="wrapper">
    <div class="static-page-container-no-side">
      <app-layout *ngIf="layoutApiData" [structure]="layoutApiData.struct" [configuration]="layoutApiData.content"> </app-layout>
    </div>
  </div>
</section>
