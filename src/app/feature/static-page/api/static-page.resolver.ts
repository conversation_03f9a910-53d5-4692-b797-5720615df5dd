import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, switchMap, throwError } from 'rxjs';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { StaticPageService } from './static-page.service';
import { environment } from '../../../../environments/environment';
import type { Response } from 'express';
import { catchError, map } from 'rxjs/operators';
import { ApiService, ArticleService } from '../../../shared';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class StaticPageResolver {
  constructor(
    @Inject(RESPONSE) @Optional() private readonly response: Response,
    private readonly staticPageService: StaticPageService,
    private readonly utilsService: UtilService,
    private readonly articleService: ArticleService,
    private readonly seoService: SeoService,
    private readonly router: Router,
    private readonly apiService: ApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    const previewHash = route.queryParams['previewHash'];
    const param = route.params['slug'];
    const request$ = previewHash
      ? this.staticPageService.getStaticPagePreview(param, previewHash)
      : param
        ? this.apiService.getArticlesByFoundationTag(param)
        : of({});
    return request$.pipe(
      switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
        if (param && (searchResult?.data ?? []).length === 0) {
          return this.staticPageService.getStaticPage(param).pipe(
            catchError((error) => {
              return this.redirectStaticPageUrls(error);
            })
          );
        }
        return of(searchResult);
      })
    );
  }

  private redirectStaticPageUrls(error: Error): Observable<any> {
    const currentUrl = this.seoService.currentUrl;
    return this.articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      map(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          window.location.href = url;
        } else if (url && this.response) {
          this.response.status(301);
          // port is missing from the response and `process.env.PORT` reflects
          // the devserver's port when running w/ local devserver -> replacing w/ the predefined
          if (url.match(/^https?:\/\/localhost\//)) {
            url = url.replace(/^https?:\/\/localhost/, environment.siteUrl as string);
          }
          this.response.setHeader('location', url);
        } else if (url === null) {
          this.router
            .navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            })
            .then();
          return throwError(() => error);
        }
        return of({});
      })
    );
  }
}
