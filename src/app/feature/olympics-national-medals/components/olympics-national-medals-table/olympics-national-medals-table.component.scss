@use 'shared' as *;

:host {
  display: block;

  .wrapper {
    gap: 24px;
  }

  .left-column {
    display: flex;
    flex-direction: column;

    man-breadcrumb {
      margin-bottom: 20px;
    }

    kesma-olimpia-page-banner {
      margin-bottom: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }
    }

    .title {
      color: var(--kui-black);
      font-size: 28px;
      font-weight: 700;
      line-height: 34px;
      margin-top: 10px;
      margin-bottom: 26px;

      @include media-breakpoint-down(md) {
        margin-bottom: 8px;
      }
    }

    kesma-olimpia-national-medals-table {
      margin-bottom: 10px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }

      ::ng-deep {
        .no-medals {
          font-family: var(--kui-font-opensans);

          .hungarian-team-link {
            border-radius: 0;
            font-weight: 400;
            padding-top: 6px;
            padding-bottom: 6px;
            font-style: normal;
          }
        }
      }
    }

    kesma-olimpia-navigator {
      margin: 32px 0;

      @include media-breakpoint-down(md) {
        margin: 18px 0 16px;
      }
    }
  }
}
