import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  BreadcrumbItem,
  Layout,
  LayoutPageType,
  OlimpiaMedalsNational,
  OlimpiaNationalMedalsTableComponent,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { ManBreadcrumbComponent } from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';

@Component({
  selector: 'app-olympics-national-medals-table',
  templateUrl: './olympics-national-medals-table.component.html',
  styleUrl: './olympics-national-medals-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBreadcrumbComponent,
    OlimpiaPageBannerComponent,
    OlimpiaNationalMedalsTableComponent,
    AsyncPipe,
    OlimpiaNavigatorComponent,
    StrossleAdvertComponent,
    LayoutComponent,
    NgIf,
  ],
})
export class OlympicsNationalMedalsTableComponent {
  breadcrumbItems: BreadcrumbItem[] = [{ label: 'Olimpia 2024', url: '' }];

  tableData$: Observable<OlimpiaMedalsNational[]> = this.route.data.pipe(map(({ data }) => data['nationalMedals']));
  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));

  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;

  constructor(private readonly route: ActivatedRoute) {}
}
