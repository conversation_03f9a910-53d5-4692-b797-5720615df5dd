import { ResolveFn, Router } from '@angular/router';
import { Layout, OlimpiaMedalsNational } from '@trendency/kesma-ui';
import { inject } from '@angular/core';
import { ApiService } from '../../../shared/services/api.service';
import { catchError, map, take } from 'rxjs/operators';
import { forkJoin, Observable, throwError } from 'rxjs';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';

export const olympicsNationalMedalsResolver: ResolveFn<
  Observable<{ nationalMedals: OlimpiaMedalsNational[]; olympicImportantSidebar: Layout | null }>
> = () => {
  const router = inject(Router);

  return forkJoin({
    nationalMedals: inject(ApiService)
      .getOlimpiaNationalMedals()
      .pipe(map(({ data }) => data)),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  }).pipe(
    catchError((err) => {
      router.navigate(['404'], { skipLocationChange: true }).then();
      return throwError(() => err);
    })
  );
};
