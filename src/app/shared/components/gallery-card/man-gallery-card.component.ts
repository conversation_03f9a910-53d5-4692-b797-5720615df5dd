import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { BaseComponent, GalleryData, toBool, AdultOverlayComponent } from '@trendency/kesma-ui';
import { GalleryCardTypes } from './man-gallery-card.definitions';
import { StorageService } from '@trendency/kesma-core';
import { NgTemplateOutlet, NgIf } from '@angular/common';

@Component({
  selector: 'man-gallery-card',
  templateUrl: './man-gallery-card.component.html',
  styleUrls: ['./man-gallery-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdultOverlayComponent, NgTemplateOutlet, NgIf],
})
export class ManGalleryCardComponent extends BaseComponent<GalleryData> implements OnInit {
  @Input() set styleID(styleID: GalleryCardTypes) {
    this.hostClass = styleID;
  }
  @Input() isShowTag = false;
  @Input() isInsideAdultArticleBody = false;
  @HostBinding('class') hostClass: GalleryCardTypes = GalleryCardTypes.DEFAULT;

  readonly toBool = toBool;
  isAdultChoice = false;

  private readonly storage = inject(StorageService);

  override ngOnInit(): void {
    super.ngOnInit();
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
  }

  get galleryCard(): GalleryData | undefined {
    return this.data as GalleryData;
  }
}
