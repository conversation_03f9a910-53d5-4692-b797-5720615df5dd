<div class="gallery-card-wrapper">
  <div class="gallery-wrapper">
    @if (toBool(galleryCard?.isAdult) && !isInsideAdultArticleBody && !isAdultChoice) {
      <kesma-adult-overlay>
        <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
      </kesma-adult-overlay>
    } @else {
      <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
    }
    <ng-template #galleryImage>
      <img class="gallery-card" loading="lazy" [src]="galleryCard?.highlightedImageUrl || 'assets/images/placeholder-16-9.svg'" [alt]="galleryCard?.title" />
    </ng-template>

    <div class="gallery-indicator">
      <i class="icon icon-gallery"></i>
    </div>
  </div>

  <div class="gallery-header">
    <h2 class="gallery-header-title">{{ data?.title }}</h2>
    <p class="gallery-header-lead">{{ data?.description }}</p>

    <div class="gallery-tag" *ngIf="isShowTag">
      <i class="icon icon-mandiner-gallery"></i>
      <div class="gallery-tag-name">GALÉRIA</div>
    </div>
  </div>
</div>
