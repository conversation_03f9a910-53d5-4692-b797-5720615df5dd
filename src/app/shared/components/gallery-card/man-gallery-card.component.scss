@use 'shared' as *;

.gallery-card-wrapper {
  margin-bottom: 24px;
}

.gallery-wrapper {
  position: relative;

  .gallery-card {
    height: 100%;
    width: 100%;
    cursor: pointer;
    aspect-ratio: 16/9;
    object-fit: cover;
  }

  .gallery-indicator {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background: var(--kui-orange-600);
    padding: 10px;

    .icon-gallery {
      width: 25px;
      height: 23px;
    }
  }
}

:host {
  &.default {
    .gallery-header {
      background: var(--kui-gray-100);
      border-left: 5px solid var(--kui-orange-600);
      padding: 10px 15px 10px 10px;
    }
  }

  &.image-with-title {
    .gallery-wrapper {
      .gallery-indicator {
        display: none;
      }
    }

    .gallery-header {
      padding: 10px 0;

      &-title {
        font-size: 18px;
        font-weight: 600;
      }

      &-lead {
        display: none;
      }
    }
  }

  &.recommended {
    .gallery-wrapper {
      background: var(--kui-white);

      .gallery-indicator {
        display: none;
      }
    }

    .gallery-header {
      background: var(--kui-white);
      padding: 20px;
      height: 120px;

      &-title {
        font-family: var(--kui-font-secondary);
        font-size: 18px;
        line-height: 24px;
        font-weight: 700;
      }

      &-lead {
        display: none;
      }
    }
  }
}

.gallery-header {
  cursor: pointer;

  &-title {
    width: 100%;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    font-family: var(--kui-font-primary);

    @include media-breakpoint-down(sm) {
      font-size: 14px;
      line-height: 18px;
    }
  }

  &-lead {
    font-family: var(--kui-font-primary);
  }
}

.gallery-tag {
  padding: 7px;
  border: 1px solid var(--kui-orange-600);
  display: inline-flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;

  .icon {
    width: 12px;
    height: 10px;
  }

  &-name {
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
  }
}
