import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-opinion-footer',
  templateUrl: './man-opinion-footer.component.html',
  styleUrls: ['./man-opinion-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class ManOpinionFooterComponent {
  constructor() {}
}
