@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  background: var(--kui-beige-100);
}

.man-opinion {
  &-footer {
    padding-bottom: 20px;
    padding-top: 10px;
  }

  &-button {
    background: var(--kui-orange-600);
    padding: 12px 16px;
    color: var(--kui-white);
    font-weight: 400;
    font-size: 16px;
    margin: 0 auto 0;
    display: block;
  }

  &-layout-title {
    padding: 15px 10px;
    margin: 0;
    border-bottom: 0;
    font-family: var(--kui-font-primary);
    display: flex;
    align-items: center;
    border-top: 1px solid var(--kui-orange-600);
    font-weight: 700;
    font-size: 24px;
    line-height: 24px;
    color: var(--kui-orange-600);

    .icon {
      width: 7px;
      height: 12px;
      margin-left: 10px;
    }

    &:hover {
      color: var(--kui-black);
    }
  }
}
