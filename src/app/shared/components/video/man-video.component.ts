import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';

@Component({
  selector: 'man-video',
  templateUrl: './man-video.component.html',
  styleUrls: ['./man-video.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, AsyncPipe],
})
export class ManVideoComponent {
  private readonly isPlaying$ = new BehaviorSubject<boolean>(false);
  private readonly isMuted$ = new BehaviorSubject<boolean>(false);

  playing = this.isPlaying$.asObservable();
  muted = this.isMuted$.asObservable();

  onClickPlay(): void {
    this.isPlaying$.next(!this.isPlaying$.value);
  }

  onClickVolume(): void {
    this.isMuted$.next(!this.isMuted$.value);
  }
}
