@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  max-width: 792px;
}

.video {
  &-header {
    margin-bottom: 40px;
    &-title {
      font-weight: 700;
      line-height: 42px;
      font-family: var(--kui-font-secondary);
      margin-bottom: 15px;
      font-size: 28px;
      @include media-breakpoint-up(md) {
        font-size: 32px;
      }
    }
    &-meta {
      display: flex;
      justify-content: space-between;
      &-date {
        font-weight: 600;
        font-size: 12px;
        line-height: 12px;
      }
      &-bookmark {
        &-icon {
          margin-right: 5px;
        }
        &-text {
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
        }
      }
    }
  }
  &-player {
    width: 100%;
    max-width: 792px;
    margin-bottom: -3px;
    &-control {
      background: var(--kui-gray-100);
      margin-bottom: 20px;
      padding: 20px;
      @include media-breakpoint-up(md) {
        padding: 25px;
      }
      &-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 33px;
        &-nav {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 15px;
          &-play {
            background: url('../../../../assets/images/icons/video-play.svg');
            width: 80px;
            height: 80px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
          }
          &-pause {
            background: url('../../../../assets/images/icons/video-pause.svg');
            width: 80px;
            height: 80px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
          }
          &-rewind {
            background: url('../../../../assets/images/icons/video-rewind.svg');
            width: 60px;
            height: 60px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
          }
          &-forward {
            background: url('../../../../assets/images/icons/video-forward.svg');
            width: 60px;
            height: 60px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
          }
        }
        &-volume {
          position: relative;
          &-icon {
            background: url('../../../../assets/images/icons/volume.svg');
            width: 25px;
            height: 25px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
          }
          &-control {
            display: none;
            &.show {
              display: block;
              position: absolute;
              top: -20px;
              left: 10px;
            }
            background: var(--kui-gray-200);
            width: 100px;
            height: 4px;
            position: relative;
            margin-bottom: 20px;
            transform: rotate(-90deg);
            transform-origin: left;
            &-bar {
              position: absolute;
              top: 0;
              left: 0;
              width: 25%;
              height: 4px;
              background: var(--kui-orange-600);
              z-index: 9;
            }
            &-bullet {
              height: 20px;
              width: 20px;
              background: var(--kui-orange-600);
              border-radius: 50%;
              position: absolute;
              left: 25%;
              top: -8px;
            }
          }
        }
      }
      &-progress {
        background: var(--kui-gray-200);
        width: 100%;
        height: 4px;
        position: relative;
        margin-bottom: 20px;
        &-bar {
          position: absolute;
          top: 0;
          left: 0;
          width: 25%;
          height: 4px;
          background: var(--kui-orange-600);
          z-index: 9;
        }
        &-bullet {
          height: 20px;
          width: 20px;
          background: var(--kui-orange-600);
          border-radius: 50%;
          position: absolute;
          left: 25%;
          top: -8px;
        }
      }
      &-time {
        display: flex;
        justify-content: space-between;
        font-weight: 700;
        font-size: 16px;
        line-height: 26px;
      }
    }
  }
  &-divider {
    display: block;
    width: 100%;
    height: 1px;
    background: var(--kui-gray-200);
    margin-bottom: 20px;
  }
  &-topic {
    padding-bottom: 5px;
    margin-bottom: 10px;
    &-title {
      font-weight: 700;
      font-size: 18px;
      line-height: 120%;
    }
    &-list {
      &-item {
        margin: 12px 0;
        display: flex;
        justify-content: space-between;
        &-link {
          color: var(--kui-orange-600);
          text-decoration: underline;
          font-weight: 700;
          font-size: 16px;
          line-height: 24px;
        }
        &-time {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
        }
      }
    }
  }
  &-description {
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
  }
}
