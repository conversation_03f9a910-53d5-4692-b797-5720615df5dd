import { ArticleCard } from '@trendency/kesma-ui';

/** Options for the issue type */
export type PageNumberType = 'normal' | 'merged' | 'special_issue';

/**
 * Represent a column in the actual Journal.
 * @note Used where columns are ordered (e.g. journal-details-page)
 */
export type JournalColumn = {
  title: string;
  slug: string;
  order: number;
  articles: ArticleCard[];
};

/** Represents a Journal data as it is received from the BE */
export type JournalData = {
  id: string;
  publishDate: string | { date: string; timezone_type: number; timezone: string };
  yearGrade: string;
  pageNumber: string;
  pageNumberType: PageNumberType;
  textOfSpecialIssue?: string;
  name: string;
  mainTitle: string;
  mainText: string;
  urlSlug: string;
  image: string;
  pdfEmbedCode: string;
  imageThumbnailUrlCreatedAt: string;
  imageThumbnailUrl: string;
};

/**
 * Represents a Journal data with the articles appended.
 * @note Used in journal-list-page and man-weekly-newspaper layout component
 */
export type JournalDataWithArticles = JournalData & {
  articles: ArticleCard[];
};

/**
 * Represents a Journal data with the columns appended.
 * @note Used where columns are ordered (e.g. journal-details-page)
 */
export type JournalDataWithColumns = JournalData & {
  columns: JournalColumn[];
};

/** Represents a Journal recommender in the header. This is only an alias for now. */
export type HeaderMagazineData = {
  journalIssue: {
    name: string;
    title: string;
    text: string;
    urlSlug: string;
    image: {
      thumbnailUrl: string;
      fullSizeUrl: string;
    };
  };
  articles: HeaderMagazineArticle[];
};

export type HeaderMagazineArticle = {
  title: string;
  slug: string;
  publishDate: string;
  column: {
    title: string;
    slug: string;
  };
};
