@use 'shared' as *;

:host {
  display: block;

  ::ng-deep {
    man-block-title-sidebar {
      .heading-line-link {
        .heading-line-title {
          text-transform: none;
        }
      }
    }
  }

  .weekly-newspaper {
    &-text {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;

      .small-text {
        font-size: 14px;
        line-height: 18px;
      }
    }

    &-img {
      display: block;
      width: 100%;
      margin: 10px 0 20px 0;
      aspect-ratio: 37/50;
    }

    &-actual {
      text-align: center;
      font-size: 18px;
      text-transform: uppercase;
      font-weight: 400;
      margin-bottom: 20px;
      line-height: 21.6px;
    }

    .article-card {
      width: 100%;
      display: block;
      margin-bottom: 10px;
    }

    .button {
      font-size: 16px;
      width: 100%;
      max-width: 300px;
    }
  }
}
