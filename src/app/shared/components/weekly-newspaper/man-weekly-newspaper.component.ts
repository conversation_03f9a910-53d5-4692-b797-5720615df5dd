import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { JournalData } from './man-weekly-newspaper.definition';
import { SocialInteractionEvent } from '../social-share-modal/man-social-share-modal.definitions';
import { DateFnsModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgIf, NgFor } from '@angular/common';
import { ManBlockTitleSidebarComponent } from '../block-title-sidebar/man-block-title-sidebar.component';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'mandiner-man-weekly-newspaper',
  templateUrl: './man-weekly-newspaper.component.html',
  styleUrls: ['./man-weekly-newspaper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBlockTitleSidebarComponent, NgIf, NgFor, ManArticleCardComponent, ManSimpleButtonComponent, RouterLink, DateFnsModule],
})
export class ManWeeklyNewspaperComponent extends BaseComponent<ArticleCard[]> {
  @Input() styleIDs: { styleId: number }[] = [];
  @Input() journalData?: JournalData;

  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  @Input() blockTitle: BlockTitle = {
    text: 'Mandiner hetilap',
    url: 'hetilap',
  };

  get publishDate(): Date {
    return new Date(this.journalData?.publishDate as string);
  }

  subscribeClicked(): void {
    window.open(environment.shopUrls.subscriptions);
  }
}
