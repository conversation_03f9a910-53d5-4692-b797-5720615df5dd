<man-block-title-sidebar [data]="blockTitle"></man-block-title-sidebar>

<div class="weekly-newspaper">
  <ng-container *ngIf="journalData">
    <div class="weekly-newspaper-text">
      {{ journalData?.name }}
      <div class="small-text">megjelent: {{ publishDate | dfnsFormat: 'yyyy. LLLL dd.' }}</div>
    </div>

    <img class="weekly-newspaper-img" [src]="journalData?.image || 'assets/images/placeholder-story.svg'" alt="Mandiner hetilap" />
    <div class="weekly-newspaper-actual">Aktu<PERSON><PERSON>zámunkból</div>
  </ng-container>

  <ng-container *ngIf="data">
    <ng-container *ngFor="let article of data; index as index; trackBy: trackByFn">
      <man-article-card
        *ngIf="article"
        [styleID]="styleIDs[index]?.styleId || 0"
        class="article-card"
        [data]="article"
        [hideDate]="true"
        (socialInteraction)="socialInteraction.emit($event)"
        [isMplus]="article?.isPaywalled"
      >
      </man-article-card>
    </ng-container>
  </ng-container>

  <man-simple-button class="button" color="primary" (click)="subscribeClicked()"> Előfizetek a hetilapra </man-simple-button>

  <man-simple-button class="button" color="light" [routerLink]="['/', 'hetilap', journalData?.urlSlug]"> Aktuális hetilap megtekintése </man-simple-button>
</div>
