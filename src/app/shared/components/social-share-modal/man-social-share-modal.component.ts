import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import {
  getEmailShareUrl,
  getFacebookShareUrl,
  getTwitterShareUrl,
  PopoverService,
  RoutingHelperService,
  PopoverDirective,
  CopyToClipboardDirective,
} from '@trendency/kesma-ui';
import { SocialInteractionEvent, SocialInteractionEventType } from './man-social-share-modal.definitions';
import { NgIf, NgClass } from '@angular/common';

@Component({
  selector: 'mandiner-social-share-modal',
  templateUrl: './man-social-share-modal.component.html',
  styleUrls: ['./man-social-share-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, PopoverDirective, NgClass, CopyToClipboardDirective],
})
export class ManSocialShareModalComponent implements OnChanges {
  @ViewChild('shareOpportunities') shareOpportunities?: ElementRef;

  @Input() set link(link: string[]) {
    if (link) {
      this._link = link;
      this.setArticleLink();
    }
  }
  get link(): string[] {
    return this._link;
  }
  @Input() set hasExternalUrl(hasExternalUrl: boolean) {
    if (hasExternalUrl) {
      this._hasExternalUrl = hasExternalUrl;
      this.setArticleLink();
    }
  }
  get hasExternalUrl(): boolean {
    return this._hasExternalUrl;
  }

  @Input() title?: string;
  @Input() isShowText = false;
  @Input() isShowShareText = false;

  @Output() urlCopied = new EventEmitter<string>();
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  _link: string[] = [];
  _hasExternalUrl: boolean = false;
  shareUrl: string = '';
  facebookUrl: string = '';
  twitterUrl: string = '';
  emailUrl: string = '';

  readonly linkCopyLabel = 'Link másolása';
  readonly facebookShareLabel = 'Facebook-megosztás';
  readonly twitterShareLabel = 'Twitter-megosztás';
  readonly emailShareLabel = 'E-mailben küldés';

  constructor(
    public readonly service: PopoverService,
    private readonly routingHelperService: RoutingHelperService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    const link = changes['link']?.currentValue;
    const title = changes['title']?.currentValue;

    if (link?.length && title) {
      this.getSocialShareUrls(this.shareUrl, title);
    }
  }

  public onUrlCopied(): void {
    this.urlCopied.emit(this.shareUrl);
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.LinkCopy, this.shareUrl, this.title, this.linkCopyLabel));
  }

  private getSocialShareUrls(articleLink: string, articleTitle: string): void {
    this.facebookUrl = getFacebookShareUrl(articleLink);
    this.twitterUrl = getTwitterShareUrl(articleLink);
    this.emailUrl = getEmailShareUrl(articleLink, articleTitle);
  }

  private setArticleLink(): void {
    this.shareUrl = this.routingHelperService.resolveLink(this.link, this.hasExternalUrl);
  }

  onFacebookShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.FacebookShare, this.shareUrl, this.title, this.facebookShareLabel));
  }

  onTwitterShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.TwitterShare, this.shareUrl, this.title, this.twitterShareLabel));
  }

  onEmailShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.EmailShare, this.shareUrl, this.title, this.emailShareLabel));
  }
}
