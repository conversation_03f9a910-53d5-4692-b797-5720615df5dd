@use 'shared' as *;

:host {
  position: relative;
  top: 0;
  left: 0;
}
.share-text {
  cursor: pointer;
  color: var(--kui-orange-600);
  font-size: 16px;
}

.social-icon {
  cursor: pointer;
  width: 17px;
  height: 16px;
  &:hover {
    content: url('/assets/images/icons/share-dark.svg');
  }
}

.social-text {
  cursor: pointer;
  margin-left: 5px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: var(--kui-gray-700);
}

.dont-show-text {
  display: none;
}

.share-opportunities {
  width: 320px;
  padding: 20px;
  max-width: 360px;
  font-size: 12px;
  background: var(--kui-white);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.25);

  .copy-link {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 15px;

    .link {
      max-width: 222px;
      overflow: hidden;
      white-space: nowrap;
      background: var(--kui-gray-150);
      border: 1px solid var(--kui-gray-220);
      padding: 5px;
      width: 100%;
    }

    &-btn {
      font-weight: 600;
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .social-platforms {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px 0;

    .social-platform {
      &-link {
        .social-platform-icon {
          margin-right: 20px;
        }

        .social-platform-name {
          vertical-align: middle;
        }
      }
    }
  }

  .cancel-share {
    cursor: pointer;
    display: inline-block;

    &-icon {
      margin-right: 23px;
      width: 14px;
      margin-left: 3px;
    }

    &-name {
      vertical-align: middle;
    }
  }
}
