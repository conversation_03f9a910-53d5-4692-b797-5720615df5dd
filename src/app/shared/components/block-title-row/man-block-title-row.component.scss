@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 20px;
  background: var(--kui-orange-600);

  .heading-line {
    display: flex;
    &-logo {
      i {
        width: 150px;
        height: 30px;
        margin-bottom: 10px;
        @media (min-width: 400px) {
          margin: 0;
        }
      }
    }
    &-link {
      padding: 0;
      margin: 0;
    }

    &-title {
      font-family: var(--kui-font-primary);
      display: inline-block;
      padding: 15px 20px;
      color: var(--kui-white);
      text-transform: uppercase;
      font-weight: 700;
      font-size: 26px;
      line-height: 35px;
      flex: 1;
    }

    &-right {
      margin-left: auto;
      &-more {
        font-family: var(--kui-font-primary);
        color: var(--kui-white);
        margin-right: 5px;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
      }
      .icon {
        width: 8px;
        height: 13px;
        vertical-align: middle;
      }
    }
  }
}
