import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { BlockTitleRowComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
  selector: 'man-block-title-row',
  templateUrl: './man-block-title-row.component.html',
  styleUrls: ['./man-block-title-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink],
})
export class ManBlockTitleRowComponent extends BlockTitleRowComponent implements OnInit {
  override ngOnInit(): void {
    super.ngOnInit();
    if (!this.data) {
      this.setData({
        text: 'Friss hírek',
      });
    }
  }
}
