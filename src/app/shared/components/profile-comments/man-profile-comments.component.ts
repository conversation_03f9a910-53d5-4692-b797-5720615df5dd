import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { buildArticleUrl, CommentWithArticle } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { DATETIME_FORMAT } from '../../constants';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { FormatPipeModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManSocialInteractionsComponent } from '../social-interactions/man-social-interactions.component';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgIf, NgFor, SlicePipe } from '@angular/common';

@Component({
  selector: 'man-profile-comments',
  templateUrl: './man-profile-comments.component.html',
  styleUrls: ['./man-profile-comments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, <PERSON><PERSON><PERSON>, ManArticleCardComponent, ManSocialInteractionsComponent, ManSimpleButtonComponent, RouterLink, SlicePipe, FormatPipeModule],
})
export class ManProfileCommentsComponent {
  @Input() commentsWithArticle: CommentWithArticle[] = [];

  readonly DATETIME_FORMAT = DATETIME_FORMAT;
  readonly ArticleCardType = ArticleCardType;

  getSocial(comment: CommentWithArticle): LikeButtons {
    return {
      like: comment.likeCount ?? 0,
      dislike: comment.dislikeCount ?? 0,
      comment: comment.answerCount ?? 0,
    };
  }

  getArticleLink(comment: CommentWithArticle): string[] {
    return buildArticleUrl(comment.article);
  }
}
