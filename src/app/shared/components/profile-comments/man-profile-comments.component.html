<div class="profile-divider"></div>
<h2 class="profile-title"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h2>
<ng-container *ngIf="commentsWithArticle.length > 0; else EMPTY">
  <div class="profile-comment profile-grid-3">
    <ng-container *ngFor="let comment of commentsWithArticle | slice: 0 : 3; let i = index">
      <div class="profile-comment-card">
        <man-article-card [data]="comment.article" [styleID]="ArticleCardType.TitleDateMeta" [areSocialInteractionsVisible]="false"></man-article-card>
        <small class="profile-comment-card-date">{{ comment.createdAt | dfnsFormat: DATETIME_FORMAT }}</small>
        <div class="profile-comment-card-text">{{ comment.text }}</div>
        <mandiner-social-interactions
          [data]="getSocial(comment)"
          [articleLink]="getArticleLink(comment)"
          [articleTitle]="comment.article.title"
          [isMaxWidth]="true"
          [showDividers]="true"
        ></mandiner-social-interactions>
      </div>
      <man-simple-button class="w-100" *ngIf="i === 1" routerLink="/profil/hozzaszolasok">Összes hozzászólás »</man-simple-button>
    </ng-container>
  </div>
</ng-container>
<ng-template #EMPTY>
  <p>Nincsenek hozzászólások.</p>
  <p class="profile-comment-empty-cta">
    Véleményét bármikor kifejtheti a <a class="profile-comment-link" href="/">cikkoldalak</a> alján található hozzászólás szekciónál.
  </p>
</ng-template>
