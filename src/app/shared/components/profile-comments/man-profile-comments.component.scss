@use 'shared' as *;

.profile {
  &-divider {
    width: 100%;
    height: 1px;
    background: var(--kui-gray-300);
    margin-bottom: 20px;
  }

  &-title {
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  &-grid-3 {
    display: grid;
    grid-gap: 24px;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr;
    grid-template-areas:
      '.'
      '.'
      '.'
      'button';

    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      grid-template-areas:
        '. . .'
        '. button .';
    }
  }

  &-comment {
    ::ng-deep {
      man-article-card {
        margin-bottom: 12px;

        .article-card-title {
          font-family: var(--kui-font-primary) !important;
        }

        .article-card-publish-date {
          display: none;
        }
      }
    }

    &-card {
      &-date {
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: var(--kui-gray-600);
        margin-bottom: 10px;
        display: block;
      }

      &-text {
        font-weight: 400;
        font-size: 16px;
        line-height: 21px;
      }
    }

    man-simple-button {
      grid-area: button;
    }

    &-link {
      color: var(--kui-orange-600);
      text-decoration: underline;
    }

    &-empty-cta {
      margin-top: 10px;
    }
  }
}
