import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { ArticleAuthor, FollowStatus } from '@trendency/kesma-ui';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, <PERSON>For, NgClass, SlicePipe } from '@angular/common';

@Component({
  selector: 'man-profile-authors',
  templateUrl: './man-profile-authors.component.html',
  styleUrls: ['./man-profile-authors.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, NgClass, ManSimpleButtonComponent, SlicePipe],
})
export class ManProfileAuthorsComponent {
  @Input() authors: ArticleAuthor[] = [];
  @Input() loadingSlug: string | null = null;
  @Output() changeAuthor: EventEmitter<ArticleAuthor> = new EventEmitter<ArticleAuthor>();
  FollowStatus = FollowStatus;
}
