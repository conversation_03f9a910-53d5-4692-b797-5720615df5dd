@use 'shared' as *;

.profile {
  &-divider {
    width: 100%;
    height: 1px;
    background: var(--kui-gray-300);
    margin-bottom: 20px;
  }

  &-title {
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  &-follow {
    &-author {
      display: flex;
      margin-bottom: 22px;
      gap: 11px;
      align-items: center;

      &-image {
        box-shadow: 0 0 0 1px var(--kui-orange-600);
        border: 1px solid var(--kui-white);
        border-radius: 14px;
        height: 60px;
        width: 60px;

        &-placeholder {
          background-color: var(--kui-gray-800);

          &:after {
            display: block;
            width: 58px;
            height: 58px;
            content: '';
            background-repeat: no-repeat;
            background-size: 40px;
            background-position: center;
            @include icon('icons/icon-mandiner-user-gold.svg');
          }
        }
      }

      &-caption {
        &-title {
          font-family: var(--kui-font-primary);
          font-weight: 700;
          font-size: 18px;
          line-height: 18px;
          margin-bottom: 8px;
          display: block;
        }
      }
    }

    &-button {
      border: 1px solid var(--kui-orange-600);
      border-radius: 3px;
      padding: 6px 12px;
      transition:
        background-color 0.3s ease,
        color 0.3s ease,
        border-color 0.3s ease;
      display: inline-flex;
      text-align: center;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      color: var(--kui-black);

      &:not(.followed):hover {
        background-color: var(--kui-orange-600);
        color: var(--kui-white);
      }

      &.followed {
        background-color: var(--kui-orange-600);
        color: var(--kui-white);

        &:hover {
          background-color: var(--kui-black);
          border-color: var(--kui-black);
        }
      }
    }

    &-link {
      color: var(--kui-orange-600);
      text-decoration: underline;
    }

    &-empty-cta {
      margin-top: 10px;
    }
  }
}
