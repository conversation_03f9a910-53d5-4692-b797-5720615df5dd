<div class="profile-follow">
  <div class="profile-divider"></div>
  <h2 class="profile-title"><PERSON><PERSON><PERSON><PERSON></h2>
  <ng-container *ngIf="authors.length > 0; else EMPTY">
    <div *ngFor="let author of authors | slice: 0 : 3" class="profile-follow-author">
      <img *ngIf="author?.avatar; else avatarPlaceholder" [alt]="author?.name" [src]="author?.avatar" class="profile-follow-author-image" />
      <ng-template #avatarPlaceholder>
        <div class="profile-follow-author-image profile-follow-author-image-placeholder"></div>
      </ng-template>
      <div class="profile-follow-author-caption">
        <a [routerLink]="['/szerzo', author.slug]" class="profile-follow-author-caption-title">{{ author.name }}</a>
        <button
          (click)="loadingSlug !== author?.slug && changeAuthor.emit(author)"
          [ngClass]="{ followed: author.isFollowed === FollowStatus.LOGGED_IN_FOLLOWED }"
          class="profile-follow-button"
        >
          {{
            loadingSlug === author?.slug ? 'Kérem várjon...' : author.isFollowed === FollowStatus.LOGGED_IN_FOLLOWED ? 'Követés leállítása' : 'Szerző követése'
          }}
        </button>
      </div>
    </div>
    <man-simple-button class="w-100" routerLink="/szerzo">Összes szerző »</man-simple-button>
  </ng-container>
  <ng-template #EMPTY>
    <p>Nincsenek követett szerzők.</p>
    <p class="profile-follow-empty-cta">Új szerzők követéséhez tekintse meg <a class="profile-follow-link" href="/szerzo">szerzőink</a> listáját.</p>
  </ng-template>
</div>
