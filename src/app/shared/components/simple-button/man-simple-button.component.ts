import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconComponent, SimpleButtonComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgIf, Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'man-simple-button',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.scss',
    './man-simple-button.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, IconComponent, NgClass, NgStyle, NgIf],
})
export class ManSimpleButtonComponent extends SimpleButtonComponent {}
