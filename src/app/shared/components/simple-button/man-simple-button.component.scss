@use 'shared' as *;

:host {
  --button-text-alternative: var(--kui-orange-600) !important;

  --button-bg-primary: var(--kui-black);
  --button-bg-secondary: transparent !important;
  --button-bg-tertiary: var(--kui-orange-600, #d74929);
  --button-bg-dark: var(--kui-black, #000);
  --button-bg-hover: var(--kui-orange-600) !important;

  &.social {
    .btn-outline {
      border-width: 2px;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        width: 65%;
      }

      .icon {
        width: 20px;
        height: 20px;
      }
    }
  }
}

button.btn {
  &-primary {
    background: var(--kui-black);
    color: var(--button-text-light);
  }
  &-tertiary {
    background-color: var(--kui-orange-600);
  }
  &-secondary {
    color: var(--button-text-alternative) !important;
    border: 1px solid var(--kui-orange-600);

    &:hover {
      border-color: var(--button-text-light);
      color: var(--button-text-light) !important;
    }
  }

  &-light {
    border: 1px solid var(--kui-orange-600);
  }

  &-info {
    color: var(--kui-black);
    border: 1px solid var(--kui-orange-600);
    background-color: var(--kui-white);

    &:hover {
      border-color: var(--button-text-light);
      color: var(--button-text-light);
    }
  }

  &-dark {
    color: var(--kui-orange-600);
    border: 1px solid var(--kui-orange-600);

    &:hover {
      color: var(--button-text-dark);
    }
  }
}
