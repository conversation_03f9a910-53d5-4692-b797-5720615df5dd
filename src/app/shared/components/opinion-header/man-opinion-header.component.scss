@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  &.style-2 {
    background: var(--kui-beige-100);
  }
}

.man-opinion {
  &-header {
    padding-bottom: 10px;

    .man-divider {
      display: block;
      background: var(--kui-orange-600);
      width: 100%;
      height: 1px;
    }
  }

  &-layout-title {
    padding: 15px 10px;
    margin: 0;
    border-bottom: 0;
    font-family: var(--kui-font-primary);
    display: flex;
    align-items: center;
    border-top: 1px solid var(--kui-orange-600);
    font-weight: 700;
    font-size: 24px;
    line-height: 24px;
    color: var(--kui-orange-600);

    .icon {
      width: 7px;
      height: 12px;
      margin-left: 10px;
    }

    &:hover {
      color: var(--kui-black);
    }
  }
}
