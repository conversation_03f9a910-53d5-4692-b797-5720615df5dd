import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { ManOpinionCardType } from '../opinion-card/man-opinion-card.types';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-opinion-header',
  templateUrl: './man-opinion-header.component.html',
  styleUrls: ['./man-opinion-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class ManOpinionHeaderComponent implements OnInit {
  @HostBinding('class') hostClass = '';
  @Input() styleID: ManOpinionCardType = ManOpinionCardType.NO_BACKGROUND;
  constructor() {}

  ngOnInit(): void {
    this.hostClass = `style-${this.styleID}`;
  }
}
