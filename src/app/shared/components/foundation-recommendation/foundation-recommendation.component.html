<ng-container *ngIf="recommendations && recommendations.length > 0">
  <div class="row foundation-recommendation">
    <div class="col-12 {{ sideRecommendations.length > 0 ? 'col-md-8' : 'col-md-12' }}">
      <man-block-title-small [data]="{ text: 'Tov<PERSON><PERSON><PERSON> cikkek a témában' }"></man-block-title-small>
      <div class="row">
        <div class="col-12 foundation-recommendation-main">
          <man-article-card
            (socialInteraction)="onSocialInteraction($event)"
            [data]="mainRecommendation"
            [hideDate]="true"
            [isCategoryVisible]="true"
            [isMaxWidth]="true"
            [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
          >
          </man-article-card>
        </div>
        <div *ngFor="let recommendation of subRecommendations" class="col-12 col-md-6 foundation-recommendation-sub">
          <man-article-card
            (socialInteraction)="onSocialInteraction($event)"
            [data]="recommendation"
            [hideDate]="true"
            [isCategoryVisible]="true"
            [isMaxWidth]="true"
            [styleID]="ArticleCardType.ImgTitleDateMeta"
          >
          </man-article-card>
        </div>
      </div>
    </div>
    <ng-container *ngIf="sideRecommendations.length > 0">
      <div class="col-12 col-md-4">
        <man-block-title-small [data]="{ text: 'További hírek a témában' }" class="side"></man-block-title-small>
        <div class="foundation-recommendation-side">
          <man-article-card
            (socialInteraction)="onSocialInteraction($event)"
            *ngFor="let recommendation of sideRecommendations"
            [data]="recommendation"
            [hideDate]="true"
            [isCategoryVisible]="true"
            [isMaxWidth]="true"
            [styleID]="ArticleCardType.TitleMeta"
          >
          </man-article-card>
        </div>
      </div>
    </ng-container>
  </div>

  <div class="more-recommendation">
    <man-simple-button [routerLink]="['/', 'cimke', foundationTagSlug]" color="info">
      <strong>Még több cikk </strong>
    </man-simple-button>
  </div>
</ng-container>
