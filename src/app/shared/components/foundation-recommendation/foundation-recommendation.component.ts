import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ApiService } from '../../services';
import uniqBy from 'lodash-es/uniqBy';
import { AnalyticsService, ArticleCard, Tag } from '@trendency/kesma-ui';
import { searchResultToArticleCard } from '../../../feature/tags-page/tags-page.utils';
import { NgForOf, NgIf } from '@angular/common';
import { ManBlockTitleSmallComponent } from '../block-title-small/man-block-title-small.component';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { RouterLink } from '@angular/router';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { SocialInteractionEvent, SocialInteractionEventType } from '../social-share-modal/man-social-share-modal.definitions';

@Component({
  selector: 'app-foundation-recommendation',
  templateUrl: './foundation-recommendation.component.html',
  styleUrls: ['./foundation-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManBlockTitleSmallComponent, ManArticleCardComponent, ManSimpleButtonComponent, RouterLink, NgForOf],
})
export class FoundationRecommendationComponent implements OnInit {
  @Input() foundationTagSlug: string;
  @Input() tags: Tag[];
  @Input() articleSlug: string;
  @Input() isSidebar = false;

  recommendations: ArticleCard[] = [];
  ArticleCardType = ArticleCardType;

  readonly allowedContentTypes: string[] = ['article', 'articleVideo'];

  constructor(
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly analyticsService: AnalyticsService
  ) {}

  get mainRecommendation(): ArticleCard | undefined {
    return this.recommendations.length > 0 ? this.recommendations[0] : undefined;
  }

  get subRecommendations(): ArticleCard[] {
    return this.recommendations.length > 1 ? this.recommendations.slice(1, 3) : [];
  }

  get sideRecommendations(): ArticleCard[] {
    return this.recommendations.length > 3 ? this.recommendations.slice(3, 11) : [];
  }

  ngOnInit(): void {
    this.getArticlesByFoundationTagSlug();
  }

  getArticlesByFoundationTagSlug(): void {
    this.apiService.searchArticleByTags([this.foundationTagSlug], this.allowedContentTypes, 0, 12).subscribe((res) => {
      this.recommendations = res.data.filter((a) => a.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult));

      if (this.recommendations.length < 11) {
        this.getMoreArticlesByTags();
      } else {
        this.cdr.detectChanges();
      }
    });
  }

  getMoreArticlesByTags(): void {
    this.apiService
      .searchArticleByTags(
        this.tags.map((tag) => tag.slug),
        this.allowedContentTypes,
        0,
        12
      )
      .subscribe((res2) => {
        this.recommendations = uniqBy(
          this.recommendations.concat(res2.data.filter((a) => a.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult))),
          'id'
        );
        this.cdr.detectChanges();
      });
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }
}
