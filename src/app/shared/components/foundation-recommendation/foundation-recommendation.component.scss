@use 'shared' as *;

.foundation-recommendation {
  margin-top: 20px;

  man-block-title-small {
    margin-bottom: 20px;

    &.side {
      @include media-breakpoint-down(md) {
        margin-top: 20px;
      }
    }
  }

  &-sub {
    padding-top: var(--bs-gutter-x);
  }

  &-side {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.more-recommendation {
  margin: 20px 0;
  text-align: center;
}
