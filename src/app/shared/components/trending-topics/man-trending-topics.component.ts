import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, Tag } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf, NgFor } from '@angular/common';

@Component({
  selector: 'man-trending-topics',
  templateUrl: './man-trending-topics.component.html',
  styleUrls: ['./man-trending-topics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgFor],
})
export class ManTrendingTopicsComponent extends BaseComponent<Tag[]> {
  @Input() foundationTagSlug?: string;
  @Input() foundationTagTitle?: string;
}
