@use 'shared' as *;

:host {
  display: block;
}

.trending-topics {
  width: 100%;
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-bottom: 20px;

  &-topic {
    font-family: var(--kui-font-primary);
    color: var(--kui-black);
    font-weight: 400;
    font-size: 14px;
    padding: 10px 8px;
    border: solid 1px var(--kui-gray-800);
    margin-top: 20px;
    cursor: pointer;
  }
}
