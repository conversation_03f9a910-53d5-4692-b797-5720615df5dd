@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  max-width: 384px;
  width: 100%;

  .profile-data-modification {
    &-title {
      font-weight: 700;
      font-size: 16px;
      line-height: 140%;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: var(--kui-orange-600);
    }

    &-form {
      &-row {
        margin-bottom: 20px;
      }

      &-label {
        font-weight: 700;
        font-size: 14px;
        margin-bottom: 5px;
      }
      &-input {
        border: 1px solid var(--kui-gray-400);
        display: block;
        width: 100%;
        height: 40px;
        margin-bottom: 5px;
        padding: 10px;

        &-password {
          position: relative;
          &-img {
            position: absolute;
            right: 10px;
            top: 13px;
          }
        }
      }
      &-info-text {
        font-weight: 400;
        font-size: 10px;
        line-height: 16px;
      }

      &-input-container {
        position: relative;
      }
      &-see-password {
        position: absolute;
        top: 13px;
        right: 10px;
        width: 19px;
        height: 13px;
        min-width: 19px;
        min-height: 13px;

        &.on {
          @include icon('icons/icon-eye.svg');
          background-repeat: no-repeat;
        }
        &.off {
          @include icon('icons/icon-eye.svg'); // Kellene még ide egy áthúzott szem icon. Design-ban nem található.
          background-repeat: no-repeat;
        }
      }
    }

    &-buttons {
      display: flex;
      gap: 20px;
    }

    &-btn-modification-of-data {
      background: var(--kui-black);
      color: var(--kui-white);
      display: block;
      width: 100%;
      padding: 12px;
      font-weight: 600;
      font-size: 14px;
      line-height: 19px;
      transition: all 0.3s ease;

      &:hover {
        background: var(--kui-orange-600);
      }
    }
    &-btn-cancel {
      color: var(--kui-orange-600);
      border: 1px solid var(--kui-orange-600);
      padding: 10px 20px;
      transition: all 0.3s ease-in-out;

      &:hover {
        background: var(--kui-orange-600);
        color: var(--kui-white);
        border: 1px solid var(--kui-orange-600);
      }
    }
  }
}
