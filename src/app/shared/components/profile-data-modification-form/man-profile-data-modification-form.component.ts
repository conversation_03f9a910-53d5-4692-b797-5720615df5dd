import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';

@Component({
  selector: 'mandiner-man-profile-data-modification-form',
  templateUrl: './man-profile-data-modification-form.component.html',
  styleUrls: ['./man-profile-data-modification-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ManProfileDataModificationFormComponent implements OnInit {
  @HostBinding('class.profile-data-modification') inLayout = true;
  @HostBinding('class') hostClass = '';

  @Input() styleID: number | undefined;

  showPassword = false;

  private setProperties(): void {
    this.hostClass = `style-${this.styleID}`;
  }

  ngOnInit(): void {
    this.setProperties();
  }

  toggleShowPassword(): boolean {
    return (this.showPassword = !this.showPassword);
  }
}
