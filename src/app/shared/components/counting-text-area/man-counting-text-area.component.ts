import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';
import { CountingTextAreaComponent } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'man-counting-text-area',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/counting-text-area/counting-text-area.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/counting-text-area/counting-text-area.component.scss',
    './man-counting-text-area.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ManCountingTextAreaComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => ManCountingTextAreaComponent),
      multi: true,
    },
  ],
  imports: [FormsModule, NgIf],
})
export class ManCountingTextAreaComponent extends CountingTextAreaComponent {}
