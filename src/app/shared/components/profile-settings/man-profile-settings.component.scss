@use 'shared' as *;

:host {
  .text-bold {
    font-weight: 700;
  }

  .text-yellow {
    color: var(--kui-orange-600);
  }

  .text-gray {
    color: rgba(0, 0, 0, 0.5);
  }

  .text-green {
    color: var(--kui-green-600);
  }

  .opacity {
    opacity: 0.3;
  }
}

.profile-settings {
  &-wrapper {
    padding: 30px 0 50px;

    @include media-breakpoint-down(md) {
      padding: 20px 0 50px;
    }
  }

  &-title {
    font-family: var(--kui-font-primary);
    color: var(--kui-orange-600);
    border-bottom: 1px solid var(--kui-gray-100);
    font-size: 24px;
    padding-bottom: 20px;
    margin: 20px 0 10px;
    line-height: 24px;
    font-weight: 700;
  }

  &-container {
    max-width: 768px;
    margin: 20px auto;
  }

  &-box {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border: 2px solid var(--kui-orange-600);
    padding: 20px;

    man-simple-button {
      width: 280px;
    }

    &-divider {
      background: var(--kui-gray-300);
      display: block;
      margin: 20px auto;
      height: 1px;
      max-width: 384px;
      width: 100%;
    }

    &-title {
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 16px;
      line-height: 140%;
      text-transform: uppercase;
      margin-bottom: 5px;

      &-line {
        background: var(--kui-orange-600);
        height: 2px;
        width: 100px;
        margin-bottom: 20px;
      }

      @include media-breakpoint-down(md) {
        font-size: 16px;
      }
    }

    &-label {
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 5px;
    }

    &-text {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }

    &-link {
      text-decoration: underline;
      color: var(--kui-orange-600);
    }
  }

  &-row {
    margin-bottom: 20px;
    font-size: 14px;

    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: 1fr 3fr;
      grid-gap: 50px;
    }

    &-content {
      display: flex;
    }

    &-left {
      width: 50%;
    }

    &-right {
      width: 50%;
    }
  }

  &-btn-transparent {
    background: transparent;
    color: var(--kui-gray-600);
    display: block;
    width: 100%;
    padding: 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
    margin-top: 30px;
    text-decoration: underline;
  }

  &-warning {
    font-size: 10px;

    a {
      color: var(--kui-orange-600);
    }
  }
}

.icon-dustbin {
  position: relative;

  &::before {
    top: 2px;
    left: -20px;
    background: url('../../../../assets/images/icons/icon-dustbin.svg');
    width: 12px;
    height: 15px;
    content: '';
    position: absolute;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.action-button {
  margin-bottom: 20px;
}
