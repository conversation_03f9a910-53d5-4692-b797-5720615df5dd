import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DATETIME_FORMAT } from '../../constants';
import { User } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { NgIf, DatePipe } from '@angular/common';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'man-profile-settings',
  templateUrl: './man-profile-settings.component.html',
  styleUrls: ['./man-profile-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManSimpleButtonComponent, RouterLink, DatePipe],
})
export class ManProfileSettingsComponent {
  readonly DATETIME_FORMAT = DATETIME_FORMAT;

  @Input() user?: User & { isOldOtpSimplePayPayment?: boolean };
  @Output() legacyCancelSubscription = new EventEmitter<void>();

  handleCancelSubscription(): void {
    if (this.user?.isOldOtpSimplePayPayment) {
      // Legacy cancel subscription
      this.legacyCancelSubscription.emit();
      return;
    }
    window.open(environment.shopUrls.cancelSubscription);
  }

  handleCardChange(): void {
    window.open(environment.shopUrls.manageCards);
  }

  handleSubscription(): void {
    window.open(environment.shopUrls.subscriptions);
  }
}
