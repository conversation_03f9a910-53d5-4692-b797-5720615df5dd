export type SearchFilterDefinitions = {
  datum: FilterData[];
  rovat: FilterData[];
  tipus: FilterData[];
};
export type FilterData = {
  title: string;
  slug: string;
  match: number | null;
};

export type FilterValues = {
  keyword?: string;
  sort?: string;
  fromDate?: Date;
  toDate?: Date;
  columns?: string[];
  contentTypes?: string[];
  dateFilterType?: string;
};

export type FormValue = {
  keyword?: string;
  sort: string;
  dateFilterType?: string;
  fromDate?: Date;
  toDate?: Date;
  selectedColumns: boolean[];
  selectedContentTypes: boolean[];
};
