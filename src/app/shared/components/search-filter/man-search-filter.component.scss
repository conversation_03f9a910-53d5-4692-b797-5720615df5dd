@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  width: 100%;

  @include media-breakpoint-down(md) {
    padding-bottom: 15px;
    border-bottom: 1px solid var(--kui-gray-50);
  }
}

.filter-button {
  width: 200px;
  margin: auto;
}

.search-bar-append {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 99%;

  man-simple-button {
    margin: 0;
  }
}

.search {
  &-header {
    &-title {
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--kui-gray-100);
      margin-bottom: 10px;
    }
  }

  &-result {
    &-count {
      display: block;
      margin-bottom: 20px;
      font-weight: 700;
      font-size: 14px;

      &-match {
        color: var(--kui-orange-600);
      }
    }
  }

  &-bar {
    position: relative;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;

    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: 8fr 1fr 1fr;
      align-items: center;
      grid-row-gap: 10px;

      &.filter-on-side {
        grid-template-columns: 7.5fr 2fr;
      }
    }

    &-wrap {
      border: 1px solid var(--kui-gray-200);
      padding: 10px;
      margin-bottom: 10px;
      width: 100%;

      @include media-breakpoint-up(md) {
        margin-bottom: 0;
      }

      &.focus {
        .search-bar-icon {
          display: none;
        }
      }
    }

    &-icon {
      position: absolute;
      top: 8px;
    }

    &-input {
      width: 85%;
      margin-left: 40px;
    }

    &-order {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      @include media-breakpoint-up(md) {
        margin-bottom: 0;
      }
      @include media-breakpoint-down(md) {
        order: 2;
      }
    }

    &-select {
      background: var(--kui-white);
      border: none;
      font-weight: 700;
      font-size: 14px;
      margin-left: -4px;
      color: var(--kui-black);
      outline: none;
    }

    &-btn {
      margin-left: auto;
      display: inline-flex;
      align-items: center;
      font-weight: 700;

      @include media-breakpoint-down(md) {
        order: 3;
      }

      &-txt {
        margin-right: 5px;
        font-size: 14px;
        color: var(--kui-black);
      }

      .icon {
        width: 20px;
        height: 20px;
      }
    }

    &.without-search {
      max-width: 320px;
      grid-template-columns: 1fr 1fr;
      display: grid;
      grid-gap: 30px;

      .search-bar-order {
        margin-bottom: 0;
      }

      @include media-breakpoint-down(md) {
        display: flex;
        width: 100%;
        max-width: 100%;
        align-content: space-between;
        grid-gap: 0;
      }
    }
  }

  &-filter {
    position: relative;
    border: 1px solid var(--kui-gray-200);
    padding: 40px;
    display: none;

    @include media-breakpoint-down(sm) {
      padding: 20px;
    }

    &.filter-on-side {
      @include media-breakpoint-up(md) {
        position: absolute;
        width: 100%;
        top: 50px;
        right: 0;
        z-index: 1;
        background-color: var(--kui-white);
      }
    }

    &.active {
      display: block;
    }

    &-close-icon {
      position: absolute;
      top: 30px;
      right: 30px;
      cursor: pointer;
    }

    &-title {
      font-weight: 700;
      font-size: 18px;
      text-align: center;
      margin-bottom: 20px;
    }

    &-btn {
      background: var(--kui-black);
      color: var(--kui-white);
      text-align: center;
      font-weight: 700;
      font-size: 16px;
      padding: 10px 20px;
      max-width: 320px;
      width: 100%;
      margin: 0 auto 20px;
      display: block;
    }

    &-settings {
      @include media-breakpoint-up(lg) {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 50px;
      }

      &-column {
        margin: 30px 0;
        @include media-breakpoint-up(lg) {
          margin: 0;
        }
      }

      &-title {
        color: var(--kui-gray-390);
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 20px;
        text-transform: uppercase;
      }
    }

    &-settings2 {
      @include media-breakpoint-up(lg) {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 50px;
      }

      &-column {
        margin: 30px 0;
        @include media-breakpoint-up(lg) {
          margin: 0;
        }
      }

      &-title {
        color: var(--kui-gray-390);
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 20px;
        text-transform: uppercase;
      }
    }

    &-published {
      .search-filter-checkbox {
        &-checkmark {
          border: none;
        }

        &:hover input ~ .search-filter-checkbox-checkmark {
          background-color: none;
          border: none;
        }

        input:checked ~ .search-filter-checkbox-checkmark {
          background-color: none;
          border: none;
        }
      }

      .search-filter-checkbox input:checked ~ .search-filter-checkbox-checkmark {
        background-color: var(--kui-white);
        border: 2px solid var(--kui-white);
      }

      .search-filter-checkbox-checkmark::after {
        border: solid var(--kui-black);
        border-width: 0 3px 3px 0 !important;
      }
    }

    // Checkbox
    &-checkbox {
      display: flex;
      justify-content: space-between;
      position: relative;
      padding-left: 35px;
      margin-bottom: 12px;
      cursor: pointer;
      font-size: 22px;
      user-select: none;
      /* On mouse-over, add a grey background color */
      &:hover input ~ .search-filter-checkbox-checkmark {
        background-color: var(--kui-white);
        border: 3px solid var(--kui-orange-600);
      }

      &-text {
        font-weight: 400;
        font-size: 16px;
      }

      /* Hide the browser's default checkbox */
      &-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
      }

      /* Create a custom checkbox */
      &-checkmark {
        position: absolute;
        top: 8px;
        left: 0;
        height: 16px;
        width: 16px;
        background-color: var(--kui-white);
        border: 3px solid var(--kui-black);
        border-radius: 4px;
        /* Create the checkmark/indicator (hidden when not checked) */
        &:after {
          content: '';
          position: absolute;
          display: none;
        }

        /* Style the checkmark/indicator */
        &:after {
          left: 4px;
          top: 0px;
          width: 6px;
          height: 12px;
          border: solid var(--kui-white);
          border-width: 0 3px 3px 0;
          transform: rotate(45deg);
        }
      }

      /* When the checkbox is checked, add a blue background */
      input:checked ~ .search-filter-checkbox-checkmark {
        background-color: var(--kui-black);
        border: 2px solid var(--kui-black);
      }

      input:checked ~ .search-filter-checkbox-text {
        font-weight: 700;
      }

      /* Show the checkmark when checked */
      input:checked ~ .search-filter-checkbox-checkmark:after {
        display: block;
      }

      &-count {
        font-weight: 400;
        font-size: 12px;
        margin-top: 8px;
      }
    }
  }

  .external-data-wrapper {
    font-size: 14px;

    @include media-breakpoint-up(md) {
      margin-bottom: 10px;
    }
  }
}

man-simple-button {
  margin: 0 auto 30px;
  max-width: 320px;
  display: block;

  .man-btn-black {
    font-weight: 700 !important;
    font-size: 16px !important;
  }
}

.active-color {
  color: var(--kui-orange-600);
}

.hide {
  display: none;
}

.form {
  &.filter-on-side {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  .filters {
    &.filter-on-side {
      display: flex;
      gap: 10px;
    }
  }
}
