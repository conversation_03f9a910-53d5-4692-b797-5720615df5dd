import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FilterData, FilterValues, FormValue, SearchFilterDefinitions } from './man-search-filter.definitions';
import { BaseComponent } from '@trendency/kesma-ui';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { NgIf, NgClass, NgTemplateOutlet, NgFor } from '@angular/common';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManDateRangePickerComponent } from '../date-range-picker/man-date-range-picker.component';

@Component({
  selector: 'man-search-filter',
  templateUrl: './man-search-filter.component.html',
  styleUrls: ['./man-search-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, FormsModule, NgTemplateOutlet, ReactiveFormsModule, NgFor, ManSimpleButtonComponent, ManDateRangePickerComponent],
})
export class ManSearchFilterComponent extends BaseComponent<SearchFilterDefinitions> implements OnInit, OnDestroy {
  @ViewChild('filterWrapper') filterWrapper?: ElementRef<HTMLElement>;
  /** Article title for the search header - only shown when `showSearchHeader` is `true` */
  @Input() articleTitle?: string;
  /** Count of the search results - only shown when `showSearchHeader` is `true` */
  @Input() resultCount?: number;
  /** Show the search header */
  @Input() showSearchHeader: boolean = true;
  /** Show the search input bar */
  @Input() showSearchBar: boolean = true;
  /** Default state of the search filter */
  @Input() defaultFilterValues: FilterValues = {};
  /** Toggle search filters and order select to be on the side of the search bar */
  @Input() filterOnSide: boolean = false;
  /**
   * Debounce time for the filters.
   * @default 300
   */
  @Input() debounceTime: number = 300;
  @Input() placeholder = 'Keresés a cikkekben ...';
  @Input() isShowColumns = true;
  @Input() isShowContentTypes = true;
  @Input() showSorting = true;
  @Input() showToggleButton = true;
  @Input() showedSearchedKeyWord?: string;

  /**
   * Event emitted when the search filter is changed
   * Note: Search button was removed in KESMA-5975, so this event is emitted on every change.
   */
  @Output() filterEvent: EventEmitter<FilterValues> = new EventEmitter<FilterValues>();

  formGroup: UntypedFormGroup | undefined;
  searchedKeyword: string | undefined;

  @Input() active = false;
  @Output() activeChange = new EventEmitter<boolean>();
  focus: boolean = false;
  filterWrapperHeight?: number;
  isMobile = false;

  private readonly _destroy$ = new Subject<void>();

  get isExactDate(): boolean {
    return this.formGroup?.get('dateFilterType')?.value === 'exact_date';
  }

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly route: ActivatedRoute,
    private readonly cd: ChangeDetectorRef,
    private readonly utilService: UtilService
  ) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.isMobile = this.utilService.isBrowser() ? window.innerWidth <= 768 : false;
    this.defaultFilterValues = { keyword: this.route.snapshot.queryParamMap.get('global_filter') || '' };
    this.initForm(this.defaultFilterValues.keyword as string);
  }

  initForm(keyword: string): void {
    this.formGroup = this.formBuilder.group({
      keyword: [keyword],
      sort: [this.defaultFilterValues?.sort ?? 'date_desc'],
      dateFilterType: ['anytime'],
      fromDate: [undefined],
      toDate: [undefined],
      selectedColumns: new UntypedFormArray(this.data?.rovat.map(() => new UntypedFormControl(false)) ?? []),
      selectedContentTypes: new UntypedFormArray(this.data?.tipus.map(() => new UntypedFormControl(false)) ?? []),
    });

    if (this.defaultFilterValues?.keyword) {
      this.searchedKeyword = this.defaultFilterValues?.keyword;
    }

    this.addCheckboxes('selectedColumns', this.data?.rovat, this.defaultFilterValues?.columns);
    this.addCheckboxes('selectedContentTypes', this.data?.tipus, this.defaultFilterValues?.contentTypes);

    this.formGroup.valueChanges
      .pipe(debounceTime(this.debounceTime), takeUntil(this._destroy$))
      .subscribe(() => (this.searchedKeyword = this.formGroup?.value.keyword));
  }

  filter(): void {
    this.filterEvent.emit(this.mapFormValueToFilterValues(this.formGroup?.value));
    this.active = false;
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  private addCheckboxes(to: string, from?: FilterData[], defaultValues?: string[]): void {
    from?.forEach((item) => this.getFormArray(to).push(new UntypedFormControl(defaultValues?.includes(item.slug) ?? false)));
  }

  private getFormArray(name: string): UntypedFormArray {
    return this.formGroup?.get(name) as UntypedFormArray;
  }

  private mapFormValueToFilterValues(value: FormValue): FilterValues {
    return {
      keyword: this.searchedKeyword,
      sort: value.sort,
      fromDate: value.fromDate,
      toDate: value.toDate,
      dateFilterType: value.dateFilterType,
      columns: value.selectedColumns.map((column, i) => (column ? this.data?.rovat[i].slug : null)).filter((column) => !!column) as string[],
      // eslint-disable-next-line max-len
      contentTypes: value.selectedContentTypes
        .map((contentType, i) => (contentType ? this.data?.tipus[i].slug : null))
        .filter((contentType) => !!contentType) as string[],
    };
  }

  handleDateChange(event: { start: Date | null; end: Date | null }): void {
    this.formGroup?.patchValue({
      fromDate: event.start,
      toDate: event.end,
    });
  }

  onSearch(): void {
    this.defaultFilterValues.keyword = this.searchedKeyword;
    this.formGroup?.patchValue({
      keyword: this.searchedKeyword,
    });

    this.filter();
  }

  toggle(): void {
    this.active = !this.active;
    this.activeChange.emit(this.active);

    setTimeout(() => {
      this.getFilterWrapperHeight();
      this.cd.markForCheck();
    }, 0);
  }

  getFilterWrapperHeight(): number {
    if (this.active && this.filterOnSide && !this.isMobile) {
      return Number(this.filterWrapper?.nativeElement.offsetHeight) + 20;
    }
    return 10;
  }
}
