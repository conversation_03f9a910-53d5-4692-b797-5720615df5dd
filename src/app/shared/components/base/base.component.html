<man-header
  [class.with-eb-matches]="ebService.isEnableFootballEbElements() && !!(ebLiveHeaderData$ | async)?.length"
  [class.with-olimpia]="olimpiaService.isEnableOlympicsElements() && (olimpiaHeaderData$ | async)"
  [currentWeather]="(currentWeather$ | async) ?? undefined"
  [data]="(currencyExchangeRates$ | async) ?? undefined"
  [dossiers]="(dossiers$ | async) ?? []"
  [mainMenu]="mainMenu"
  [trendingTags]="(trendingTags$ | async) ?? []"
  [user]="user"
  class="header"
>
  <kesma-eb-matches
    (navigateTo)="navigateToEbPage()"
    *ngIf="ebService.isEnableFootballEbElements() && (ebLiveHeaderData$ | async) as data"
    [data]="data"
    [showLogo]="true"
    [styleID]="EBPortalEnum.MANDINER"
    class="style-{{ EBPortalEnum[EBPortalEnum.MANDINER] }}"
    eb-header
  >
  </kesma-eb-matches>

  <kesma-olimpia-header
    (navigateTo)="navigateToOlimpiaPage()"
    *ngIf="olimpiaService.isEnableOlympicsElements() && (olimpiaHeaderData$ | async) as data"
    [data]="data"
    [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
    class="style-{{ OlimpicPortalEnum[OlimpicPortalEnum.OlimpicMANDINER] }}"
    olimpia-header
  ></kesma-olimpia-header>
</man-header>

<man-header-magazine
  (registrationClicked)="handleRegister()"
  (subscribeClicked)="handleSubscribe()"
  *ngIf="magazineData$ | async as magazineData; else noMagazine"
  [data]="magazineData"
  [user]="user"
  class="header-magazine"
></man-header-magazine>

<app-strossle-advert advertId="Mandiner_leaderboard_top_1"></app-strossle-advert>

<app-eb-header *ngIf="ebService.isEnableFootballEbElements() && (sportService.isSport$ | async)"></app-eb-header>

<ng-container *ngIf="electionsService.isElections2024Enabled() && (isHome$ | async) === false">
  <section>
    <div class="wrapper">
      <kesma-elections-box [link]="electionsService.getElections2024Link()" [styleID]="ElectionsBoxStyle.HEADER"></kesma-elections-box>
    </div>
  </section>
</ng-container>

<ng-template #noMagazine>
  <div class="header-spacer"></div>
</ng-template>

<man-subscription-strip *ngIf="moreHeaderConfigSubscription"></man-subscription-strip>

<man-newsletter-diverter-card (subscribeClicked)="onSubscribeClicked()" *ngIf="moreHeaderConfigNewsletter" [styleID]="NewsletterDiverterCardType.Header">
</man-newsletter-diverter-card>

<man-breaking-strip *ngIf="breakingNews" [data]="breakingNews" [type]="BreakingType.Default"></man-breaking-strip>

<app-static-mobile-nav></app-static-mobile-nav>

<div class="content-wrap">
  <router-outlet></router-outlet>
</div>

<app-olimpia-footer-navigator *ngIf="olimpiaService.isEnableOlympicsElements()"></app-olimpia-footer-navigator>
<man-footer
  (cookieSettingsClick)="onCookieSettingsClick()"
  [data]="footer0"
  [facebookLink]="facebookLink"
  [instagramLink]="instagramLink"
  [linkedInLink]="linkedInLink"
  [twitterLink]="twitterLink"
  [youtubeLink]="youtubeLink"
></man-footer>

<app-calendar (closed)="hideCalendar = $event" *ngIf="!hideCalendar"></app-calendar>
