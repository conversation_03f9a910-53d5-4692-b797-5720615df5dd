import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  BreakingBlock,
  ChampionshipSchedule,
  CityWeatherCurrent,
  DossierData,
  EBPortalEnum,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  InitResolverData,
  KesmaEbMatchesComponent,
  NEWSLETTER_COMPONENT_TYPE,
  OlimpiaHeaderComponent,
  OlimpiaHeaderData,
  OlimpicPortalEnum,
  PortfolioResponse,
  SimplifiedMenuItem,
  TrendingTag,
  User,
} from '@trendency/kesma-ui';
import { Observable, of, share, Subject } from 'rxjs';
import { filter, map, startWith, take, takeUntil } from 'rxjs/operators';
import { WeatherService } from '../../../feature/weather/api/weather.service';
import { BreakingType, CalendarType, Header, NewsletterDiverterCardType } from '../../definitions';
import {
  ApiService,
  AuthService,
  CalendarService,
  EbService,
  ElectionsService,
  ExchangeRateService,
  JournalService,
  OlimpiaService,
  SportResultService,
  SportService,
} from '../../services';
import { ManBreakingStripComponent } from '../breaking-strip/man-breaking-strip.component';
import { CalendarComponent } from '../calendar/calendar.component';
import { EbHeaderComponent } from '../eb-header/eb-header.component';
import { ManFooterComponent } from '../footer/man-footer.component';
import { ManHeaderMagazineComponent } from '../header-magazine/man-header-magazine.component';
import { ManHeaderComponent } from '../header/man-header.component';
import { ManNewsletterDiverterCardComponent } from '../newsletter-diverter-card/man-newsletter-diverter-card.component';
import { OlimpiaFooterNavigatorComponent } from '../olimpia/olimpia-footer-navigator/olimpia-footer-navigator.component';
import { StaticMobileNavComponent } from '../static-mobile-nav/static-mobile-nav.component';
import { ManSubscriptionStripComponent } from '../subscription-strip/man-subscription-strip.component';

import { StrossleAdvertComponent } from '../strossle-advert/strossle-advert.component';
import { environment } from '../../../../environments/environment';

declare const __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManHeaderComponent,
    AsyncPipe,
    KesmaEbMatchesComponent,
    NgIf,
    OlimpiaHeaderComponent,
    ManHeaderMagazineComponent,
    EbHeaderComponent,
    ElectionsBoxComponent,
    ManSubscriptionStripComponent,
    ManNewsletterDiverterCardComponent,
    ManBreakingStripComponent,
    StaticMobileNavComponent,
    RouterOutlet,
    OlimpiaFooterNavigatorComponent,
    ManFooterComponent,
    CalendarComponent,
    StrossleAdvertComponent,
  ],
})
export class BaseComponent implements OnInit, AfterViewInit, OnDestroy {
  mainMenu: SimplifiedMenuItem[] = [];
  topMenu: SimplifiedMenuItem[] = [];
  footer0: SimplifiedMenuItem[] = [];
  footer1: SimplifiedMenuItem[] = [];

  breakingNews?: BreakingBlock;
  currentWeather$?: Observable<CityWeatherCurrent>;

  moreHeaderConfigNewsletter = false;
  moreHeaderConfigSubscription = false;
  mediaworksFooter$: Observable<PortfolioResponse>;

  isOlimpiaMainOrArticlePage$ = this.olimpiaService.getIsOlimpiaMainOrArticlePage$();

  hideCalendar = false;

  readonly NewsletterDiverterCardType = NewsletterDiverterCardType;
  readonly BreakingType = BreakingType;
  readonly CalendarType = CalendarType;
  facebookLink = 'https://www.facebook.com/mandiner.hu/';
  instagramLink = 'https://www.instagram.com/mandiner.hu/?hl=hu';
  youtubeLink = 'https://www.youtube.com/user/MandinerTV/featured';
  linkedInLink = 'https://www.linkedin.com/company/mandiner?originalSubdomain=hu';
  twitterLink = 'https://twitter.com/mandiner?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor';
  dossiers$: Observable<DossierData[]>;
  trendingTags$: Observable<TrendingTag[]>;
  isArticleUrl: boolean;
  categorySlug: string;
  currencyExchangeRates$: Observable<Header>;
  magazineData$ = this.journalService.getHeaderMagazineData();
  isHomePage?: boolean;
  adBlockerIsActive = false;
  areAdsInitiated = false;
  EBPortalEnum = EBPortalEnum;
  OlimpicPortalEnum = OlimpicPortalEnum;
  isLeaderboardAdHidden$: Observable<boolean> = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(this.router),
    map(() => this.route.snapshot.firstChild?.data?.['isLeaderboardAdHidden'] === true)
  );
  ebLiveHeaderData$: Observable<ChampionshipSchedule[]> = this.sportResultService.getScheduleByCompetition(this.ebService.getSlug()).pipe(
    map(({ data }) => data?.schedules),
    share()
  );
  olimpiaHeaderData$: Observable<OlimpiaHeaderData> = this.olimpiaService.getHeadlineMedalsAndEvents().pipe(
    map((data) => data?.data),
    share()
  );
  ElectionsBoxStyle = ElectionsBoxStyle;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  readonly isHome$ = this.router.events.pipe(
    startWith(new NavigationEnd(0, '/', '/')),
    filter((event) => event instanceof NavigationEnd),
    map(() => this.router.url === '/' || this.router.url.includes('valasztas-2024-')),
    takeUntil(this.unsubscribe$)
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly weatherService: WeatherService,
    private readonly apiService: ApiService,
    private readonly authService: AuthService,
    private readonly exchangeRateService: ExchangeRateService,
    private readonly calendarService: CalendarService,
    private readonly journalService: JournalService,
    private readonly analyticsService: AnalyticsService,
    private readonly utilService: UtilService,
    private readonly sportResultService: SportResultService,
    public readonly ebService: EbService,
    public readonly olimpiaService: OlimpiaService,
    public readonly electionsService: ElectionsService,
    readonly sportService: SportService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.calendarService.fetchCalendar();
    this.sportService.handleChanges();
    this.mediaworksFooter$ = this.apiService.getPortfolioFooter();
    if (this.utils.isBrowser()) {
      this.currencyExchangeRates$ = this.exchangeRateService.getExchangeRates();
    }
    // Check user at first page load (async, not to block UI) to display header differently if user is logged in
    this.authService.isAuthenticated().subscribe(() => {
      this.changeRef.markForCheck();
    });

    this.currentWeather$ = this.weatherService.getFullWeatherData().pipe(
      take(1),
      map((weather) => weather?.current?.find(({ city }) => 'Budapest' === city) as CityWeatherCurrent)
    );

    const responseData: InitResolverData & {
      tags: Observable<TrendingTag[]>;
    } = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;

    const {
      menu: { header, header_1, footer, footer_1 },
    } = responseData || {};
    this.mainMenu = header ?? [];
    this.topMenu = header_1 ?? [];
    this.footer0 = footer ?? [];
    this.footer1 = footer_1 ?? [];
    this.dossiers$ = of(responseData.init.dossiers) as Observable<DossierData[]>;
    this.trendingTags$ = responseData.tags;

    const menu = responseData.menu as any;

    this.moreHeaderConfigNewsletter = menu?.moreHeaderConfigNewsletter;
    this.moreHeaderConfigSubscription = menu?.moreHeaderConfigSubscription;

    // Enable sticky layout debugging.
    this.enableStickyLayoutDebug();
  }

  ngAfterViewInit(): void {
    if (!this.utilService.isBrowser()) return;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onSubscribeClicked(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.HEADER);
    window.open('/hirlevel-feliratkozas', '_blank');
  }

  onCookieSettingsClick(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  handleRegister(): void {
    this.router.navigateByUrl('/regisztracio').then();
  }

  handleSubscribe(): void {
    window.open(environment.shopUrls.subscriptions);
  }

  navigateToEbPage(): void {
    this.router.navigate(['/', 'foci-eb-2024']).then();
  }

  navigateToOlimpiaPage(): void {
    this.router.navigate(['/', 'olimpia-2024']).then();
  }

  /**
   * Sets a CSS background color for the sticky elements. This could be useful for debugging.
   */
  enableStickyLayoutDebug(): void {
    if (this.utils.isBrowser() && this.route.snapshot.queryParams['stickyBg'] === '1') {
      (this.document.querySelector(':root') as any)['style'].setProperty('--man-sticky-bg', 'lightblue');
    }
  }

  isAdblockerActive(): boolean {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the adocean does not exist on SSR.
      return false;
    }
    return typeof (<any>window).ado !== 'object';
  }
}
