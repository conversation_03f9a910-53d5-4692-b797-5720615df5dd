@use 'shared' as *;

$header-height: 55px;
$eb-header-height: 64px;
$eb-header-mobil-height: 88px;
$olimpia-header-height: 64px;
$olimpia-header-height-mobile: 72px;

.content-wrap {
  width: calc(100% - 120px);
  margin: auto;

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

:host {
  man-subscription-strip {
    margin-bottom: 0;
  }

  .ad-above-nav {
    ::ng-deep .desktop-ad {
      margin-bottom: 20px;
    }
  }
}

.header {
  height: $header-height;

  &.with-eb-matches {
    height: $header-height + $eb-header-height;

    @include media-breakpoint-down(sm) {
      height: $header-height + $eb-header-mobil-height;
    }
  }

  &.with-olimpia {
    height: $header-height + $olimpia-header-height;

    @include media-breakpoint-down(sm) {
      height: $header-height + $olimpia-header-height-mobile;
    }
  }
}

.news-letter-popup {
  z-index: 2;
}

.header-spacer {
  position: relative;
  height: $header-height;

  @include media-breakpoint-down(md) {
    height: 50px;
  }
}

kesma-elections-box {
  margin: 30px 0;
}
