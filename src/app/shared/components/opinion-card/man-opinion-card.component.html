<man-opinion-header
  *ngIf="(mobileHeader && (isMobile$ | async)) || (desktopHeader && (isMobile$ | async) === false)"
  class="opinion-header"
></man-opinion-header>
<div
  [class]="fontSizeClass"
  [ngClass]="{
    highlight,
    caption,
    italic,
  }"
  class="opinion-card"
>
  <div class="opinion-card-top">
    <figure class="opinion-card-figure">
      <img
        *ngIf="data?.author?.avatarUrl; else avatarPlaceholder"
        [alt]="data?.thumbnail?.alt"
        [src]="data?.author?.avatarUrl"
        class="opinion-card-figure-image"
        loading="lazy"
      />
      <ng-template #avatarPlaceholder>
        <div class="opinion-card-figure-image opinion-card-figure-image-placeholder"></div>
      </ng-template>
      <figcaption class="opinion-card-figcaption">
        <h4 class="opinion-card-figcaption-title">{{ data?.author?.name }}</h4>
        <p *ngIf="data?.articleMedium" class="opinion-card-figcaption-subtitle">{{ data?.articleMedium }}</p>
      </figcaption>
    </figure>
    <div class="opinion-card-icon">
      <img alt="Idézőjel" class="opinion-card-icon-quote" loading="lazy" src="/assets/images/icons/new-icon-quote.svg" />
    </div>
  </div>
  <div class="opinion-card-header">
    <a [routerLink]="articleLink" class="opinion-card-link">
      <h2 [style.font-size]="fontSizePx" class="opinion-card-header-title">
        {{ data?.title }}
      </h2>
    </a>
  </div>
  <p class="opinion-card-content">
    {{ data?.lead }}
  </p>
  <div *ngIf="styleID !== ManOpinionCardType.NO_BACKGROUND_NO_DATE_META && styleID !== ManOpinionCardType.ARTICLE_DETAIL_STYLE" class="opinion-card-footer">
    <div *ngIf="!hideDate" class="opinion-card-publish-date">
      {{ data?.publishDate | publishDate }}
    </div>
    <mandiner-social-interactions
      (socialInteraction)="onSocialInteraction($event)"
      [articleLink]="articleLink"
      [articleTitle]="data?.title"
      [data]="social"
    ></mandiner-social-interactions>
  </div>
</div>
<man-opinion-footer
  *ngIf="(mobileFooter && (isMobile$ | async)) || (desktopFooter && (isMobile$ | async) === false)"
  class="opinion-footer"
></man-opinion-footer>
