import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit, EventEmitter, Output, inject } from '@angular/core';
import { ArticleCard, ArticleCardWithSocial, buildArticleUrl } from '@trendency/kesma-ui';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { ManOpinionCardType } from './man-opinion-card.types';
import { SocialInteractionEvent } from '../social-share-modal/man-social-share-modal.definitions';
import { distinctUntilChanged, fromEvent, map, Observable, of, startWith } from 'rxjs';
import { UtilService, PublishDatePipe } from '@trendency/kesma-core';
import { ManOpinionFooterComponent } from '../opinion-footer/man-opinion-footer.component';
import { ManSocialInteractionsComponent } from '../social-interactions/man-social-interactions.component';
import { RouterLink } from '@angular/router';
import { ManOpinionHeaderComponent } from '../opinion-header/man-opinion-header.component';
import { NgIf, NgClass, AsyncPipe } from '@angular/common';

@Component({
  selector: 'man-opinion-card',
  templateUrl: './man-opinion-card.component.html',
  styleUrls: ['./man-opinion-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManOpinionHeaderComponent, NgClass, RouterLink, ManSocialInteractionsComponent, ManOpinionFooterComponent, AsyncPipe, PublishDatePipe],
})
export class ManOpinionCardComponent implements OnInit {
  private readonly utils = inject(UtilService);
  @HostBinding('class') hostClass = '';
  @Input()
  set data(data: ArticleCard) {
    if (data) {
      this._data = data;
    }
  }
  get data(): ArticleCard {
    return this._data as ArticleCard;
  }
  @Input() socialInteractions?: LikeButtons;
  @Input() styleID: ManOpinionCardType = ManOpinionCardType.NO_BACKGROUND;
  @Input() desktopHeader: boolean = false;
  @Input() desktopFooter: boolean = false;
  @Input() mobileHeader: boolean = false;
  @Input() mobileFooter: boolean = false;
  @Input() highlight: boolean = false;
  @Input() caption: boolean = false;
  @Input() italic: boolean = false;
  @Input() hideDate = false;
  @Input() fontSize = 20;
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  currentWindowWidth$: Observable<number> = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        distinctUntilChanged()
      )
    : of(1920); // Default to desktop width if not in browser
  isMobile$: Observable<boolean> = this.currentWindowWidth$.pipe(map((width: number) => width < 992));

  readonly ManOpinionCardType = ManOpinionCardType;

  _data?: ArticleCard;
  articleLink: string[] = [];

  get social(): LikeButtons {
    const articleData = this.data as ArticleCardWithSocial | undefined;
    return {
      like: articleData?.likeCount ?? 0,
      dislike: articleData?.dislikeCount ?? 0,
      comment: articleData?.commentCount ?? 0,
      ...(this.socialInteractions ?? {}), // Social interactions can be overridden if passed in manually
    };
  }

  ngOnInit(): void {
    this.articleLink = this.data ? buildArticleUrl(this.data) : [];
    if (this.data) {
      this.hostClass = `style-${this.styleID}`;
    }
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.socialInteraction.emit({
      ...$event,
      publishDate: this.data.publishDate as Date,
    });
  }

  get fontSizePx(): string | undefined {
    return this.fontSize ? `${this.fontSize}px` : undefined;
  }

  get fontSizeClass(): string {
    return this.fontSize ? `fs-${this.fontSize}` : '';
  }
}
