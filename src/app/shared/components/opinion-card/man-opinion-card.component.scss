@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  margin-bottom: 20px;

  $fontSizes: 20, 22, 24, 28;

  // Difference should be same between font-size and line-height.
  // For example: 20:26, 22:28, 24:30, 28:34;
  $difference: 6px;

  @each $fontSize in $fontSizes {
    .fs-#{$fontSize} {
      .opinion-card-header-title {
        line-height: ($fontSize + $difference);
      }
    }
  }

  .opinion-card {
    padding: 10px 0;
  }

  &.style-1 {
    .opinion-card {
      padding: unset;
    }
  }

  &.style-2 {
    background-color: var(--kui-beige-100);

    .opinion-card {
      margin: 10px;
      border-bottom: 1px solid var(--kui-orange-600);
    }

    .mandiner-social-interactions {
      border-bottom: none;
    }
  }

  &.style-3 {
    padding: 15px 0;
    border-bottom: 1px solid var(--kui-gray-100);
    border-top: 1px solid var(--kui-gray-100);

    .opinion-card {
      padding: 0;

      &-content {
        margin-bottom: 0;
      }
    }
  }

  &.style-4 {
    margin-bottom: 0;

    .opinion-card {
      padding: 0;

      &-top {
        margin-bottom: 9px;
      }

      &-figcaption {
        &-title {
          font-family: var(--kui-font-primary);
          margin-bottom: 0;
        }

        &-subtitle {
          font-size: 16px;
          font-weight: 400;
          line-height: 16px;
          margin-top: 5px;
        }
      }

      &-icon-quote {
        width: 40px;
        height: 33px;
      }

      &-content {
        margin-bottom: 0;
      }
    }
  }
}

.caption {
  .opinion-card-header-title {
    text-transform: uppercase;
  }

  .opinion-card-content {
    text-transform: uppercase;
  }
}

.highlight {
  .opinion-card-header-title {
    font-size: 28px;
  }

  .opinion-card-content {
    font-size: 20px;
  }
}

.italic {
  .opinion-card-header-title {
    font-style: italic;
  }

  .opinion-card-content {
    font-style: italic;
  }
}

.opinion-card {
  &-link {
    color: var(--kui-black);
  }

  &-caption {
    background-color: black !important;
  }

  &-italic {
    background-color: yellow !important;
  }

  &-lead {
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
    margin-bottom: 10px;
  }

  &-publish-date {
    display: block;
    color: var(--kui-gray-600);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    margin-bottom: 15px;
  }
}

.opinion-card {
  &-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  &-figure {
    display: flex;

    &-image {
      border: 2px solid var(--kui-orange-600);
      border-radius: 14px;
      margin-right: 11px;
      object-fit: cover;
      height: 60px;
      width: 60px;

      &-placeholder {
        background-color: var(--kui-gray-800);

        &:after {
          display: block;
          width: 58px;
          height: 58px;
          content: '';
          background-repeat: no-repeat;
          background-size: 40px;
          background-position: center;
          border-radius: 14px;
          @include icon('icons/icon-mandiner-user-gold.svg');
        }
      }
    }
  }

  &-figcaption {
    display: flex;
    flex-direction: column;
    justify-content: center;

    &-title {
      text-transform: uppercase;
      font-size: 18px;
      line-height: 18px;
      margin-bottom: 5px;
    }

    &-subtitle {
      color: var(--kui-orange-600);
      font-weight: 400;
    }
  }

  &-icon {
    &-quote {
      width: 30px;
      height: 52px;
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;

    &-title {
      margin-right: 10px;
      font-family: var(--kui-font-primary);
      font-size: 24px;
      line-height: 30px;
      font-weight: 700;
      @include media-breakpoint-up(lg) {
        &:hover {
          color: var(--kui-orange-600);
        }
      }
    }
  }

  &-content {
    font-family: var(--kui-font-primary);
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
    margin-bottom: 5px;
  }
}
