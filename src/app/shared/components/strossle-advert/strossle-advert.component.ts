import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-strossle-advert',
  templateUrl: './strossle-advert.component.html',
  styleUrls: ['./strossle-advert.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class StrossleAdvertComponent implements OnInit {
  @Input() advertId: string;
  hasDebug: boolean;
  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit(): void {
    this.hasDebug = this.route.snapshot.queryParamMap.has('apptest');
  }
}
