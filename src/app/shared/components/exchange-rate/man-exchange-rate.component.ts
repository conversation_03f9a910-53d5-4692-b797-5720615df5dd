import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ExchangeRate } from './man-exchange-rate.definitions';
import { BaseComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'mandiner-man-exchange-rate',
  templateUrl: './man-exchange-rate.component.html',
  styleUrls: ['./man-exchange-rate.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ManExchangeRateComponent extends BaseComponent<ExchangeRate> {}
