@use 'shared' as *;

:host {
  display: block;
  --kui-gray-500: #a0a0a0;
}

.footer {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--kui-gray-50);
  font-family: var(--kui-font-primary);

  &-menu {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &-wrapper {
    display: block;
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;

    @media screen and (max-width: 1250px) {
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  &-top {
    display: flex;
    align-items: center;
    width: 100%;
  }

  &-logo {
    margin: 30px auto;
  }

  &-logo-image {
    min-width: 175px;
    min-height: 35px;
  }

  &-content {
    display: grid;
    gap: 90px;
    width: 100%;
    padding: 24px 0 48px 0;
    border-top: 1px solid var(--kui-gray-400);
    border-bottom: 1px solid var(--kui-gray-400);

    grid-template-columns: repeat(2, 1fr);

    @include media-breakpoint-up(xs) {
      gap: 20px;
    }

    @include media-breakpoint-up(sm) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    @include media-breakpoint-up(md) {
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }

    @include media-breakpoint-up(lg) {
      grid-template-columns: repeat(3, 1fr);
    }

    @include media-breakpoint-up(xl) {
      display: flex;
      justify-content: space-between;
    }
  }

  &-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 54px;
  }

  &-copyright {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: var(--kui-black);
  }

  &-menu-title {
    text-transform: uppercase;
    font-weight: 700;
    font-size: 14px;
    line-height: 19px;
    color: var(--kui-gray-250);
  }

  &-menu-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    &-item {
      display: flex;
      align-items: flex-start;

      &-sub {
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .footer-menu-link {
          font-weight: normal;
          margin-left: 16px;
        }
      }

      &.parent {
        flex-direction: column;
      }
    }
  }

  &-menu-link {
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: var(--kui-black);
    cursor: pointer;

    &:hover {
      text-decoration: underline;
      color: var(--kui-orange-600);
    }
  }

  .no-link {
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    cursor: default;
  }

  .follow-us-link {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.icon {
  width: 20px;
  height: 16px;
}
