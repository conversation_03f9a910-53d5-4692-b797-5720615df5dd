import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MenuChild, RelatedType, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { NgFor, NgTemplateOutlet, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-footer',
  templateUrl: './man-footer.component.html',
  styleUrls: ['./man-footer.component.scss'],
  imports: [RouterLink, NgFor, NgTemplateOutlet, NgIf],
})
export class ManFooterComponent implements OnInit {
  @Input() data: SimplifiedMenuItem[] = [];

  @Input() facebookLink = '';
  @Input() instagramLink = '';
  @Input() youtubeLink = '';
  @Input() linkedInLink = '';
  @Input() twitterLink = '';

  @Output() cookieSettingsClick = new EventEmitter<MouseEvent>();

  followUsContent: { title?: string; children?: Partial<MenuChild>[] } = {};
  currentYear = new Date().getFullYear();

  public readonly RelatedType = RelatedType;

  ngOnInit(): void {
    this.followUsContent = {
      title: 'kövess minket',
      children: [
        {
          title: 'Facebook',
          customUrl: this.facebookLink,
          color: 'facebook',
          targetBlank: true,
        },
        {
          title: 'Instagram',
          customUrl: this.instagramLink,
          color: 'instagram',
          targetBlank: true,
        },
        {
          title: 'Twitter',
          customUrl: this.twitterLink,
          color: 'twitter',
          targetBlank: true,
        },
        {
          title: 'Youtube',
          customUrl: this.youtubeLink,
          color: 'youtube',
          targetBlank: true,
        },
        {
          title: 'LinkedIn',
          customUrl: this.linkedInLink,
          color: 'linkedin',
          targetBlank: true,
        },
      ],
    };
  }

  onCookieSettingsClick($event: MouseEvent): void {
    this.cookieSettingsClick.emit($event);
  }
}
