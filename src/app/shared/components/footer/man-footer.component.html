<footer class="footer">
  <div class="footer-wrapper">
    <div class="footer-top">
      <a class="footer-logo" [routerLink]="['/']">
        <img class="footer-logo-image" src="/assets/images/new-mandiner-logo.svg" width="175" height="35" alt="Mandiner logó" />
      </a>
    </div>
    <div class="footer-content">
      <div class="footer-menu" *ngFor="let item of data; let last = last">
        <div class="footer-menu-title">{{ item.title }}</div>
        <ul class="footer-menu-list">
          <li *ngFor="let listItem of item?.children" class="footer-menu-list-item" [class.parent]="listItem?.children?.length">
            <ng-container *ngTemplateOutlet="!listItem.isCustomUrl ? normalLink : customUrl; context: { item: listItem }"></ng-container>
            <ul class="footer-menu-list-item-sub">
              <li *ngFor="let subItem of listItem?.children" class="footer-menu-list-item">
                <ng-container *ngTemplateOutlet="!subItem.isCustomUrl ? normalLink : customUrl; context: { item: subItem }"></ng-container>
              </li>
            </ul>
          </li>
          <li *ngIf="last" class="footer-menu-list-item">
            <a class="footer-menu-link" (click)="onCookieSettingsClick($event)">Sütibeállítások</a>
          </li>
        </ul>
      </div>

      <div class="footer-menu">
        <div class="footer-menu-title">{{ followUsContent.title }}</div>
        <ul class="footer-menu-list">
          <li *ngFor="let listItem of followUsContent.children" class="footer-menu-list-item">
            <a class="footer-menu-link follow-us-link" [href]="listItem.customUrl" target="_blank">
              <i class="icon icon-mandiner-{{ listItem.color }}"></i>
              {{ listItem.title }}
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-copyright">© {{ currentYear }}. Mandiner Novum Zrt. Minden jog fenntartva!</div>
    </div>
  </div>
</footer>

<ng-template #normalLink let-item="item">
  <ng-container *ngIf="item.relatedType === RelatedType.DROPDOWN; else notEmpty">
    <span class="no-link">{{ item.title }}</span>
  </ng-container>
  <ng-template #notEmpty>
    <a class="footer-menu-link" [routerLink]="item.link" [target]="item.target"> {{ item.title }}</a>
  </ng-template>
</ng-template>
<ng-template #customUrl let-item="item">
  <a class="footer-menu-link" [href]="item.link" [target]="item.target"> {{ item.title }}</a>
</ng-template>
