@use 'shared' as *;

:host {
  ::ng-deep {
    man-simple-button {
      span {
        line-height: 1;
        display: inherit;
      }
    }
  }
}
.magazine {
  background: var(--kui-yellow-300);
  position: relative;
  z-index: 1;
  box-shadow: 0 5px 5px #00000026;
  justify-content: space-between;

  @include media-breakpoint-up(md) {
    padding: 0;
  }

  &-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 25px;

    @include media-breakpoint-down(md) {
      padding: 0 20px;
    }

    @include media-breakpoint-up(md) {
      flex-direction: row;
      max-width: 1310px;
      width: 100%;
      margin: 0 auto;
    }

    @include media-breakpoint-down(sm) {
      padding: 5px 20px;
    }
  }

  &-content {
    display: flex;
    gap: 20px;
    width: 100%;

    @include media-breakpoint-down(sm) {
      justify-content: center;
    }

    &.no-buttons {
      @include media-breakpoint-down(lg) {
        gap: 0;

        .magazine-divider {
          display: none;
        }
      }

      @include media-breakpoint-down(sm) {
        .magazine-header {
          display: initial;
        }

        .magazine-left {
          flex-basis: 100%;
          gap: 20px;
        }
      }
    }
  }

  &-divider {
    display: block;
    width: 1px;
    height: 84px;
    background: var(--kui-black);
    align-self: center;

    @include media-breakpoint-up(sm) {
      width: 2px;
    }
  }

  &-right {
    width: 50%;
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: space-between;

    @include media-breakpoint-down(lg) {
      width: auto;
      margin-left: 20px;
    }

    @include media-breakpoint-down(md) {
      justify-content: flex-end;
      margin-left: 0;
    }
  }

  &-left {
    display: flex;
    width: calc(50% - 18px);

    @include media-breakpoint-down(lg) {
      flex-basis: 100%;
    }

    @include media-breakpoint-down(sm) {
      flex-basis: 0;
    }
  }

  &-link {
    display: flex;
    align-items: center;
    min-width: 65px;
    margin-right: 20px;
    flex: 0 0 auto;

    @include media-breakpoint-down(sm) {
      margin-right: 0;
      justify-content: flex-end;
    }

    .magazine-img {
      width: 82px;
      aspect-ratio: 41/57; //Regular aspect ratio for a A4 image would be 1.414, however the image that is used is a bit wider, so the borders would be cropped.
      object-fit: cover;

      @include media-breakpoint-down(sm) {
        width: 60px;
      }
    }
  }

  &-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 90%;
    &-title {
      font-family: var(--kui-font-secondary);
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      margin-bottom: 3px;

      @include media-breakpoint-down(lg) {
        font-size: 18px;
        line-height: 24px;
      }
    }
    &-subtitle {
      font-family: var(--kui-font-secondary);
      font-weight: 700;
      font-size: 16px;
      line-height: 20px;
      text-transform: uppercase;
      margin-bottom: 10px;

      @include media-breakpoint-down(lg) {
        font-size: 14px;
        line-height: 20px;
      }
    }
    &-published {
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 24px;
      }
    }
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
  &-list {
    margin: 0 0 0 10px;
    padding: 5px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 12px;
    position: relative;
    @include media-breakpoint-up(md) {
      position: static;
    }

    @include media-breakpoint-down(lg) {
      display: none;
    }

    &-item {
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
      margin-bottom: 10px;
      position: relative;
      cursor: pointer;

      &:hover {
        color: var(--kui-orange-600);
      }

      &:last-child {
        margin-bottom: 0;
      }

      &:before {
        content: '';
        width: 6px;
        height: 6px;
        background: var(--kui-orange-600);
        color: var(--kui-orange-600);
        border-radius: 50%;
        position: absolute;
        left: -13px;
        top: 6px;
      }
    }

    &-buttons {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 10px;
      max-width: 216px;

      & > * {
        width: 100%;
      }
    }
  }
  &-buttons {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 216px;
    min-width: 205px;

    @include media-breakpoint-down(sm) {
      gap: 5px;
    }
  }
}
