<div class="magazine">
  <div class="magazine-wrapper">
    <div class="magazine-content" [class.no-buttons]="user && user.hasValidSubscription">
      <div class="magazine-left">
        <a class="magazine-link" [routerLink]="['hetilap', data?.journalIssue?.urlSlug]">
          <img class="magazine-img" [src]="data?.journalIssue?.image?.thumbnailUrl || data?.journalIssue?.image?.fullSizeUrl" alt="Hetilap" />
        </a>
        <div class="magazine-header">
          <a [routerLink]="['hetilap', data?.journalIssue?.urlSlug]">
            <h3 class="magazine-header-title">{{ data?.journalIssue?.title }}</h3>
          </a>
          <div class="magazine-header-subtitle">{{ data?.journalIssue?.text }}</div>
          <div class="magazine-header-published">{{ data?.journalIssue?.name }}</div>
        </div>
      </div>
      <div class="magazine-divider"></div>
      <div class="magazine-right">
        <ul class="magazine-list">
          <li class="magazine-list-item" *ngFor="let article of data?.articles ?? []">
            <a [routerLink]="getUrl(article)">{{ article.title }}</a>
          </li>
        </ul>
        <div class="magazine-buttons" *ngIf="!user || !user.hasValidSubscription">
          <man-simple-button class="w-100" (click)="onSubscribe()">ELŐFIZETEK A HETILAPRA</man-simple-button>
          <man-simple-button *ngIf="!user" class="w-100" color="secondary" (click)="onRegistration()">REGISZTRÁCIÓ </man-simple-button>
        </div>
      </div>
    </div>
  </div>
</div>
