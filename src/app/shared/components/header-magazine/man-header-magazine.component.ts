import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { BaseComponent, buildArticleUrl, User } from '@trendency/kesma-ui';
import { HeaderMagazineArticle, HeaderMagazineData } from '../weekly-newspaper/man-weekly-newspaper.definition';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { NgFor, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-header-magazine',
  templateUrl: './man-header-magazine.component.html',
  styleUrls: ['./man-header-magazine.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgFor, NgIf, ManSimpleButtonComponent],
})
export class ManHeaderMagazineComponent extends BaseComponent<HeaderMagazineData | null> {
  @Input() user: User | undefined;
  @Output() subscribeClicked = new EventEmitter<void>();
  @Output() registrationClicked = new EventEmitter<void>();

  onSubscribe(): void {
    this.subscribeClicked.next();
  }

  onRegistration(): void {
    this.registrationClicked.next();
  }

  getUrl(article: HeaderMagazineArticle): string[] {
    return buildArticleUrl(article, article.column?.slug);
  }
}
