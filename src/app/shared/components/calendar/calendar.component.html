<ng-container *ngIf="!hideCalendar">
  <ng-container *ngIf="type === CalendarType.Popup">
    <div class="calendar-popup" (click)="handlePopupClose()">
      <img class="calendar-popup-img" [src]="calendar?.popupImageUrl" [alt]="calendar?.title" loading="lazy" />
    </div>
  </ng-container>

  <ng-container *ngIf="type === CalendarType.Advent">
    <div class="calendar-advent" (click)="handlePopupClose()">
      <img class="calendar-advent-img" [src]="calendar?.popupImageUrl" [alt]="calendar?.title" loading="lazy" />
      <div class="calendar-advent-content">
        <ng-container *ngFor="let element of body">
          <ng-container [ngSwitch]="element.type">
            <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
              <ng-container *ngFor="let wysiwygDetail of element?.details">
                <man-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></man-wysiwyg-box>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="type === CalendarType.ContentLink">
    <div class="calendar-content">
      <div class="calendar-content-link">
        <div class="close" (click)="handlePopupClose()"><i class="icon icon-close"></i></div>
        <a [href]="calendar?.url" target="_blank">
          <img class="calendar-content-img" [src]="calendar?.popupImageUrl" [alt]="calendar?.title" loading="lazy" />
        </a>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="type === CalendarType.Article">
    <div class="calendar-article">
      <a *ngIf="calendar?.url; else withoutUrl" [href]="calendar?.url" target="_blank">
        <img *ngIf="calendar?.itemEndImageUrl" [src]="calendar?.itemEndImageUrl" [alt]="calendar?.title" loading="lazy" />
      </a>
    </div>
    <ng-template #withoutUrl>
      <img *ngIf="calendar?.itemEndImageUrl" [src]="calendar?.itemEndImageUrl" [alt]="calendar?.title" loading="lazy" />
    </ng-template>
  </ng-container>
</ng-container>
