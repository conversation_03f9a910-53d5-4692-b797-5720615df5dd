import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Calendar, CalendarType } from '../../definitions';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { ArticleService, CalendarService } from '../../services';
import { ArticleBody, ArticleBodyType, ArticleFileLinkDirective } from '@trendency/kesma-ui';
import { NgForOf, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { ManWysiwygBoxComponent } from '../wysiwyg-box/man-wysiwyg-box.component';

const TWO_MONTHS_IN_SECONDS = 5184000;

@Component({
  selector: 'app-calendar',
  templateUrl: 'calendar.component.html',
  styleUrls: ['calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, Ng<PERSON>orO<PERSON>, Ng<PERSON><PERSON>, NgS<PERSON>Case, ManWysiwygBoxComponent, ArticleFileLinkDirective],
})
export class CalendarComponent implements OnInit {
  calendar: Calendar;
  storedCalendarId: string | undefined;
  hideCalendar = true;
  body: ArticleBody[];

  readonly CalendarType = CalendarType;
  readonly ArticleBodyType = ArticleBodyType;

  @Input() type: CalendarType;
  @Input() shouldClose = true;

  @Output() closed = new EventEmitter<boolean>();

  constructor(
    private readonly calendarService: CalendarService,
    private readonly storageService: StorageService,
    private readonly articleService: ArticleService,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.calendarService.calendar$.subscribe((calendar: Calendar) => {
      this.calendar = calendar;

      if (!this.type) {
        this.type = this.calendar?.type;
      }

      if (this.calendar && !this.shouldClose) {
        this.hideCalendar = false;
      }

      if (this.utilsService.isBrowser()) {
        const calendarObj = this.storageService.getLocalStorageData<{ value: string; expiresAt: number } | undefined>('calendar');
        if (calendarObj) {
          if (calendarObj.expiresAt > new Date().getTime()) {
            this.storedCalendarId = calendarObj.value;
          } else {
            this.storageService.removeLocalStorageData('calendar');
          }
        }
      }

      const hasChanges = this.calendar?.id !== this.storedCalendarId;

      if (!this.calendar || (!hasChanges && this.shouldClose)) {
        this.closed.emit(true);
        return;
      }
      this.hideCalendar = false;

      if (this.calendar.type === CalendarType.Advent) {
        this.body = this.articleService.prepareArticleBody(this.calendar.content);
      }

      this.cdr.markForCheck();
    });
  }

  public handlePopupClose(): void {
    this.closed.emit(true);
    this.storageService.setLocalStorageData('calendar', {
      value: this.calendar.id,
      expiresAt: new Date().getTime() + TWO_MONTHS_IN_SECONDS * 1000,
    });
  }
}
