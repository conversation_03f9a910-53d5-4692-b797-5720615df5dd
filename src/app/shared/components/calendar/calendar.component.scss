@use 'shared' as *;

.calendar {
  &-content,
  &-advent,
  &-popup {
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    overflow: hidden;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &-img {
      object-fit: cover;
      max-width: 800px;
      width: 100%;
    }
  }

  &-advent {
    flex-direction: column;

    &-content {
      overflow: scroll;
      display: block;
      max-width: 800px;
      width: 100%;
      background-color: var(--kui-white);
      padding: 20px 10px 0 10px;
    }
  }

  &-content {
    cursor: default;

    &-link {
      cursor: pointer;
      margin-bottom: 20px;
      position: relative;

      .close {
        background-color: var(--kui-orange-600);
        padding: 12px;
        position: absolute;
        line-height: 0;
        right: 25px;
        top: 25px;
      }

      .icon-close {
        padding: 8px;
      }
    }
  }

  &-article {
    margin-bottom: 20px;
  }
}
