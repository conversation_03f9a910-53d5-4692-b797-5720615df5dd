<form class="txt" [formGroup]="form" (ngSubmit)="onTextSubmit()">
  <div class="txt-input-wrapper">
    <man-counting-text-area
      formControlName="text"
      [rows]="rows"
      [cols]="cols"
      [(focused)]="focus"
      [maxLength]="maxLength"
      [minLength]="minLength"
      [placeholder]="placeholder"
      [resize]="resize"
      [disabled]="disabled"
      [wide]="wide"
    ></man-counting-text-area>
  </div>
  <man-simple-button class="submit-button" [disabled]="!form.valid" [color]="submitButtonColor" [wide]="true">
    <span>{{ submitButtonText }}</span>
  </man-simple-button>
</form>
