import { ChangeDetectionStrategy, Component } from '@angular/core';
import { UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommentAnswerFormComponent } from '@trendency/kesma-ui';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManCountingTextAreaComponent } from '../counting-text-area/man-counting-text-area.component';

@Component({
  selector: 'man-comment-answer-form',
  templateUrl: './man-comment-answer-form.component.html',
  styleUrls: ['./man-comment-answer-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, ReactiveFormsModule, ManCountingTextAreaComponent, ManSimpleButtonComponent],
})
export class ManCommentAnswerFormComponent extends CommentAnswerFormComponent {
  constructor(formBuilder: UntypedFormBuilder) {
    super(formBuilder);
  }
}
