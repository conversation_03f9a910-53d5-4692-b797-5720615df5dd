@use 'shared' as *;

.popup {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9100;

  &-backdrop {
    background-color: rgba(#323232, 0.8); // var(--kui-gray-800)
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9101;
  }

  &-wrapper {
    width: 420px;
    display: block;
    background-color: var(--kui-white);
    border: 5px solid var(--kui-orange-600);
    padding: 40px 30px;
    z-index: 9102;
  }

  &-title {
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    text-transform: uppercase;
    font-family: var(--kui-font-condensed);
    margin: 20px 0 15px 0;
  }

  &-content {
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    margin-bottom: 30px;
  }

  &-buttons-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }
}
