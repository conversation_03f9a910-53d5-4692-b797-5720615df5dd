import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'man-popup',
  templateUrl: 'man-popup.component.html',
  styleUrls: ['man-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManSimpleButtonComponent],
})
export class ManPopupComponent {
  @Input() title?: string;

  @Input() showAcceptButton = true;
  @Input() showCancelButton = true;

  @Input() acceptButtonLabel: string = 'Rendben';
  @Input() cancelButtonLabel: string = 'Mégsem';

  @Input() disabled = false;

  @Output() resultEvent: EventEmitter<boolean> = new EventEmitter<boolean>();

  handleUserChoice(answer: boolean): void {
    if (!this.disabled) {
      this.resultEvent.emit(answer);
    }
  }
}
