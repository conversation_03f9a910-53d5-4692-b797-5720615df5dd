<div class="popup">
  <div class="popup-backdrop" (click)="handleUserChoice(false)"></div>
  <div class="popup-wrapper">
    <div class="popup-title" *ngIf="title">{{ title }}</div>
    <div class="popup-content">
      <ng-content></ng-content>
    </div>
    <div class="popup-buttons-wrapper">
      <man-simple-button class="w-100" *ngIf="showAcceptButton" [disabled]="disabled" (click)="handleUserChoice(true)">{{
        acceptButtonLabel
      }}</man-simple-button>
      <man-simple-button class="w-100" color="outline" *ngIf="showCancelButton" [disabled]="disabled" (click)="handleUserChoice(false)">{{
        cancelButtonLabel
      }}</man-simple-button>
    </div>
  </div>
</div>
