@use 'shared' as *;

:host {
  width: 100%;
  display: block;
  background: var(--kui-black);
  margin-bottom: 20px;
  padding: 15px;

  .subscription-strip {
    color: var(--kui-white);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;

    @include media-breakpoint-down(md) {
      gap: 10px;
    }

    &-title {
      font-weight: 700;
      text-transform: uppercase;
      font-size: 24px;
      line-height: 24px;

      @include media-breakpoint-down(sm) {
        line-height: 14px;
        font-size: 12px;
      }
    }

    .icon {
      width: 12px;
      height: 12px;
    }

    .icon-mandiner-plus {
      width: 24px;
      height: 24px;

      @include media-breakpoint-down(sm) {
        width: 22px;
        height: 22px;
      }
    }
  }
}
