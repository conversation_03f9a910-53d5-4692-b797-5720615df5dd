import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'man-subscription-strip',
  templateUrl: './man-subscription-strip.component.html',
  styleUrls: ['./man-subscription-strip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class ManSubscriptionStripComponent {
  readonly subscribeLink = environment.shopUrls.subscriptions;

  @Input() text: string = '3 hónap előfizetés 1 hónap áráért!';
  @Output() private readonly subscriptionClicked: EventEmitter<boolean> = new EventEmitter<boolean>();

  public onClickSubscription(): void {
    this.subscriptionClicked.emit(true);
  }
}
