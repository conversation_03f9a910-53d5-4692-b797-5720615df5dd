import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'man-related-articles',
  templateUrl: './man-related-articles.component.html',
  styleUrls: ['./man-related-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgIf, RouterLink],
})
export class ManRelatedArticlesComponent extends BaseComponent<ArticleCard[]> {
  public getArticleUrl(article: ArticleCard): string[] {
    return buildArticleUrl(article);
  }
}
