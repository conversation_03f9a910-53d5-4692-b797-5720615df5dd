@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  max-width: 486px;
}

.related-articles-list {
  padding: 10px 20px;
  border-bottom: 1px solid var(--kui-gray-100);
  border-top: 1px solid var(--kui-gray-100);
  &-item {
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 10px;
    &::before {
      content: '';
      border-radius: 50%;
      width: 8px;
      height: 8px;
      background: var(--kui-orange-600);
      position: absolute;
      left: -20px;
      top: 7px;
    }
    &::after {
      content: '';
      width: 80px;
      height: 1px;
      background: var(--kui-orange-600);
      position: absolute;
      bottom: 0px;
      left: 0;
    }
    &-link {
      color: var(--kui-black);
      &:hover {
        color: var(--kui-orange-600);
      }
    }
  }
}
