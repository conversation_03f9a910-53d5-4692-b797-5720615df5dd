import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';

export type ArticleCardComments = Readonly<{
  title: string;
  slug?: string;
  lead?: string;
  publishDate?: Date | string;
  socialData: LikeButtons;
  commentsData: ArticleCardComment[];
  answers: number;
}>;

export type ArticleCardComment = Readonly<{
  hasAnswers: boolean;
  publishDate: Date | string;
  text: string;
  subComments?: ArticleCardComment[];
}>;
