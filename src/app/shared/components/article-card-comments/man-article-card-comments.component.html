<article class="article-card style-TitleLeadDateMeta">
  <a href="#" class="article-card-link">
    <h2 class="article-card-title">
      {{ data?.title }}
    </h2>
  </a>
  <p class="article-card-lead">
    {{ data?.lead }}
  </p>
  <div class="article-card-social">
    <div class="social-left">
      <!-- Like -->
      <div class="social-item">
        <img class="social-icon" src="/assets/images/icons/new-thumbs-up.svg" alt="Like" />
        <div class="social-counter">
          {{ data?.socialData?.like }}
        </div>
      </div>
      <span class="social-divider"></span>
      <!-- Dislike -->
      <div class="social-item">
        <img class="social-icon" src="/assets/images/icons/new-thumbs-down.svg" alt="Dislike" />
        <div class="social-counter">
          {{ data?.socialData?.dislike }}
        </div>
      </div>
      <span class="social-divider"></span>
      <!-- Comment -->
      <div class="social-item">
        <img class="social-icon" src="/assets/images/icons/new-comments.svg" alt="Komment" />
        <div class="social-counter">
          {{ data?.socialData?.comment }}
        </div>
      </div>
    </div>
    <div class="social-right">
      <!-- Bookmark -->
      <div class="social-item">
        <button (click)="isBookMarked = !isBookMarked">
          <ng-template #bookmarked [ngIf]="isBookMarked">
            <img class="social-icon" src="/assets/images/icons/new-bookmark.svg" alt="Könyvjelző" />
          </ng-template>
          <ng-template #notBookmarked [ngIf]="!isBookMarked">
            <img class="social-icon" src="/assets/images/icons/new-bookmark2.svg" alt="Könyvjelző" />
          </ng-template>
        </button>
      </div>
      <span class="social-divider"></span>
      <!-- Share -->
      <div class="social-item">
        <img class="social-icon" src="/assets/images/icons/new-share.svg" alt="Megosztás" />
      </div>
    </div>
  </div>
  <div class="article-card-publish-date">
    {{ data?.publishDate | publishDate }}
  </div>
  <div class="article-card-comment">
    <div class="article-card-comment-toggle">
      <img class="article-card-comment-toggle-chevron" src="/assets/images/icons/chevron-down.svg" alt="" />
      {{ data?.answers }} válasz megtekintése
    </div>
    <div class="article-card-comment-box" *ngFor="let item of data?.commentsData">
      <div class="article-card-publish-date">
        {{ item?.publishDate | publishDate }}
      </div>
      <div class="article-card-comment-box-text">
        {{ item?.text }}
      </div>
      <div *ngIf="!item?.hasAnswers" class="article-card-comment-box-answer">Nem érkezett válasz</div>
    </div>
  </div>
</article>
