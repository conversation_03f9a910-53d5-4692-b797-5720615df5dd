@use 'shared' as *;

:host {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--kui-gray-100);
  padding-bottom: 15px;
}

.article-card {
  margin-bottom: 30px;
  font-family: var(--kui-font-primary);
  &-title {
    font-family: var(--kui-font-secondary);
    font-weight: 700;
    font-size: 28px;
    line-height: 34px;
    margin-bottom: 10px;
  }
  &-link {
    color: var(--kui-black);
  }
  &-lead {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 10px;
  }
  &-publish-date {
    display: block;
    color: var(--kui-gray-300);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    margin-bottom: 15px;
  }
  &-social {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }
  &-comment {
    &-toggle {
      font-weight: 700;
      font-size: 12px;
      line-height: 15px;
      margin-bottom: 20px;
    }
    &-box {
      margin-left: 20px;
      padding: 10px 10px 0 10px;
      border-left: 1px solid var(--kui-gray-200);
      &-text {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 10px;
      }
      &-answer {
        border-bottom: 1px solid var(--kui-gray-200);
        color: var(--kui-gray-300);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        padding-bottom: 10px;
      }
    }
  }
}

.social {
  &-icon {
    fill: var(--kui-orange-600);
    margin-right: 5px;
  }
  &-left,
  &-right,
  &-item {
    display: flex;
  }
  &-counter {
    color: var(--kui-gray-600);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }
  &-divider {
    background: var(--kui-gray-100);
    width: 1px;
    height: 100%;
    margin: 0 10px;
  }
}
