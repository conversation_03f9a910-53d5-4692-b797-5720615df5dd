import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { ArticleCardComments } from './man-article-card-comments.definitions';
import { PublishDatePipe } from '@trendency/kesma-core';
import { NgIf, NgFor } from '@angular/common';

@Component({
  selector: 'man-article-card-comments',
  templateUrl: './man-article-card-comments.component.html',
  styleUrls: ['./man-article-card-comments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, PublishDatePipe],
})
export class ManArticleCardCommentsComponent {
  @Input() data?: ArticleCardComments;
  @Input() socialInteractions?: LikeButtons;

  isBookMarked: boolean = false;
}
