@use 'shared' as *;

:host {
  display: block;

  .quiz {
    &-step {
      font-weight: 800;
      font-size: 20px;
      display: block;
    }

    &-question {
      &-text {
        font-size: 20px;
        font-weight: 800;
        left: 20px;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          left: 10px;
          bottom: 0;
        }
      }

      .answer-list {
        .radio-label {
          font-size: 16px;
          font-weight: 800;
          color: var(--kui-black);
          background-color: var(--kui-gray-500);
          border-bottom: 1px solid var(--kui-white);
          padding: 21px 199px 21px 74px;

          @include media-breakpoint-down(sm) {
            font-size: 14px;
            padding: 20px 20px 20px 64px;
          }

          &:before {
            border: 1px solid var(--kui-gray-100);
            width: 24px;
            height: 24px;
            left: 30px;

            @include media-breakpoint-down(sm) {
              left: 20px;
            }
          }

          &:after {
            width: 16px;
            height: 16px;
            background-color: var(--kui-orange-600);
            left: 34px;

            @include media-breakpoint-down(sm) {
              display: none;
            }
          }

          &.correct {
            background-color: var(--kui-green-300);

            &:before {
              border: 1px solid var(--kui-gray-600);

              @include media-breakpoint-down(sm) {
                border: none;
                background: none;
              }
            }
          }

          &.wrong {
            background-color: var(--kui-red-100);

            &:before {
              border: 1px solid var(--kui-gray-600);

              @include media-breakpoint-down(sm) {
                border: none;
                background: none;
              }
            }
          }

          .extra-label {
            font-size: 14px;
            font-weight: 800;
            letter-spacing: 0;

            @include media-breakpoint-down(sm) {
              left: 18px;
              position: absolute;
              right: auto;
            }

            &-icon {
              width: 24px;
              height: 24px;
            }

            &.correct {
              color: var(--kui-green-600);
            }

            &.wrong {
              color: var(--kui-red-900);
            }
          }
        }
      }
    }

    &-result {
      padding: 24px;
      background-color: var(--kui-orange-600);
      gap: 24px;

      &-status {
        font-size: 40px;
        line-height: 1;
      }

      &-text {
        font-size: 24px;
        margin-bottom: 0;
        line-height: 1;
      }

      &-share {
        border-radius: 5px;
        background: var(--kui-white);
        padding: 5px 10px;

        a {
          font-weight: 400;
        }
      }
    }
    &-rating {
      &-image {
        width: 100%;
        object-fit: cover;
      }
    }
  }
}

.icon.icon-share-fb {
  background-image: url(/assets/images/icons/icon-mandiner-facebook.svg);
  width: 20px;
  margin-top: 0 !important;
}
