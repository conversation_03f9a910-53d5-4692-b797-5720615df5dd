import { ChangeDetectionStrategy, Component } from '@angular/core';
import { QuizComponent } from '@trendency/kesma-ui';
import { <PERSON><PERSON><PERSON>, NgIf, NgClass } from '@angular/common';

@Component({
  selector: 'mandiner-man-quiz',
  templateUrl: 'man-quiz.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/quiz/quiz.component.scss', './man-quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Ng<PERSON><PERSON>, NgIf, NgClass],
})
export class ManQuizComponent extends QuizComponent {
  override get placeholderImage(): string {
    return '';
  }
  get ratingImage(): string {
    return this.rating?.thumbnailUrl || this.rating?.image || '';
  }
}
