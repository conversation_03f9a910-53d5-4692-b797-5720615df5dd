<ng-container [ngSwitch]="styleID">
  <!-- STYLE layout -->
  <ng-container *ngSwitchCase="NewsletterDiverterCardType.Layout">
    <div class="newsletter-diverter-card-layout">
      <div class="newsletter-diverter-card-layout-title">
        <i class="icon icon-m"></i>
        <h3 class="newsletter-diverter-card-layout-title-text">Hírlevél-feliratkozás</h3>
      </div>
      <div class="newsletter-diverter-card-layout-text">
        Ne maradjon le a <b>Mandiner</b> cikke<PERSON><PERSON>l, iratkozzon fel hírlevelünkre! Adja meg a nevét és az e-mail-címét, és elküldjük Önnek a nap legfontosabb
        híreit.
      </div>
      <button class="newsletter-diverter-card-layout-btn-subscribe" type="button" (click)="subscribeClicked.emit()">feliratkozom a hírlevélre</button>
    </div>
  </ng-container>

  <!-- STYLE header -->
  <ng-container *ngSwitchCase="NewsletterDiverterCardType.Header">
    <div class="newsletter-diverter-card-header">
      <div class="newsletter-diverter-card-header-title">
        <i class="icon icon-m"></i>
        <h3 class="newsletter-diverter-card-header-title-text">Hírlevél-feliratkozás</h3>
      </div>
      <div class="newsletter-diverter-card-header-text">
        Ne maradjon le a <b>Mandiner</b> cikkeiről, iratkozzon fel hírlevelünkre! Adja meg a nevét és az e-mail-címét, és elküldjük Önnek a nap legfontosabb
        híreit.
      </div>
      <button class="newsletter-diverter-card-header-btn-subscribe" type="button" (click)="subscribeClicked.emit()">feliratkozom a hírlevélre</button>
    </div>
  </ng-container>

  <!-- STYLE popup -->
  <ng-container *ngSwitchCase="NewsletterDiverterCardType.Popup">
    <div class="newsletter-diverter-card-popup">
      <div class="newsletter-diverter-card-popup-title">
        <i class="icon icon-m"></i>
        <h3 class="newsletter-diverter-card-popup-title-text">Hírlevél-feliratkozás</h3>
      </div>
      <div class="newsletter-diverter-card-popup-text">
        Ne maradjon le a <b>Mandiner</b> cikkeiről, iratkozzon fel hírlevelünkre! Adja meg a nevét és az e-mail-címét, és elküldjük Önnek a nap legfontosabb
        híreit.
      </div>
      <button class="newsletter-diverter-card-popup-btn-subscribe" type="button" (click)="subscribeClicked.emit()">feliratkozom a hírlevélre</button>
      <button class="newsletter-diverter-card-popup-btn-close" type="button" (click)="closeClicked.emit()">bezár</button>
    </div>
  </ng-container>

  <!-- STYLE article -->
  <ng-container *ngSwitchCase="NewsletterDiverterCardType.Article">
    <div class="newsletter-diverter-card-article">
      <div class="newsletter-diverter-card-article-left">
        <div class="newsletter-diverter-card-article-title">
          <i class="icon icon-m"></i>
          <h3 class="newsletter-diverter-card-article-title-text">Hírlevél-feliratkozás</h3>
        </div>

        <div class="newsletter-diverter-card-article-text">
          Ne maradjon le a <b>Mandiner</b> cikkeiről, iratkozzon fel hírlevelünkre! Adja meg a nevét és az e-mail-címét, és elküldjük Önnek a nap legfontosabb
          híreit.
        </div>
      </div>
      <div class="newsletter-diverter-card-article-separator"></div>
      <div class="newsletter-diverter-card-article-right">
        <button class="newsletter-diverter-card-article-btn-subscribe" type="button" (click)="subscribeClicked.emit()">feliratkozom a hírlevélre</button>
      </div>
    </div>
  </ng-container>
</ng-container>
