import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { NewsletterDiverterCardType } from './man-newsletter-diverter-card.definitions';
import { NgSwitch, NgSwitchCase } from '@angular/common';

@Component({
  selector: 'man-newsletter-diverter-card',
  templateUrl: './man-newsletter-diverter-card.component.html',
  styleUrls: ['./man-newsletter-diverter-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSwitch, NgSwitchCase],
})
export class ManNewsletterDiverterCardComponent {
  @Input() set styleID(styleID: NewsletterDiverterCardType) {
    this._type = styleID;
    this.hostClass = `newsletter-diverter-card style-${NewsletterDiverterCardType[styleID].toLowerCase()}`;
  }

  @HostBinding('class') hostClass = '';

  @Output() subscribeClicked = new EventEmitter<void>();
  @Output() closeClicked = new EventEmitter<void>();

  NewsletterDiverterCardType = NewsletterDiverterCardType;

  private _type = NewsletterDiverterCardType.Layout;

  get styleID(): NewsletterDiverterCardType {
    return this._type;
  }
}
