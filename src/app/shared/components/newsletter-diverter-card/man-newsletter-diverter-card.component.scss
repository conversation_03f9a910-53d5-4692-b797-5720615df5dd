@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  width: 100%;

  &.style-layout {
    .newsletter-diverter-card {
      &-layout {
        width: 100%;
        border: 5px solid var(--kui-orange-600);
        padding: 15px 28px;

        @include media-breakpoint-down(lg) {
          padding: 15px;
        }

        .icon-m {
          width: 52px;
          height: 35px;
          min-width: 52px;
          min-height: 35px;
        }
        &-title {
          display: flex;
          align-items: center;
          column-gap: 10px;
          margin-bottom: 20px;
        }
        &-title-text {
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;
          text-transform: uppercase;
          font-family: var(--kui-font-secondary);

          @include media-breakpoint-down(lg) {
            font-size: 18px;
          }
        }
        &-text {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
          margin-bottom: 20px;

          @include media-breakpoint-down(lg) {
            font-size: 14px;
            line-height: 22px;
          }
        }
        &-btn-subscribe {
          height: 38px;
          background-color: var(--kui-orange-600);
          color: var(--kui-white);
          font-weight: 700;
          font-size: 14px;
          line-height: 14px;
          text-transform: uppercase;
          width: 100%;
        }
      }
    }
  }

  &.style-popup {
    .newsletter-diverter-card {
      &-popup {
        max-width: 384px;
        width: 100%;
        border: 5px solid var(--kui-orange-600);
        padding: 15px 28px;

        @include media-breakpoint-down(lg) {
          padding: 15px 16px;
        }

        @include media-breakpoint-down(sm) {
          max-width: none;
        }

        .icon-m {
          width: 52px;
          height: 35px;
          min-width: 52px;
          min-height: 35px;
        }
        &-title {
          display: flex;
          align-items: center;
          column-gap: 10px;
          margin-bottom: 20px;
        }
        &-title-text {
          font-family: var(--kui-font-secondary);
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;
          text-transform: uppercase;
        }
        &-text {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
          margin-bottom: 20px;
        }
        &-btn-subscribe {
          height: 38px;
          background-color: var(--kui-orange-600);
          color: var(--kui-white);
          font-weight: 700;
          font-size: 14px;
          line-height: 14px;
          text-transform: uppercase;
          width: 100%;
          margin-bottom: 20px;
        }
        &-btn-close {
          background: var(--kui-white);
          color: var(--kui-black);
          border: 1px solid var(--kui-orange-600);
          text-transform: uppercase;
          font-weight: 700;
          height: 38px;
          font-size: 14px;
          line-height: 14px;
          width: 100%;
        }
      }
    }
  }

  &.style-header {
    background-color: var(--kui-gray-100);
    box-shadow: inset 0 4px 4px rgba(0, 0, 0, 0.25);

    .newsletter-diverter-card {
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0;
        column-gap: 20px;
        max-width: 1310px;
        margin: 0 auto;

        @include media-breakpoint-down(lg) {
          flex-direction: column;
          padding: 20px 22px;
        }

        .icon-m {
          width: 52px;
          height: 35px;
          min-width: 52px;
          min-height: 35px;
        }

        &-title {
          display: flex;
          flex-direction: row;
          align-items: center;
          column-gap: 10px;

          @include media-breakpoint-down(lg) {
            margin-bottom: 25px;
          }
        }

        &-title-text {
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;
          text-transform: uppercase;
          white-space: nowrap;
          font-family: var(--kui-font-secondary);
        }

        &-text {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
          max-width: 603px;
          width: 100%;

          @include media-breakpoint-down(lg) {
            margin-bottom: 25px;
          }
        }

        &-btn-subscribe {
          height: 38px;
          background-color: var(--kui-black);
          color: var(--kui-white);
          font-weight: 700;
          font-size: 14px;
          line-height: 14px;
          text-transform: uppercase;
          width: auto;
          padding: 12px 16px;
          white-space: nowrap;
        }
      }
    }
  }

  &.style-article {
    .newsletter-diverter-card {
      &-article {
        display: flex;
        column-gap: 30px;
        align-items: center;
        width: 100%;
        border: 5px solid var(--kui-orange-600);
        padding: 15px 17px;
        justify-content: space-between;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          padding: 20px;
        }

        .icon-m {
          width: 52px;
          height: 35px;
          min-width: 52px;
          min-height: 35px;
        }

        &-left {
          width: 100%;
        }

        &-right {
          @include media-breakpoint-down(sm) {
            width: 100%;
          }
        }
        &-separator {
          width: 2px;
          height: 100px;
          background-color: var(--kui-orange-600);

          @include media-breakpoint-down(md) {
            display: none;
          }
        }
        &-title {
          display: flex;
          align-items: center;
          column-gap: 10px;
          margin-bottom: 20px;
        }
        &-title-text {
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;
          text-transform: uppercase;
          font-family: var(--kui-font-secondary);
        }
        &-text {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;

          @include media-breakpoint-down(md) {
            text-align: center;
            margin-bottom: 20px;
          }
        }
        &-btn-subscribe {
          height: 38px;
          background-color: var(--kui-orange-600);
          color: var(--kui-white);
          font-weight: 700;
          font-size: 14px;
          line-height: 14px;
          text-transform: uppercase;
          width: 100%;
          padding: 12px 16px;
          white-space: nowrap;
          @media (max-width: 1060px) {
            height: auto;
            white-space: normal;
          }
          @media (max-width: 400px) {
            height: 38px;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
