import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { ManOpinionCardType } from '../opinion-card/man-opinion-card.types';
import { ManOpinionCardComponent } from '../opinion-card/man-opinion-card.component';
import { NgFor } from '@angular/common';
import { ManOpinionHeaderComponent } from '../opinion-header/man-opinion-header.component';

@Component({
  selector: 'man-opinion-layout',
  templateUrl: './man-opinion-layout.component.html',
  styleUrls: ['./man-opinion-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManOpinionHeaderComponent, NgFor, ManOpinionCardComponent],
})
export class ManOpinionLayoutComponent {
  @Input() data?: ArticleCard[];
  @Input() socialInteractions?: LikeButtons;
  @Input() hideDate = false;

  ManOpinionCardType = ManOpinionCardType;
}
