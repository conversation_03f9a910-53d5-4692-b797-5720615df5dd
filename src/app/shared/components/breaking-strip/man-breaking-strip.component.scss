@use 'shared' as *;

:host {
  display: block;

  &.narrow-view {
    .breaking-strip {
      flex-direction: column;

      &-title,
      &-text {
        text-align: left;
      }
    }
  }
}

.breaking-strip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 18px;
  gap: 10px;
  width: 100%;
  background: var(--kui-orange-600);
  box-shadow: inset 0 4px 4px rgba(0, 0, 0, 0.25);
  font-family: var(--kui-font-secondary);
  font-size: 24px;

  @include media-breakpoint-down(lg) {
    font-size: 20px;
  }
  @include media-breakpoint-down(sm) {
    font-size: 16px;
  }

  &-text {
    color: var(--kui-black);
    font-style: normal;
    font-weight: 700;
    line-height: 24px;

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  &-title {
    color: var(--kui-white) !important;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    text-align: center;
    font-size: 24px;
    font-family: var(--kui-font-secondary);

    @include media-breakpoint-down(lg) {
      font-size: 20px;
    }

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 20px;
    }
  }

  .icon-mandiner-arrow-right-gold,
  .icon-mandiner-breaking-strip-arrow {
    width: 12px;
    height: 12px;
  }

  &-black {
    background: var(--kui-black);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 18px;
    overflow: hidden;
    width: 100%;
    gap: 15px;

    &-item {
      color: var(--kui-white);
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 15px;

      &-title {
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
        text-transform: uppercase;
        font-family: var(--kui-font-secondary);

        @include media-breakpoint-down(sm) {
          line-height: 12px;
          font-size: 12px;
        }

        &-span {
          display: none;
          @media (min-width: 420px) {
            display: inline-block;
          }
        }
      }

      .icon-mandiner-breaking {
        width: 12px;
        height: 12px;

        @include media-breakpoint-down(sm) {
          &.last {
            display: none;
          }
        }

        @include media-breakpoint-down(md) {
          &.last {
            display: none;
          }
        }
      }
    }
  }
}
