import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { BaseComponent, ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';
import { BreakingType } from './man-breaking-strip.definitions';
import { UtilService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { NgSwitch, NgSwitchCase, NgFor } from '@angular/common';

@Component({
  selector: 'man-breaking-strip',
  templateUrl: './man-breaking-strip.component.html',
  styleUrls: ['./man-breaking-strip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSwitch, NgSwitchCase, RouterLink, NgFor],
})
export class ManBreakingStripComponent extends BaseComponent<ArticleCard> implements OnInit {
  @HostBinding('class') hostClass = '';
  @Input() desktopWidth = 12;
  @Input() isSidebar = false;

  private readonly utils = inject(UtilService);

  articleLink: string[] = [];
  breakingTextLength: number = 8;

  protected override setProperties(): void {
    super.setProperties();
    this.articleLink = this.data ? buildArticleUrl(this.data) : [];
  }
  get isNarrowView(): boolean {
    return !!(this.desktopWidth < 5 || this.isSidebar);
  }

  @Input() type?: BreakingType;
  BreakingType = BreakingType;

  override ngOnInit(): void {
    super.ngOnInit();

    const width = this.utils.isBrowser() ? window.innerWidth : 1920;
    if (width <= 576) {
      // mobil
      this.breakingTextLength = 3;
    } else if (width <= 768) {
      // tablet
      this.breakingTextLength = 5;
    }

    if (this.isNarrowView) {
      this.hostClass = 'narrow-view';
    }
  }

  getIteratorArray(length: number): number[] {
    return new Array(length).fill(0);
  }
}
