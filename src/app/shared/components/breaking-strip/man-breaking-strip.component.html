<ng-container [ngSwitch]="type">
  <ng-container *ngSwitchCase="BreakingType.Default">
    <a class="breaking-strip" [routerLink]="articleLink">
      <div class="breaking-strip-text">{{ data?.label?.text ?? 'Rendkívüli!' }}</div>
      <h3 class="breaking-strip-title">{{ data?.title }}</h3>
      <i class="icon icon-mandiner-breaking-strip-arrow"></i>
    </a>
  </ng-container>

  <ng-container *ngSwitchCase="BreakingType.Black">
    <div class="breaking-strip-black">
      <a class="breaking-strip-black-item" *ngFor="let link of getIteratorArray(breakingTextLength); last as last" [routerLink]="articleLink">
        <div class="breaking-strip-black-item-title">Rendkívüli</div>
        <i [class.last]="last" class="icon icon-mandiner-breaking"></i>
      </a>
    </div>
  </ng-container>
</ng-container>
