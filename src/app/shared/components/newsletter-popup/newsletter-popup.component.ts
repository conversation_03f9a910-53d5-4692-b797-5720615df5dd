import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, NEWSLETTER_COMPONENT_TYPE } from '@trendency/kesma-ui';
import { ManNewsletterDiverterCardComponent } from '../newsletter-diverter-card/man-newsletter-diverter-card.component';
import { NgIf } from '@angular/common';
import { NewsletterDiverterCardType } from '../../definitions';

const NEWSLETTER_CLOSED_STORAGE_KEY = 'newsletterClosed';

@Component({
  selector: 'app-newsletter-popup',
  templateUrl: './newsletter-popup.component.html',
  styleUrls: ['./newsletter-popup.component.scss'],

  imports: [ManNewsletterDiverterCardComponent, NgIf],
})
export class NewsletterPopupComponent implements OnInit {
  isPopupOpen = false;

  readonly NewsletterDiverterCardType = NewsletterDiverterCardType;

  constructor(
    private readonly storageService: StorageService,
    private readonly analyticsService: AnalyticsService,
    private readonly utilService: UtilService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (!this.utilService.isBrowser()) {
      return;
    }

    const newsletterClosedExp = this.storageService.getLocalStorageData<number | undefined>('newsletterClosed');
    if (newsletterClosedExp) {
      if (newsletterClosedExp > new Date().getTime()) {
        return; // newsletter popup is already closed
      }

      this.storageService.removeLocalStorageData('newsletterClosed'); // newsletter popup is closed, but expired
    }

    // newsletter popup is not closed yet or expired
    setTimeout(() => {
      this.isPopupOpen = true;
      this.analyticsService.newsLetterPopupVisible();
      this.changeDetectorRef.detectChanges();
    }, 2000);
  }

  onCloseClicked(): void {
    this.isPopupOpen = false;
    this.setNewsletterClosedStorage();
  }

  onSubscribeClicked(): void {
    this.isPopupOpen = false;
    this.setNewsletterClosedStorage();
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.POPOVER);
    window.open('/hirlevel-feliratkozas', '_blank');
  }

  private setNewsletterClosedStorage(): void {
    const now: Date = new Date();
    const expiresAt = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7).getTime();
    this.storageService.setLocalStorageData(NEWSLETTER_CLOSED_STORAGE_KEY, expiresAt);
  }
}
