<a [routerLink]="routerLink || ['/', 'galeria', data?.slug]">
  <div class="gallery-card-image-box">
    <div class="gallery-card-image-box-overlay" *ngIf="!data?.isAdult">
      <i class="icon icon-gallery"></i>
    </div>
    @if (data?.isAdult && !isAdultChoice) {
      <kesma-adult-overlay>
        <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
      </kesma-adult-overlay>
    } @else {
      <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
    }
    <ng-template #galleryImage>
      <img class="gallery-card-image-box-image" [src]="data?.highlightedImageUrl || 'assets/images/placeholder-16-9.svg'" alt="" loading="lazy" />
    </ng-template>
  </div>
  <h2 class="gallery-card-title">
    {{ data?.title }}
  </h2>
</a>
<p class="gallery-card-lead">
  {{ data?.description }}
</p>
