@use 'shared' as *;

:host {
  display: block;

  .gallery-card {
    &-image-box {
      position: relative;
      margin-bottom: 5px;

      &-overlay {
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        background: rgba(0, 0, 0, 0.5);
        width: 100%;
        height: 100%;

        .icon-gallery {
          @include media-breakpoint-up(sm) {
            max-width: 48px;
            width: 20%;
            max-height: 37px;
            height: 28%;
          }
        }
      }

      &-image {
        width: 100%;
        object-fit: cover;
        aspect-ratio: 16/9;
      }
    }

    &-title {
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      margin-bottom: 5px;
      font-family: var(--kui-font-secondary);
    }

    &-lead {
      font-weight: 400;
      font-size: 16px;
      line-height: 21px;
    }
  }
}
