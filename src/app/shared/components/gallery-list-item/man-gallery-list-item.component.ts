import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { BaseComponent, GalleryData, AdultOverlayComponent } from '@trendency/kesma-ui';
import { StorageService } from '@trendency/kesma-core';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-gallery-list-item',
  templateUrl: './man-gallery-list-item.component.html',
  styleUrls: ['./man-gallery-list-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, AdultOverlayComponent, NgTemplateOutlet],
})
export class ManGalleryListItemComponent extends BaseComponent<GalleryData> {
  @Input() routerLink?: string | string[];

  isAdultChoice = false;

  private readonly storage = inject(StorageService);

  override ngOnInit(): void {
    super.ngOnInit();
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
  }
}
