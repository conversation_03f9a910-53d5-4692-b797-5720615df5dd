import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommentCardComponent, PopoverDirective } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { LowerCasePipe, NgClass, <PERSON><PERSON>orOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-comment-card',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/comment-card/comment-card.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/comment-card/comment-card.component.scss',
    './man-comment-card.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormatPipeModule, NgIf, RouterLink, NgForOf, NgClass, PopoverDirective, NgTemplateOutlet, LowerCasePipe],
})
export class ManCommentCardComponent extends CommentCardComponent {
  @Input() override closeFormIcon = 'mandiner-close';
  @Input() override openFormIcon = 'man-answer';
  @Input() override upVoteIcon = 'man-upvote';
  @Input() override downVoteIcon = 'man-downvote';
  @Input() override answerToDividerIcon = 'man-arrow-right';
}
