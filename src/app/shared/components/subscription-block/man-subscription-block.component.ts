import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'man-subscription-block',
  templateUrl: './man-subscription-block.component.html',
  styleUrls: ['./man-subscription-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManSimpleButtonComponent, RouterLink, NgIf],
})
export class ManSubscriptionBlockComponent implements OnInit {
  @Input() buttonText?: string;
  @Input() isLoggedIn?: boolean;
  redirectUrl?: string;

  constructor(private readonly router: Router) {}

  ngOnInit(): void {
    this.redirectUrl = this.router.url;
  }

  subscribeClicked(): void {
    window.open(environment.shopUrls.subscriptions);
  }
}
