<div class="subscription-block-title">
  <strong>Ez a tartalom csak előfizetők részére elérhető.</strong>
</div>

<man-simple-button (click)="subscribeClicked()">{{ buttonText }}</man-simple-button>

<div class="subscription-block-member" *ngIf="!isLoggedIn">
  M<PERSON>r el<PERSON>fi<PERSON>tőnk?
  <a class="subscription-block-member-login" [routerLink]="['/', 'bejelentkezes']" [queryParams]="{ redirect: redirectUrl }">Jelentkezzen be!</a>
</div>
