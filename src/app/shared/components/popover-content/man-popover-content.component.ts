import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PopoverContentComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'man-popover-content',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/popover-content/popover-content.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/popover-content/popover-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ManPopoverContentComponent extends PopoverContentComponent {}
