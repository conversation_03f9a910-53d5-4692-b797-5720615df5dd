import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, inject, Input, OnDestroy, Output, ViewChild } from '@angular/core';
import { Header } from '../../definitions';
import { BaseComponent, CityWeatherCurrent, DossierData, RelatedType, SimplifiedMenuItem, TrendingTag, User } from '@trendency/kesma-ui';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { filter, skip, switchMap, tap } from 'rxjs/operators';
import { BehaviorSubject, fromEvent, Subject, takeUntil } from 'rxjs';
import { StorageService, BypassPipe } from '@trendency/kesma-core';
import { DOCUMENT, NgClass, NgFor, NgIf, NgTemplateOutlet, AsyncPipe } from '@angular/common';
import { ManSubscriptionStripComponent } from '../subscription-strip/man-subscription-strip.component';
import { ManDossierListComponent } from '../dossier-list/man-dossier-list.component';
import { ManTrendingTagsComponent } from '../trending-tags/man-trending-tags.component';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { FormsModule } from '@angular/forms';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'man-header',
  templateUrl: './man-header.component.html',
  styleUrls: ['./man-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    NgClass,
    NgFor,
    NgIf,
    NgTemplateOutlet,
    FormsModule,
    ManSimpleButtonComponent,
    ManTrendingTagsComponent,
    ManDossierListComponent,
    ManSubscriptionStripComponent,
    AsyncPipe,
    BypassPipe,
  ],
})
export class ManHeaderComponent extends BaseComponent<Header> implements OnDestroy {
  private readonly document = inject(DOCUMENT);
  readonly subscribeLink = environment.shopUrls.subscriptions;

  @ViewChild('headerSearchBar') headerSearchBar?: ElementRef;
  @ViewChild('iconMandinerSearch') iconMandinerSearch?: ElementRef;
  @Input() currentWeather?: CityWeatherCurrent;
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() topMenu: SimplifiedMenuItem[] = [];
  @Input() dossiers: DossierData[] = [];
  @Input() trendingTags: TrendingTag[] = [];

  @Input() facebookLink = 'https://www.facebook.com/mandiner.hu/';
  @Input() instagramLink = 'https://www.instagram.com/mandiner.hu/?hl=hu';
  @Input() twitterLink = 'https://twitter.com/mandiner?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor';
  @Input() linkedInLink = 'https://www.linkedin.com/company/mandiner?originalSubdomain=hu';
  @Input() youtubeLink = 'https://www.youtube.com/user/MandinerTV/featured';
  @Input() authorsLink = ['/', 'szerzo'];
  @Input() newsletterLink = ['/', 'hirlevel-feliratkozas'];

  @Input() user: User | undefined;
  private readonly destroy$ = new Subject<void>();

  isUserMenuOpen = false;

  hideElement = false;
  showSearchDropdown = false;
  isHamburgerMenuOpen = false;
  activeItem = -1;
  isSearchBarOpen$ = new BehaviorSubject<boolean>(false);
  searchPhrase = '';
  isSearchDisabled = false;

  currentDate = format(new Date(), 'MM. dd.');
  currentDayName = format(new Date(), 'EEEE', { locale: hu });

  isHomePage = true;

  @Output() subscribeClicked = new EventEmitter<void>();
  @Output() registrationClicked = new EventEmitter<void>();

  public readonly RelatedType = RelatedType;

  constructor(
    private readonly router: Router,
    private readonly changeDetectionRef: ChangeDetectorRef,
    private readonly storageService: StorageService
  ) {
    super();

    router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        tap((route: any) => {
          this.isSearchDisabled = !!route.url.includes('/kereses');
          this.changeDetectionRef.detectChanges();
        }),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  redirectToSubscriptions(): void {
    this.isUserMenuOpen = !this.isUserMenuOpen;
    window.open(environment.shopUrls.subscriptions);
  }

  override ngOnInit(): void {
    super.ngOnInit();

    this.detectUrlChanges();

    /**
     * This handles clicking outside the searchbar, when it is actually open.
     * It will only create an event listener when the searchbar is currently open,
     * and the event listener will be removed when we click outside or close the search bar.
     */
    this.isSearchBarOpen$
      .pipe(
        //Only do it when we open the bar.
        filter((openState) => openState),
        switchMap(() => {
          return fromEvent<Event>(this.document, 'click').pipe(
            //Skip the first click, because that is the one that triggered to open the search bar.
            skip(1),

            tap((event: Event) => {
              if (this.headerSearchBar?.nativeElement.contains(event.target)) {
                return;
              }
              //Check where the click was happened.
              if (this.iconMandinerSearch?.nativeElement.contains(event.target)) {
                this.isSearchBarOpen$.next(false);
              } else {
                const target = event.target as HTMLElement;
                const isInSearchBar =
                  this.isSearchBarOpen$.value &&
                  (target?.classList.contains('icon-mandiner-hamburger-search') || target?.classList.contains('header-search-button-desktop'));
                if (!isInSearchBar) {
                  this.isSearchBarOpen$.next(false);
                }
              }
            }),
            takeUntil(this.isSearchBarOpen$.pipe(skip(1)))
          );
        })
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onHamburgerLinkClicked(item: SimplifiedMenuItem): void {
    if (item?.children?.length) {
      return;
    }
    this.router.navigate(item?.link as string[]).then();
    this.closeHamburgerMenu();
  }

  activateItem(index: number): void {
    this.activeItem = this.activeItem && this.activeItem !== -1 ? -1 : index;
  }

  toggleSearchBar(): void {
    this.isSearchBarOpen$.next(!this.isSearchBarOpen$.value);
  }

  closeSearchDropdown(): void {
    this.showSearchDropdown = false;
    //this.hideElement = false;
    this.isSearchBarOpen$.next(false);
  }

  openHamburgerMenu(): void {
    this.isHamburgerMenuOpen = true;
  }

  closeHamburgerMenu(): void {
    this.isHamburgerMenuOpen = false;
    this.activeItem = -1;
  }

  handleUserMenuClick(): void {
    this.storageService.setLocalStorageData('loginRedirectUrl', this.router.url);
    this.user ? (this.isUserMenuOpen = !this.isUserMenuOpen) : this.router.navigate(['/', 'bejelentkezes']);
  }

  onSearch(): void {
    this.router.navigate(['/', 'kereses'], { queryParams: { global_filter: this.searchPhrase } });
    this.closeHamburgerMenu();
    this.searchPhrase = '';
    this.isSearchBarOpen$.next(false);
  }

  logoClick(): void {
    if (this.isHomePage) {
      window.location.href = '/';
    } else {
      this.router.navigate(['/']);
    }
  }

  private detectUrlChanges(): void {
    this.isHomePage = this.router.url === '/';
    this.router.events.pipe(filter((router) => router instanceof NavigationEnd)).subscribe((router) => {
      const url = (router as NavigationEnd)?.url;
      this.isHomePage = url === '/';
      this.changeDetectionRef.markForCheck();
    });
  }

  handleLogout(): void {
    this.isUserMenuOpen = !this.isUserMenuOpen;
    this.storageService.setLocalStorageData('loginRedirectUrl', null);
    this.router.navigate(['/kijelentkezes']).then();
  }
}
