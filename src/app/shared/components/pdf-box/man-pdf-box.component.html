<div class="wrapper">
  <h1 class="box-title" *ngIf="data?.boxTitle">{{ data?.boxTitle }}</h1>
  <div class="media-offer-box" [ngClass]="data?.boxTitle ? '' : 'no-box-title'">
    <p class="title">{{ data?.title }}</p>
    <p class="lead">{{ data?.lead }}</p>
    <div class="download">
      <a [href]="data?.btnUrl" [target]="data?.target" download class="download-link">
        <img src="assets/images/icons/download-file.svg" class="download-img" alt="letöltés" />
        <span class="download-text">Letöltés</span>
      </a>
    </div>
  </div>
</div>
