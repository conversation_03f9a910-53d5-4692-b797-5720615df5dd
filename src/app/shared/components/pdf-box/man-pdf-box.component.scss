@use 'shared' as *;

.wrapper {
  width: 100%;

  .box-title {
    font-weight: 700;
    font-size: 24px;
    padding-bottom: 20px;
    font-family: var(--kui-font-primary);
    line-height: 24px;
    color: var(--kui-orange-600);
    min-height: 42px;
  }

  .media-offer-box {
    background: var(--kui-orange-600);

    text-transform: uppercase;
    text-align: center;
    color: var(--kui-white);
    font-size: 20px;
    padding: 24px 52.5px;
    display: flex;
    flex-direction: column;

    .box-title {
      height: 42px;
      color: var(--kui-white);
      font-weight: 600;
      font-size: 20px;
      line-height: 20px;
      font-style: normal;
    }

    .title {
      color: var(--kui-white);
      font-weight: 600;
      font-size: 20px;
      line-height: 20px;
      font-style: normal;
    }
    .lead {
      font-weight: 800;
      color: var(--kui-white);
      padding-bottom: 20px;
      line-height: 20px;
    }

    .download {
      &-link {
        background: var(--kui-black);
        color: var(--kui-white);
        padding: 12px 18.5px;
        display: inline-block;

        .download-img {
          margin-right: 12.5px;
        }

        .download-text {
          font-size: 16px;
          font-weight: 600;
          line-height: 16px;
          text-transform: uppercase;
          vertical-align: middle;
        }
      }
    }
  }

  .no-box-title {
    margin-top: 44px;
  }
}
