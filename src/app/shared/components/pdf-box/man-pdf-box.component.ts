import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PdfBoxDefinitions } from './man-pdf-box.definitions';
import { NgIf, NgClass } from '@angular/common';

@Component({
  selector: 'man-pdf-box',
  templateUrl: './man-pdf-box.component.html',
  styleUrls: ['./man-pdf-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass],
})
export class ManPdfBoxComponent {
  @Input() data?: PdfBoxDefinitions;
}
