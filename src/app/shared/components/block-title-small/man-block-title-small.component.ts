import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ManBlockTitleRowComponent } from '../block-title-row/man-block-title-row.component';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-block-title-small',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/block-title-row/block-title-row.component.html',
  styleUrls: ['./man-block-title-small.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf],
})
export class ManBlockTitleSmallComponent extends ManBlockTitleRowComponent {}
