import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AdultComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'man-adult',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/adult/adult.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/adult/adult.component.scss', './man-adult.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ManAdultComponent extends AdultComponent {
  override adultLead = `<p>Ez a tartalom olyan elemeket tartalmazhat, amely<PERSON> a hatályos jogszabályok szerint kiskorúakra károsak lehetnek.
   Ha azt szeretné, hogy az ilyen tartalmakhoz erről a számítógépről kiskorú ne fér<PERSON>sen hozz<PERSON>, hasz<PERSON><PERSON>jon szűrőprogramot!
   A javasolt szűrőprogram elérhető <a class="adult-link"href="https://mte.hu/gyermekbarat-internet/" target="_blank">ide kattintva.</a></p>`;

  override adultText = `
  Ha elmúlt 18 éves, kattintson az „Elmúltam 18 éves éves” gombra, és ez a tartalom elérhető lesz önnek.
  Ha nem múlt el 18 éves, kattintson a „Nem múltam el 18 éves éves” gombra, és ez a tartalom nem lesz elérhető önnek.`;

  override acceptedText = 'Elmúltam 18 éves';
  override declinedText = 'Nem múltam el 18 éves';
}
