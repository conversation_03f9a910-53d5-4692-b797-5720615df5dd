@use 'shared' as *;

:host {
  width: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.95);
  position: fixed;
  z-index: 9000;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  .adult-lead ::ng-deep {
    .adult-link {
      color: var(--kui-orange-600);
    }
  }
}

.adult {
  color: var(--kui-white);
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  &-warning-circle {
    border-width: 12px;
    border-color: var(--kui-red-900);
    color: var(--kui-white);
    height: 100px;
    width: 100px;
  }

  &-wrapper {
    width: 800px;
    flex-direction: column;
    justify-content: center;

    &-left {
      margin: 0 auto;
    }

    &-right {
      margin-left: 0;
      width: 100%;
    }
  }

  &-lead {
    font-weight: 700;
    font-size: 18px;
    line-height: 24px;
  }

  &-text {
    font-size: 18px;
    line-height: 24px;
    font-weight: 400;
    color: var(--kui-white);
  }

  @include media-breakpoint-down(md) {
    &-lead,
    &-text {
      font-size: 16px;
      line-height: 22px;
    }
  }

  &-title {
    &-item {
      margin-top: 20px;
      color: var(--kui-white);
      font-family: var(--kui-font-secondary);
      text-transform: none;
      text-align: center;
    }

    &:after {
      display: none;
    }
  }

  &-bottom {
    width: 100%;
    justify-content: center;
    margin: 10px 0;
  }

  &-button {
    width: 216px;
    font-weight: 700;
    line-height: 14px;
    border-radius: 0;
    text-transform: uppercase;
    background: var(--kui-red-900);

    &-light {
      background: var(--kui-green-600);
    }
  }
}
