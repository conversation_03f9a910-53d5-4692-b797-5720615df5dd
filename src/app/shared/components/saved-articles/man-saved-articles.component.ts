import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardType, LikeButtons } from '../../definitions';
import { ManSearchFilterComponent } from '../search-filter/man-search-filter.component';
import { NgForOf } from '@angular/common';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';

@Component({
  selector: 'man-saved-articles',
  templateUrl: './man-saved-articles.component.html',
  styleUrls: ['./man-saved-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [ManSearchFilterComponent, NgForOf, ManArticleCardComponent, ManSimpleButtonComponent],
})
export class ManSavedArticlesComponent {
  @Input() data?: ArticleCard[];
  @Input() styleID?: ArticleCardType;
  @Input() socialInteractions?: LikeButtons;
  @Input() buttonText?: string;

  ArticleCardType = ArticleCardType;
}
