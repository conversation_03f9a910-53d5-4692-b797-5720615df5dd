@use 'shared' as *;

.profile {
  &-divider {
    width: 100%;
    height: 1px;
    background: var(--kui-gray-300);
    margin-bottom: 20px;
  }

  &-title {
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  &-filter {
    &-sub {
      padding-left: 20px;
    }

    &-checkbox {
      display: flex;
      justify-content: space-between;
      position: relative;
      padding-left: 36px;
      margin-bottom: 10px;
      cursor: pointer;
      font-size: 22px;
      user-select: none;

      &:hover input ~ .profile-filter-checkbox-checkmark {
        background-color: var(--kui-white);
        border: 1px solid var(--kui-orange-600);
      }

      &-text {
        font-weight: 400;
        font-size: 16px;
      }

      &-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
      }

      &-checkmark {
        position: absolute;
        top: 8px;
        left: 0;
        height: 16px;
        width: 16px;
        background-color: var(--kui-white);
        border: 1px solid var(--kui-black);
        border-radius: 2px;

        &:after {
          content: '';
          position: absolute;
          display: none;
        }

        &:after {
          left: 4px;
          top: 0;
          width: 6px;
          height: 12px;
          border: solid var(--kui-white);
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      }

      input:checked ~ .profile-filter-checkbox-checkmark {
        background-color: var(--kui-black);
        border: 1px solid var(--kui-black);
      }

      input:checked ~ .profile-filter-checkbox-text {
        font-weight: 700;
      }

      input:checked ~ .profile-filter-checkbox-checkmark:after {
        display: block;
      }
    }
  }
}
