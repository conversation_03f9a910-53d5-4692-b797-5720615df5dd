<div class="profile-filter">
  <div class="profile-divider"></div>
  <h2 class="profile-title">Rovatok követése</h2>
  <ng-container *ngIf="data.length > 0; else empty">
    <ng-container *ngFor="let item of data">
      <label [for]="item.slug" class="profile-filter-checkbox">
        <div class="profile-filter-checkbox-wrap">
          <input
            (ngModelChange)="changeCategory.emit(item)"
            [(ngModel)]="item.isFollowed"
            [disabled]="loadingSlug === item.slug"
            [id]="item.slug"
            class="profile-filter-checkbox-input"
            type="checkbox"
          />
          <span class="profile-filter-checkbox-checkmark"></span>
          <span class="profile-filter-checkbox-text"> {{ item.title }} {{ loadingSlug === item.slug ? '(Kérem várjon...)' : '' }} </span>
        </div>
      </label>
    </ng-container>
  </ng-container>
  <ng-template #empty>
    <p><PERSON><PERSON><PERSON><PERSON> rovatok.</p>
  </ng-template>
</div>
