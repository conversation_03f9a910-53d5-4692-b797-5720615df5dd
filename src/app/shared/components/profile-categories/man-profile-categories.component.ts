import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FollowedColumn } from '@trendency/kesma-ui';
import { FormsModule } from '@angular/forms';
import { NgIf, NgFor } from '@angular/common';

@Component({
  selector: 'man-profile-categories',
  templateUrl: './man-profile-categories.component.html',
  styleUrls: ['./man-profile-categories.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, FormsModule],
})
export class ManProfileCategoriesComponent {
  @Input() data: FollowedColumn[] = [];
  @Input() loadingSlug: string | null = null;
  @Output() changeCategory: EventEmitter<FollowedColumn> = new EventEmitter<FollowedColumn>();
}
