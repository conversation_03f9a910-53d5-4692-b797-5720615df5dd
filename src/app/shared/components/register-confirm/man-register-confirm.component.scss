@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
}

.register-confirm {
  max-width: 384px;
  margin: auto;

  &-wrapper {
    max-width: 792px;
    margin: auto;
  }

  &-header {
    &-title {
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      margin-bottom: 20px;
    }

    &-text {
      font-weight: 400;
      font-size: 16px;
      margin-bottom: 20px;

      &-link {
        color: var(--kui-orange-600);
        text-decoration: underline;
      }
    }
  }

  &-row {
    margin-bottom: 10px;
  }

  &-label {
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;

    &-password {
      display: flex;
      justify-content: space-between;

      &-link {
        color: var(--kui-black-700);
        font-weight: 400;
        font-size: 12px;
        text-decoration: underline;
        transition: all 0.3s ease-in-out;

        &:hover {
          color: var(--kui-orange-600);
        }
      }
    }
  }

  &-input {
    border: 1px solid var(--kui-gray-400);
    display: block;
    width: 100%;
    height: 40px;
    padding: 10px;
    margin-bottom: 5px;

    &-password {
      position: relative;

      &-img {
        position: absolute;
        right: 10px;
        top: 13px;
      }
    }
  }

  &-small {
    margin-bottom: 15px;
    display: block;
  }

  &-checkboxes {
    margin: 20px 0 10px;
  }

  &-checkbox-item {
    display: flex;
  }

  &-checkbox {
    margin-bottom: 30px;
    display: flex;
    align-self: flex-start;

    &-label {
      margin-bottom: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      margin-left: 6px;

      &-link {
        color: var(--kui-orange-600);
        text-decoration: underline;
      }
    }
  }

  &-or {
    display: block;
    text-align: center;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 12px;
    margin: 10px 0;
  }
}
