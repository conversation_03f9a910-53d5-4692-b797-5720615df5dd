import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgForm, FormsModule } from '@angular/forms';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';

@Component({
  selector: 'mandiner-man-register-confirm',
  templateUrl: './man-register-confirm.component.html',
  styleUrls: ['./man-register-confirm.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, ManSimpleButtonComponent],
})
export class ManRegisterConfirmComponent {
  @Input() privacyNotice?: string;
  @Input() termsOfUse?: string;
  @Input() articleTitle?: string;
  @Input() buttonText?: string;

  @Output() formSubmitted = new EventEmitter<object>();

  emitFormData(form: NgForm): void {
    form.valid && this.formSubmitted.emit(form.value);
  }
}
