@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-primary);

  .twenty-four {
    &-title {
      font-family: var(--font-kui-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      line-height: 24px;
      margin-bottom: 10px;
    }

    &-list {
      max-height: 300px;
      overflow-y: scroll;
      padding-right: 15px;
      scrollbar-color: var(--kui-orange-600) var(--kui-gray-100);
      scrollbar-width: auto;
      -webkit-overflow-scrolling: auto;

      &::-webkit-scrollbar {
        width: 6px;
        background-color: var(--kui-gray-100);
        border-radius: 20px;

        &-thumb {
          border-radius: 20px;
          background-color: var(--kui-orange-600);
        }
      }

      &-card {
        margin-bottom: 0;
      }
    }
  }
}
