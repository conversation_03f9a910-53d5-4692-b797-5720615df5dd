import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgFor } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-twentyfour-hour-news',
  templateUrl: './man-twentyfour-hour-news.component.html',
  styleUrls: ['./man-twentyfour-hour-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgFor, ManArticleCardComponent],
})
export class ManTwentyfourHourNewsComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;

  @Input() blockTitle: string = '24 óra hírei';
}
