import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleFileLinkDirective, ImageLightboxComponent, WysiwygBoxComponent } from '@trendency/kesma-ui';
import { NgForOf, NgIf } from '@angular/common';
import { BypassPipe, RunScriptsDirective } from '@trendency/kesma-core';

@Component({
  selector: 'man-wysiwyg-box',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.scss', './man-wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, BypassPipe, ArticleFileLinkDirective, RunScriptsDirective, ImageLightboxComponent],
})
export class ManWysiwygBoxComponent extends WysiwygBoxComponent {}
