import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { DateRangePickerComponent } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { NgIf, TitleCasePipe } from '@angular/common';

@Component({
  selector: 'man-date-range-picker',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/date-range-picker/date-range-picker.component.html',
  styleUrls: ['../../../../../node_modules/flatpickr/dist/flatpickr.min.css', './man-date-range-picker.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormatPipeModule, NgIf, TitleCasePipe],
})
export class ManDateRangePickerComponent extends DateRangePickerComponent {}
