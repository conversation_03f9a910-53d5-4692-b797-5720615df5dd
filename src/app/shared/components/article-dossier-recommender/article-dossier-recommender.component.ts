import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { BasicDossier, DossierArticle, DossierData } from '@trendency/kesma-ui';
import { Observable, map, BehaviorSubject, combineLatest } from 'rxjs';
import { ApiService } from 'src/app/shared/services/api.service';
import { RouterLink } from '@angular/router';
import { AsyncPipe, NgForOf } from '@angular/common';
import { ArticleCardType } from '../../definitions';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';

const DOSSIER_REQUEST_ARTICLE_COUNT = 5;

@Component({
  selector: 'app-article-dossier-recommender',
  templateUrl: './article-dossier-recommender.component.html',
  styleUrls: ['./article-dossier-recommender.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Router<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, ManArticle<PERSON>ardComponent],
})
export class ArticleDossierRecommenderComponent implements OnInit, OnChanges {
  @Input() subsequentDossier: BasicDossier;
  @Input() excludedArticleSlug: string;

  excludedSlug$: BehaviorSubject<string>;

  dossierArticles$: Observable<DossierArticle[]>;

  dossierCardData$: Observable<DossierData>;
  articleCardType = ArticleCardType;

  constructor(private readonly api: ApiService) {}

  ngOnInit(): void {
    if (this.subsequentDossier.slug) {
      this.excludedSlug$ = new BehaviorSubject<string>(this.excludedArticleSlug);

      this.dossierArticles$ = this.api
        // The dossier card needs 4 articles,
        // but if the excluded parameter present we need 5 articles, to check whether the current article is in the array.
        .getDossier(this.subsequentDossier.slug, 0, DOSSIER_REQUEST_ARTICLE_COUNT)
        .pipe(map((res) => res.data));
      this.dossierCardData$ = combineLatest({ slug: this.excludedSlug$, articles: this.dossierArticles$ }).pipe(
        map(({ articles, slug }) => this.excludeArticleBySlug(articles, slug).slice(0, 3)),
        map((articles) => {
          return {
            title: this.subsequentDossier.title,
            headerImage: this.subsequentDossier.thumbnailUrl ?? '',
            mainArticle: { publishDate: '', slug: '', title: '' },
            tag: {
              title: this.subsequentDossier.title,
              slug: this.subsequentDossier.slug,
            },
            secondaryArticles: articles.map((article) => {
              return {
                //publishDate property wants a string, but the input could be falsely typed as Date, in some case it is still string.
                publishDate: new Date(article.publishDate as unknown as string) as unknown as string,
                slug: article.slug,
                title: article.title,
                columnSlug: article.columnSlug,
                lead: article.lead,
                thumbnail: article.thumbnail
                  ? {
                      url: article.thumbnail,
                    }
                  : undefined,
                thumbnailFocusedImages: article?.thumbnailFocusedImages,
              };
            }),
          };
        })
      );
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    const slug = changes['excludedArticleSlug'];
    if (!slug.currentValue.isFirstChange && slug.currentValue !== slug.previousValue) {
      this.excludedSlug$?.next(this.excludedArticleSlug);
    }
  }

  excludeArticleBySlug(articles: DossierArticle[], slug: string): DossierArticle[] {
    return articles.filter((article) => article.slug !== slug);
  }
}
