@use 'shared' as *;

:host {
  border: 5px solid var(--kui-orange-600);
  padding: 20px;

  ::ng-deep man-article-card:last-child {
    margin-bottom: 0 !important;
    border-bottom: none !important;
    padding-bottom: 10px !important;
  }
}

.block-title {
  font-size: 12px;
  text-transform: uppercase;
  margin-bottom: 20px;

  .dossier-name {
    color: var(--kui-orange-600);
    font-weight: 700;
  }
}
