import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ManBlockTitleRowComponent } from '../block-title-row/man-block-title-row.component';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-block-title-sidebar',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/block-title-row/block-title-row.component.html',
  styleUrls: ['./man-block-title-sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink],
})
export class ManBlockTitleSidebarComponent extends ManBlockTitleRowComponent {}
