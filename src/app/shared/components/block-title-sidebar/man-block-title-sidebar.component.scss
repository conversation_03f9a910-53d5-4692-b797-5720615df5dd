@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .heading-line {
    &-link {
      padding: 0;
      margin: 0;
      display: flex;
    }

    &-title {
      display: block;
      color: var(--kui-orange-600);
      background: transparent;
      padding: 15px 0;
      font-family: var(--kui-font-primary);
      text-transform: uppercase;
      font-weight: 700;
      font-size: 24px;
      line-height: 24px;
      flex: 1;

      &:after {
        font-weight: bold;
        content: '›';
        color: var(--kui-black);
        margin-left: 10px;
      }
    }
  }
}
