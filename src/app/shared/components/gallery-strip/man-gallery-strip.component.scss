@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .gallery-article-box {
    &-header {
      display: flex;
      justify-content: space-between;
      background-color: var(--kui-black);
      cursor: pointer;
      width: 100%;
      height: 50.48px;
      margin-bottom: 10px;
      padding: 10px 20px;

      &-left {
        display: flex;
        column-gap: 10px;
        align-items: center;
      }
      &-right {
        display: flex;
        column-gap: 10px;
        align-items: center;
      }
      &-title {
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
        color: var(--kui-white);
        text-transform: uppercase;
        font-family: var(--kui-font-primary);

        &-link {
          color: var(--kui-white);
        }
      }
      &-link {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: var(--kui-white);

        @include media-breakpoint-down(lg) {
          display: none;
        }
      }
    }
  }

  .icon {
    width: 7.41px;
    height: 12px;
  }
}
