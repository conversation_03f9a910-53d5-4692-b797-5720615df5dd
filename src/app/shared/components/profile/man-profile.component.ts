import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-profile',
  templateUrl: './man-profile.component.html',
  styleUrls: ['./man-profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ManSimpleButtonComponent],
})
export class ManProfileComponent {}
