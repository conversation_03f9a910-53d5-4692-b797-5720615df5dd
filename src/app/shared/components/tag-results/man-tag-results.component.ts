import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgFor } from '@angular/common';
import { ManSearchFilterComponent } from '../search-filter/man-search-filter.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-tag-results',
  templateUrl: './man-tag-results.component.html',
  styleUrls: ['./man-tag-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ManSearchFilterComponent, NgFor, ManArticleCardComponent],
})
export class ManTagResultsComponent extends BaseComponent<ArticleCard[]> {
  @Input() articleTitle?: string;
  @Input() socialInteractions?: LikeButtons;

  public articleCardType = ArticleCardType;
}
