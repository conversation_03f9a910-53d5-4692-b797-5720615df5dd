@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
}

.tag-results {
  &-breadcrumb {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    margin-bottom: 20px;
    &-title {
      color: var(--kui-gray-500);
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
    }
    &-link {
      color: var(--kui-gray-400);
    }
    &-divider {
      height: 8px;
      width: 8px;
      border-radius: 50%;
      background: var(--kui-orange-600);
      margin-top: 2px;
    }
  }
  &-title {
    color: var(--kui-orange-600);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--kui-gray-100);
  }
  &-counter {
    font-size: 14px;
    line-height: 19px;
    margin-bottom: 10px;
  }
  &-meta {
    margin-bottom: 50px;
  }
  &-filter {
    display: flex;
    justify-content: space-between;
    max-width: 320px;
    width: 100%;
    &-select {
      background: none;
      border: none;
      font-weight: bold;
      margin-left: -3px;
    }
    &-right {
      display: flex;
      font-size: 14px;
      font-weight: 600;
      &-txt {
        margin-right: 5px;
      }
    }
  }
}
man-filter-bar {
  width: 320px !important;
  #filter-order {
    padding: 0;
  }
}
