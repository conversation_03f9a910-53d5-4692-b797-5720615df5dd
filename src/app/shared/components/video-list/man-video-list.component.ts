import { Component, Input, ChangeDetectionStrategy, Output, EventEmitter } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { SocialInteractionEvent } from '../social-share-modal/man-social-share-modal.definitions';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgFor } from '@angular/common';
import { ManBlockTitleRowComponent } from '../block-title-row/man-block-title-row.component';

@Component({
  selector: 'mandiner-man-video-list',
  templateUrl: './man-video-list.component.html',
  styleUrls: ['./man-video-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBlockTitleRowComponent, NgFor, ManArticleCardComponent],
})
export class ManVideoListComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;

  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  @Input() blockTitle: BlockTitle = {
    text: 'Videók',
    url: 'video',
    urlName: 'Még több videó',
    visibleIcon: true,
  };
}
