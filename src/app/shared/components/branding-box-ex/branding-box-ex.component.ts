import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { BrandingBoxArticle, BrandingBoxBrand, BrandingBoxExComponent } from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { BrandingBoxService } from 'src/app/feature/layout/services/branding-box.service';

@Component({
  selector: 'app-mandiner-branding-box-ex',
  imports: [AsyncPipe, BrandingBoxExComponent],
  templateUrl: './branding-box-ex.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MandinerBrandingBoxExComponent implements OnInit {
  @Input() set brand(brand: BrandingBoxBrand) {
    switch (brand) {
      case 'magyarnemzet':
        this._traffickingPlatform = 'MNO for Mandiner';
        break;
      case 'vilaggazdasag':
        this._traffickingPlatform = 'VG for Mandiner';
        break;
      default:
        this._traffickingPlatform = undefined;
        break;
    }
    this._brand = brand;
  }

  @Input() desktopWidth: number;

  data$: Observable<BrandingBoxArticle[] | undefined>;

  brandingBoxService = inject(BrandingBoxService);

  private _traffickingPlatform: string | undefined;
  private _brand: BrandingBoxBrand | undefined;

  get traffickingPlatforms(): string | undefined {
    return this._traffickingPlatform;
  }

  get brand(): BrandingBoxBrand | undefined {
    return this._brand;
  }

  ngOnInit(): void {
    this.data$ = this.getBrandingBoxData$();
  }

  getBrandingBoxData$(): Observable<BrandingBoxArticle[] | undefined> {
    if (this.traffickingPlatforms) {
      return this.brandingBoxService.getTrafficDeflectorData(this.traffickingPlatforms, 7);
    }

    switch (this.brand?.toString()) {
      case 'magyarkronikak':
        return this.brandingBoxService.fetchExternalFeedData('https://kronika.hu/kronika_feed.xml');
      case 'hungarianconservative':
        return this.brandingBoxService.fetchExternalFeedData('https://www.hungarianconservative.com/hucon_feed.xml');
      case 'makronom':
        return this.brandingBoxService.fetchExternalFeedData('https://makronom.eu/rss.php');
      case 'hirado':
        return this.brandingBoxService.fetchExternalFeedData('https://hirado.hu/mobil/export/hirado/mandiner.xml');
      default:
        return of(undefined);
    }
  }
}
