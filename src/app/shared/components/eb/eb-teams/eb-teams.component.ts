import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { EbService } from '../../../services';
import { CompetitionSummary } from './eb-teams.definitions';
import { EBPortalEnum, EbTeamsComponent, EBTeamsTable } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { SportResultService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-teams',
  templateUrl: './eb-teams.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [AsyncPipe, EbTeamsComponent, NgIf],
})
export class ManEbTeamsComponent implements OnInit {
  @Input() desktopWidth = 12;

  data$?: Observable<EBTeamsTable[]>;
  EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService
  ) {}

  ngOnInit(): void {
    const slug = this.ebService.getSlug();
    this.data$ = slug ? this.sportResultService.getCompetitionSummary(slug).pipe(map((data) => this.mapBackendSummaryToSummary(data))) : of([]);
  }

  private mapBackendSummaryToSummary(summaries: CompetitionSummary[]): EBTeamsTable[] {
    return summaries.map((summary) => ({
      groupName: summary?.phase?.name,
      teams: summary?.table,
      results: summary?.schedules,
    }));
  }
}
