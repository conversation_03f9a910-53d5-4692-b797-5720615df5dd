import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { backendDateToDate } from '@trendency/kesma-core';
import { ChampionshipSchedule, EbDailyProgramComponent, EBPortalEnum } from '@trendency/kesma-ui';
import { Observable, map, of } from 'rxjs';
import { EbService } from 'src/app/shared/services/eb.service';
import { SportResultService } from 'src/app/shared/services/sport-result.service';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-daily-program',
  templateUrl: './eb-daily-program.component.html',
  styleUrls: ['./eb-daily-program.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [AsyncPipe, EbDailyProgramComponent, NgIf],
})
export class ManEbDailyProgramComponent implements OnInit {
  readonly EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly ebService: EbService,
    private readonly sportResultService: SportResultService
  ) {}

  dailyProgram$: Observable<ChampionshipSchedule[]>;

  ngOnInit(): void {
    const slug = this.ebService.getSlug();
    this.dailyProgram$ = slug
      ? (this.sportResultService.getScheduleByCompetition(slug).pipe(
          map(({ data: { schedules } }) =>
            schedules?.map((schedule: ChampionshipSchedule) => ({
              ...schedule,
              scheduleDate: {
                ...schedule?.scheduleDate,
                date: backendDateToDate(schedule?.scheduleDate?.date.toString()),
              },
            }))
          )
        ) as Observable<ChampionshipSchedule[]>)
      : of([] as ChampionshipSchedule[]);
  }
}
