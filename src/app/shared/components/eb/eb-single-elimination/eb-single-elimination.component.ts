import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { backendDateToDate, EBPortalEnum, EbSingleEliminationComponent, Elimination, SingleElimination } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { SportResultService } from '../../../services';
import { EbService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-single-elimination',
  templateUrl: './eb-single-elimination.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [AsyncPipe, NgIf, EbSingleEliminationComponent],
})
export class ManEbSingleEliminationComponent implements OnInit {
  data$?: Observable<SingleElimination>;
  readonly EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService
  ) {}

  ngOnInit(): void {
    const slug = this.ebService.getSlug(); /* || 'europa-bajnoksag-1'*/
    this.data$ = slug
      ? this.sportResultService.getSchedulesGroupedByRound(slug).pipe(map((data) => this.mapBackendSingleEliminationToSingleElimination(data)))
      : of({} as SingleElimination);
  }

  private mapBackendSingleEliminationToSingleElimination(singleElimination: SingleElimination): SingleElimination {
    Object.entries(singleElimination).forEach(([key, value]) => {
      (singleElimination as any)[key] = value.map((elimination: Elimination) => ({
        ...elimination,
        scheduleDate: {
          ...elimination.scheduleDate,
          date: backendDateToDate(elimination.scheduleDate.date.toString()),
        },
      }));
    });

    return singleElimination;
  }
}
