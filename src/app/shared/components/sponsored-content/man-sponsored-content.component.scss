@use 'shared' as *;

:host {
  display: block;
  border: 2px solid var(--kui-orange-600);
  padding: 10px;
}

.sponsored-content {
  margin-bottom: 15px;

  &:last-child {
    .sponsored-content-divider {
      display: none;
    }
  }

  &-wrapper-title {
    background: var(--kui-gray-50);
    display: inline-block;
    padding: 5px;
    width: auto;
    font-family: var(--kui-font-primary);
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 20px;
  }

  &-image {
    min-width: 90px;
    min-height: 90px;
    object-fit: cover;
  }

  &-header-thumbnail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .sponsored-content-image {
      width: 90px;
      height: 90px;
      object-fit: cover;
    }
  }

  &-title {
    color: var(--kui-orange-600);
    font-family: var(--kui-font-secondary);
    font-size: 20px;
    font-weight: 600;
    line-height: 26px;

    &.extra-margin {
      margin-bottom: 10px;
    }
  }

  &-lead {
    font-family: var(--kui-font-primary);
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
  }

  &-divider {
    margin: 15px 0;
    width: 100%;
    height: 1px;
    display: block;
    background: var(--kui-gray-50);
  }
}
