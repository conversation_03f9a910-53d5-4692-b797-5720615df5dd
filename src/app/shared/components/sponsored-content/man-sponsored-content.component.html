<h4 class="sponsored-content-wrapper-title">{{ blockTitle }}</h4>
<div class="sponsored-content" *ngFor="let item of data; let index = index; trackBy: trackByFn">
  <a [routerLink]="articleUrls?.[index] ?? []">
    <div [class.sponsored-content-header-thumbnail]="item?.thumbnail?.url">
      <div class="sponsored-content-title" [class.extra-margin]="!item?.thumbnail?.url">
        {{ item?.title }}
      </div>
      <figure *ngIf="item?.thumbnail?.url" class="sponsored-content-figure">
        <img
          withFocusPoint
          [data]="item?.thumbnailFocusedImages"
          class="sponsored-content-image"
          [displayedUrl]="item?.thumbnail?.url"
          [alt]="item?.thumbnail?.alt"
          [displayedAspectRatio]="{ desktop: '1:1' }"
        />
      </figure>
    </div>
  </a>
  <div class="sponsored-content-lead">
    {{ item?.lead }}
  </div>
  <span class="sponsored-content-divider"></span>
</div>
