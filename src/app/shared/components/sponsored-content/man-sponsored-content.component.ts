import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, FocusPointDirective } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'man-sponsored-content',
  templateUrl: './man-sponsored-content.component.html',
  styleUrls: ['./man-sponsored-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, NgIf, FocusPointDirective],
})
export class ManSponsoredContentComponent extends BaseComponent<ArticleCard[]> implements OnInit {
  @Input() blockTitle: string = 'Szponzorált tartalom';

  articleUrls?: string[][];

  override ngOnInit(): void {
    super.ngOnInit();
    this.articleUrls = this.data?.map((article: ArticleCard) => buildArticleUrl(article));
  }
}
