import { Component, EventEmitter, OnInit, Output, ChangeDetectionStrategy } from '@angular/core';
import { ArticleCard, BackendArticleSearchResult, buildArticleUrl, convertDateToBackendUtcDate } from '@trendency/kesma-ui';
import { subHours } from 'date-fns';
import { Observable, map } from 'rxjs';
import { ApiService } from 'src/app/shared/services/api.service';
import { backendDateToDate } from '@trendency/kesma-core';
import { Router } from '@angular/router';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-exit-popup',
  templateUrl: './exit-popup.component.html',
  styleUrls: ['./exit-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf],
})
export class ExitPopupComponent implements OnInit {
  readonly ArticleCardType = ArticleCardType;

  articles$: Observable<ArticleCard[]>;

  @Output() closeEvent = new EventEmitter();

  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    const lastDay = subHours(new Date(), 24);
    const lastDayString = convertDateToBackendUtcDate(lastDay);
    const now = convertDateToBackendUtcDate(new Date());

    this.articles$ = this.apiService.searchByKeyword('', 'desc', lastDayString, now, ['article', 'articleVideo'], [], 0, 8).pipe(
      map((result) => {
        return result.data?.map((article: BackendArticleSearchResult) => ({
          ...article,
          hasGallery: Number(article?.hasGallery),
          isVideoType: Number(article?.isVideo),
          isPodcastType: Number(article?.isPodcastType),
          thumbnail: {
            url: article.thumbnail,
          },
          publishDate: backendDateToDate(article?.publishDate as string),
        })) as any as ArticleCard[];
      })
    );
  }

  navigateToArticle(article: ArticleCard): void {
    this.close();
    this.router.navigate(buildArticleUrl(article));
  }

  close(): void {
    this.closeEvent.emit();
  }
}
