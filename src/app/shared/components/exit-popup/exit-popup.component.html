<ng-container *ngIf="articles$ | async as articles">
  <div *ngIf="articles.length > 0" class="exit-popup">
    <div (click)="close()" class="exit-popup-backdrop"></div>
    <div class="exit-popup-wrapper">
      <button (click)="close()" class="btn-close">
        <i class="icon icon-close"></i>
      </button>
      <h1 class="exit-popup-header">Ezeket már olvasta? Maradjon velünk!</h1>
      <div class="exit-popup-content">
        <ng-container *ngFor="let article of articles; let i = index">
          <div (click)="navigateToArticle(article)" class="article-card">
            <img
              [alt]="article?.thumbnail?.alt || ''"
              [src]="article?.thumbnail?.url || '/assets/images/placeholder-16-9.svg'"
              class="article-card-img"
              loading="lazy"
            />
            <h3 class="article-card-title">
              {{ article?.title }}
            </h3>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</ng-container>
