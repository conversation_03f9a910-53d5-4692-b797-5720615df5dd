<ul>
  <li><a routerLink="/">Főoldal</a></li>
  <li class="breadcrumb-item" *ngFor="let item of items">
    <ng-container [ngTemplateOutlet]="separator"> </ng-container>
    <a *ngIf="item.url; else current" [routerLink]="item.url">{{ item.label }}</a>
    <ng-template #current>
      <span>{{ item.label }}</span>
    </ng-template>
  </li>
</ul>

<ng-template #separator>
  <span class="separator">&#x2B24;</span>
</ng-template>
