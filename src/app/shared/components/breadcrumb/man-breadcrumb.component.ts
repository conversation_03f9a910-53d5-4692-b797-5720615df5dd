import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BreadcrumbComponent } from '@trendency/kesma-ui';
import { <PERSON><PERSON><PERSON>, NgTemplateOutlet, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-breadcrumb',
  templateUrl: './man-breadcrumb.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/breadcrumb/breadcrumb.component.scss', './man-breadcrumb.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgFor, NgTemplateOutlet, NgIf],
})
export class ManBreadcrumbComponent extends BreadcrumbComponent {}
