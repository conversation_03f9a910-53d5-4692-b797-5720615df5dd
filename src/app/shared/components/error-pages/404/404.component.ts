import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../constants';
import type { Response } from 'express';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class Error404Component implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}
  ngOnInit(): void {
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: `Az oldal nem található - ${defaultMetaInfo.ogTitle}`,
      ogTitle: `Az oldal nem található - ${defaultMetaInfo.ogTitle}`,
      robots: 'noindex',
    });
    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
