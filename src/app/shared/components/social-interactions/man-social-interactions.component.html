<div [ngClass]="isMaxWidth ? 'container-max-width' : isPositionRight ? 'container-right' : 'container'">
  <div class="social-left">
    <!-- Reading Time -->
    <div class="social-item" [ngClass]="isShowReadingTime && data?.readingTime ? '' : 'dont-show'">
      <img class="social-icon icon-mandiner-reading-time" src="/assets/images/icons/icon-new-mandiner-reading-time.svg" alt="Olvasási idő" />
      <div class="social-counter">
        {{ data?.readingTime }}
        <span class="social-text">p</span>
      </div>
    </div>
    <span class="social-divider" *ngIf="isShowReadingTime && showDividers && !data?.areReactionsHidden"></span>
    <!-- Like -->
    <div class="social-item" (click)="handleReaction(true)" *ngIf="!data?.areReactionsHidden">
      <img class="social-icon like" [class.disabled]="!isReactionEnabled" src="/assets/images/icons/new-thumbs-up.svg" alt="Like" />
      <div class="social-counter">
        {{ data?.like }}
      </div>
    </div>
    <span class="social-divider" *ngIf="showDividers && !data?.areReactionsHidden"></span>
    <!-- Dislike -->
    <div class="social-item" (click)="handleReaction(false)" *ngIf="!data?.areReactionsHidden">
      <img class="social-icon dislike" [class.disabled]="!isReactionEnabled" src="/assets/images/icons/new-thumbs-down.svg" alt="Dislike" />
      <div class="social-counter">
        {{ data?.dislike }}
      </div>
    </div>
    <span class="social-divider" *ngIf="showDividers && !data?.areCommentsHidden"></span>
    <!-- Comment -->
    <div class="social-item" (click)="handleComment()" *ngIf="!data?.areCommentsHidden">
      <img class="social-icon comments" [class.disabled]="!isReactionEnabled" src="/assets/images/icons/new-comments.svg" alt="Komment" />
      <div class="social-counter">
        {{ data?.comment }}
      </div>
    </div>
    <span class="social-divider" [ngClass]="isPositionRight ? '' : 'dont-show'"></span>
  </div>
  <div class="social-right">
    <!-- Bookmark -->
    <div *ngIf="isShowBookmark && !isBookmarked" class="social-item" (click)="onBookmarkClicked('save')">
      <img class="social-icon bookmark" src="/assets/images/icons/new-bookmark.svg" alt="Könyvjelző" />
      <span [ngClass]="isShowReadingTime ? 'social-text' : 'dont-show'">Mentés</span>
    </div>
    <div *ngIf="isShowBookmark && isBookmarked" class="social-item" (click)="onBookmarkClicked('delete')">
      <img class="social-icon bookmark" src="/assets/images/icons/new.bookmark2.svg" alt="Könyvjelző" />
      <span [ngClass]="isShowReadingTime ? 'social-text' : 'dont-show'">Törlés</span>
    </div>
    <span *ngIf="isShowBookmark" class="social-divider"></span>
    <!-- Share -->
    <div class="social-item">
      <mandiner-social-share-modal
        (urlCopied)="urlCopied.emit($event)"
        (socialInteraction)="socialInteraction.emit($event)"
        [title]="articleTitle || ''"
        [link]="articleLink || []"
        [isShowText]="isShowText"
        [hasExternalUrl]="hasExternalUrl"
      >
      </mandiner-social-share-modal>
    </div>
  </div>
</div>
