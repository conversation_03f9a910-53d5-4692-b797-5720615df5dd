@use 'shared' as *;

:host {
  width: 100%;
  border-bottom: 1px solid var(--kui-gray-100);
  padding-bottom: 15px;
  position: relative;
  flex-direction: row;
}

.container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 20px;
}

.container-right {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  height: 20px;
}

.container-max-width {
  max-width: 370px;
  display: flex;
  justify-content: space-between;
  height: 20px;
}

.icon {
  fill: black;
  color: black;
}

.social {
  &-icon {
    fill: var(--kui-orange-600);
    margin-right: 5px;
    cursor: pointer;
    transition-duration: 0.3s;
    width: 20px;
    height: 16px;
    object-fit: contain;

    &.disabled {
      cursor: default;
    }

    &:hover.like:not(.disabled) {
      content: url('/assets/images/icons/thumbs-up-dark.svg');
    }
    &:hover.dislike:not(.disabled) {
      content: url('/assets/images/icons/thumbs-down-dark.svg');
    }
    &:hover.comments:not(.disabled) {
      content: url('/assets/images/icons/comments-dark.svg');
    }
    &:hover.bookmark:not(.disabled) {
      content: url('/assets/images/icons/bookmark-dark.svg');
    }
  }

  &-left,
  &-right,
  &-item {
    display: flex;
    align-items: center;
  }

  &-item {
    margin: 0 5px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  &-counter {
    color: var(--kui-gray-600);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }

  &-divider {
    background: var(--kui-gray-100);
    width: 1px;
    height: 100%;
    margin: 0 5px;
  }

  &-text {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: var(--kui-gray-700);
    cursor: pointer;
  }
}

.dont-show {
  display: none;
}
