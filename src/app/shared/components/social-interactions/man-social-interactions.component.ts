import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { LikeButtons } from './man-social-interactions.definitions';
import { SocialInteractionEvent, SocialInteractionEventType } from '../social-share-modal/man-social-share-modal.definitions';
import { RoutingHelperService } from '@trendency/kesma-ui';
import { ManSocialShareModalComponent } from '../social-share-modal/man-social-share-modal.component';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'mandiner-social-interactions',
  templateUrl: './man-social-interactions.component.html',
  styleUrls: ['./man-social-interactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, NgIf, ManSocialShareModalComponent],
})
export class ManSocialInteractionsComponent {
  @ViewChild('shareOpportunities') shareOpportunities?: ElementRef;

  @Input() data?: LikeButtons;
  @Input() articleLink?: string[];
  @Input() articleTitle?: string;
  @Input() isMaxWidth = false;
  @Input() isShowReadingTime = false;
  @Input() isShowText = false;
  @Input() isShowBookmark = false;
  @Input() isPositionRight = false;
  @Input() isReactionEnabled = false;
  @Input() showDividers = false;
  @Input() isBookmarked = false;
  @Input() hasExternalUrl = false;
  @Output() urlCopied = new EventEmitter<string>();
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();
  @Output() bookmarkClicked = new EventEmitter<string>();
  @Output() reactionClicked = new EventEmitter<boolean>();
  @Output() commentClicked = new EventEmitter<void>();

  constructor(private readonly routingHelper: RoutingHelperService) {}

  onBookmarkClicked(method: string): void {
    this.bookmarkClicked.emit(method);
  }

  handleReaction(liked: boolean): void {
    if (!this.isReactionEnabled) {
      return;
    }
    this.reactionClicked.emit(liked);
    this.socialInteraction.emit(
      new SocialInteractionEvent(
        SocialInteractionEventType.Reaction,
        this.routingHelper.resolveLink(this.articleLink ?? '', this.hasExternalUrl),
        this.articleTitle,
        liked ? 'Like' : 'Dislike'
      )
    );
  }

  handleComment(): void {
    this.commentClicked.emit();
    this.socialInteraction.emit(
      new SocialInteractionEvent(
        SocialInteractionEventType.Comment,
        this.routingHelper.resolveLink(this.articleLink ?? '', this.hasExternalUrl),
        this.articleTitle,
        'Komment'
      )
    );
  }
}
