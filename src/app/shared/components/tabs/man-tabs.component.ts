import { ChangeDetectionStrategy, Component, ContentChildren, QueryList } from '@angular/core';
import { TabsComponent } from '@trendency/kesma-ui';
import { ManTabComponent } from './man-tab/man-tab.component';
import { Ng<PERSON><PERSON>, NgForOf } from '@angular/common';

@Component({
  selector: 'man-tabs',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/tabs/tabs.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/tabs/tabs.component.scss', './man-tabs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgClass],
})
export class ManTabsComponent extends TabsComponent {
  @ContentChildren(ManTabComponent) override tabs?: QueryList<ManTabComponent>;
}
