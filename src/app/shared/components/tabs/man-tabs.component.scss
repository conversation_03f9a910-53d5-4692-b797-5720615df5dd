@use 'shared' as *;

:host {
  --tabs-backgorund-color: var(--kui-gray-60);

  --tabs-link-hover-color: var(--tabs-backgorund-color);
  --tabs-hover-text-color: var(--kui-orange-600);

  --tabs-link-active-color: var(--tabs-backgorund-color);
  --tabs-active-text-color: var(--kui-orange-600);

  --tabs-link-disabled-color: var(--tabs-backgorund-color);
  --tabs-disabled-text-color: var(--kui-gray-300);

  ul {
    li {
      position: relative;
      padding: 8px 36px;

      a {
        text-transform: uppercase;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }

      &:hover:not(.disabled):not(.active) {
        a {
          color: var(--tabs-hover-text-color);
        }
      }

      &.active {
        a {
          color: var(--tabs-active-text-color);
          font-weight: 700;
          font-size: 14px;
          line-height: 22px;
        }

        &:before {
          content: ' ';
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          height: 3px;
          width: 50%;
          border-top: 3px solid var(--tabs-active-text-color);
        }
      }
    }
  }
}
