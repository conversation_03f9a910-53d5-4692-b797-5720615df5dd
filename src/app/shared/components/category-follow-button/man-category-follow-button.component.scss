@use 'shared' as *;

.category-follow-button {
  border: 1px solid var(--kui-orange-600);
  border-radius: 3px;
  display: flex;
  padding: 6px 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  align-items: center;
  justify-content: center;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  color: var(--kui-black);

  &:not(.followed):hover {
    background-color: var(--kui-orange-600);
    color: var(--kui-white);
  }

  &.followed {
    border: 1px solid var(--kui-black);

    &:hover {
      border-color: var(--kui-orange-600);
    }
  }

  .icon {
    display: none;
    height: 10px;
    width: 14px;
    margin-right: 5px;
  }

  &.followed .icon {
    display: block;
  }
}
