import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FollowStatus } from '@trendency/kesma-ui';
import { NgClass } from '@angular/common';

@Component({
  selector: 'man-category-follow-button',
  templateUrl: './man-category-follow-button.component.html',
  styleUrls: ['./man-category-follow-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass],
})
export class ManCategoryFollowButtonComponent {
  @Input() isFollowed?: FollowStatus | null = FollowStatus.LOGGED_OUT_NOT_FOLLOWED;
  @Input() isLoading: boolean = false;
  @Output() actionEvent: EventEmitter<void> = new EventEmitter<void>();

  FollowStatus = FollowStatus;
}
