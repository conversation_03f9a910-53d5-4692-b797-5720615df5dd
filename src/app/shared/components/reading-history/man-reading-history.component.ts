import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { LikeButtons } from '../../definitions';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { ReadingHistory } from './man-reading-history.definitions';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgFor } from '@angular/common';
import { ManSearchFilterComponent } from '../search-filter/man-search-filter.component';
import { ManArticleTitleComponent } from '../article-title/man-article-title.component';

@Component({
  selector: 'man-reading-history',
  templateUrl: './man-reading-history.component.html',
  styleUrls: ['./man-reading-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManArticleTitleComponent, ManSearchFilterComponent, NgFor, ManArticleCardComponent, ManSimpleButtonComponent],
})
export class ManReadingHistoryComponent {
  public articleCardType = ArticleCardType;

  @Input() socialInteractions?: LikeButtons;
  @Input() articleTitle?: string;
  @Input() buttonText?: string;
  @Input() data?: ReadingHistory[];

  @Output() loginButtonClicked = new EventEmitter<void>();

  private openGroups: number[] = [];

  public isGroupOpen(index: number): boolean {
    return this.openGroups.includes(index);
  }

  public onGroupClick(index: number): void {
    if (this.isGroupOpen(index)) {
      this.openGroups = this.openGroups.filter((group) => group !== index);
    } else {
      this.openGroups.push(index);
    }
  }
}
