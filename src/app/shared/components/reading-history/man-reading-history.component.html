<man-article-title [articleTitle]="articleTitle"></man-article-title>
<div class="history-content">
  <man-search-filter [showSearchBar]="false" [showSearchHeader]="false" buttonText="Szűrési beállítások mentése"></man-search-filter>
  <div class="history-article" (click)="onGroupClick(i)" *ngFor="let item of data; let i = index">
    <div class="history-article-header">
      <div class="history-article-date">
        {{ item.readDate }}
        <span class="history-article-date-span">({{ item.readDay }})</span>
      </div>
      <img class="history-article-chevron" [class.open]="isGroupOpen(i)" src="../../../../assets/images/icons/icon-mandiner-chevron-down.svg" alt="nyíl" />
    </div>
    <ul class="history-article-list" [class.open]="isGroupOpen(i)" *ngFor="let item of item.readTitle">
      <ng-container *ngFor="let item of data">
        <man-article-card
          *ngFor="let card of item.readTitle"
          [styleID]="articleCardType.ImgRightTitleLeadDateMeta"
          [data]="card"
          [socialInteractions]="socialInteractions"
        >
        </man-article-card>
      </ng-container>
    </ul>
  </div>
  <man-simple-button (click)="loginButtonClicked.emit()">
    {{ buttonText }}
  </man-simple-button>
</div>
