@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  max-width: 1200px;
  width: 100%;
}

.history {
  &-content {
    max-width: 1200px;
    width: 100%;
    margin: auto;
  }

  &-article {
    &-header {
      padding-bottom: 10px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid var(--kui-gray-200);
    }

    &-date {
      font-weight: 700;
      font-size: 18px;
      line-height: 22px;

      &-span {
        font-weight: 400;
      }
    }

    &-list {
      display: none;

      &.open {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 100%;
        margin-bottom: 20px;
      }

      &-item {
        &-link {
          color: var(--kui-orange-600);
          text-decoration: underline;
          font-weight: 400;
          font-size: 16px;
          line-height: 22px;
        }
      }
    }
  }
}
man-simple-button {
  max-width: 320px;
}
