@use 'shared' as *;

:host {
  display: block;

  &.default {
    .poll {
      background: var(--kui-orange-600);
      padding: 30px 20px;

      &-header {
        &-icon {
          display: block;
          margin: 0 auto 15px;
        }

        &-title {
          font-family: var(--kui-font-primary);
          color: var(--kui-white);
          font-weight: 700;
          font-size: 24px;
          line-height: 24px;
          text-align: center;
          margin-bottom: 15px;
        }

        &-question {
          font-family: var(--kui-font-secondary);
          color: var(--kui-black);
          font-weight: 700;
          font-size: 24px;
          line-height: 30px;
          margin-bottom: 15px;
        }
      }

      &-form {
        &-wrapper {
          margin-bottom: 20px;
        }

        &-radio {
          @extend %poll-form-radio;
          padding: 20px 20px 20px 54px;
          background: rgba(white, 0.6);

          &-option {
            display: flex;
            align-items: center;
          }

          &-label {
            cursor: pointer;
            display: inline-block;
            font-family: var(--kui-font-primary);
            font-size: 16px;
            font-weight: 400;
          }

          &:hover {
            color: var(--kui-black);
            cursor: pointer;
            background: rgba(white, 0.8);
          }

          &-input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
          }

          &.active {
            background: rgba(white, 1);

            & > li > label {
              font-weight: 600;
            }
          }

          &-progress-bar {
            height: 3px;
            background-color: var(--kui-orange-600);
            margin-top: 5px;
          }

          &.results {
            padding-left: 20px;
            background: rgba(white, 1);
          }
        }

        &-result {
          @extend %poll-form-result;
        }

        &-checkmark {
          @extend %poll-form-checkmark;
          left: 15px;
        }

        &-radio:hover input ~ .poll-form-checkmark {
          background-color: var(--kui-white);
        }

        &-radio input:checked ~ .poll-form-checkmark:after {
          display: block;
        }
        &-radio input:checked ~ .poll-form-radio {
          background: var(--kui-orange-600) !important;
        }

        &-radio .poll-form-checkmark:after {
          top: 3px;
          left: 3px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--kui-orange-600);
        }

        &-buttons {
          margin-top: 10px;

          &-submit {
            @extend %submit-btn;
            width: 100%;
            text-transform: uppercase;

            &:hover {
              background: var(--kui-orange-600);
              color: var(--kui-white);
              border: 1px solid var(--kui-white);
            }

            &.disabled {
              background: var(--kui-orange-600);
              border: 1px solid var(--kui-white);
              cursor: not-allowed;
            }

            &.voted {
              background: var(--kui-gray-500);
              cursor: not-allowed;
              color: var(--kui-gray-450);
            }
          }
        }
      }
    }
  }

  &.gray {
    width: 100%;

    .poll {
      background: var(--kui-gray-300);
      padding: 20px;

      &-header {
        .icon {
          height: 24px;
          width: 15px;
          vertical-align: middle;
          margin-left: 10px;
        }

        &-question {
          font-family: var(--kui-font-primary);
          color: var(--kui-black);
          font-weight: 700;
          font-size: 16px;
          line-height: 22px;
          border-bottom: 1px solid var(--kui-gray-100);
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
      }

      &-form {
        &-wrapper {
          margin-bottom: 30px;
        }

        &-radio {
          @extend %poll-form-radio;

          &-option {
            display: flex;
            align-items: center;
            padding-left: 15px;
          }

          &-label {
            cursor: pointer;
            display: inline-block;
            font-family: var(--kui-font-primary);
            font-size: 14px;
            font-weight: 400;
            margin-left: 25px;
            line-height: 19px;
          }

          &:hover {
            color: var(--kui-black);
            cursor: pointer;
          }

          &-input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
          }

          &.active {
            & > li > label {
              font-weight: 600;
            }
          }

          &-progress-bar {
            height: 5px;
            background-color: var(--kui-orange-600);
            margin-top: 15px;
            display: inline-block;
            opacity: 0.4;

            &.highest {
              opacity: 1;
            }
          }

          &-vote-count {
            vertical-align: middle;
            padding-left: 10px;
            color: var(--kui-gray-700);
          }

          &.results {
            padding-left: 20px;
            margin-bottom: 25px;

            .poll-form-radio-option {
              padding: 0;

              .poll-form-radio-label {
                margin: 0;
              }
            }
          }
        }

        &-result {
          @extend %poll-form-result;
        }

        &-checkmark {
          @extend %poll-form-checkmark;
          left: 0;
        }

        &-radio:hover input ~ .poll-form-checkmark {
          background-color: var(--kui-white);
        }

        &-radio input:checked ~ .poll-form-checkmark:after {
          display: block;
        }

        &-radio input:checked ~ .poll-form-radio {
          background: var(--kui-orange-600) !important;
        }

        &-radio .poll-form-checkmark:after {
          top: 2px;
          left: 2px;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: var(--kui-orange-600);
        }

        &-buttons {
          margin-top: 10px;

          &-submit {
            @extend %submit-btn;
            font-weight: 700;
            font-size: 14px;

            &:hover {
              background: var(--kui-orange-600);
              color: var(--kui-white);
              border: 1px solid var(--kui-white);
            }

            &.disabled {
              background: var(--kui-gray-320);
              border: 1px solid var(--kui-gray-320);
              cursor: default;
            }
          }
        }
      }
    }
  }
}

%submit-btn {
  font-family: var(--kui-font-primary);
  background: var(--kui-black);
  border: 1px solid var(--kui-black);
  color: var(--kui-white);
  cursor: pointer;
  margin-right: 20px;
  padding: 10px 20px;
  transition: all 0.3s ease-in-out;
}

%poll-form-radio {
  position: relative;
  font-size: 14px;
  line-height: 24px;
  cursor: pointer;
  user-select: none;
  margin-bottom: 15px;
}

%poll-form-result {
  display: inline-block;
  font-family: var(--kui-font-secondary);
  font-weight: 700;
  font-size: 30px;
  margin-right: 10px;
  line-height: 30px;
}

%poll-form-checkmark {
  position: absolute;
  top: calc(50% - 12px);
  height: 24px;
  width: 24px;
  background-color: var(--kui-white);
  border: 1px solid var(--kui-gray-250);
  border-radius: 50%;
  cursor: pointer;

  &:after {
    content: '';
    position: absolute;
    display: none;
    cursor: pointer;
  }
}
