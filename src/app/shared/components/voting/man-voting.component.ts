import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ViewModel, VoteData, VotingComponent } from '@trendency/kesma-ui';
import { MandinerVotingStyle } from './man-voting.definition';
import { FormsModule } from '@angular/forms';
import { NgIf, NgFor, NgClass, AsyncPipe } from '@angular/common';

@Component({
  selector: 'man-voting',
  templateUrl: './man-voting.component.html',
  styleUrls: ['./man-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, NgFor, NgClass, AsyncPipe],
})
export class ManVotingComponent extends VotingComponent {
  override readonly vm = new ViewModel({
    showResults: false,
    hasExpired: false,
    userVotedId: '' as string | undefined,
    voteData: {} as VoteData,
    buttonLabel: 'Szavazok',
    isSubmitButtonDisabled: true,
  });
  @Input() set styleID(styleID: MandinerVotingStyle) {
    this.hostClass = styleID;
  }

  @HostBinding('class') override hostClass = MandinerVotingStyle.DEFAULT;

  votingStyle = MandinerVotingStyle;

  override ngOnInit(): void {
    super.ngOnInit();

    this.vm.next({
      buttonLabel: this.isExpired ? 'Lejárt szavazás' : this.showResults ? 'Szavaztál' : 'Szavazok',
    });
  }

  override setVoteId(id: string): void {
    if (this.vm.state.showResults) {
      return;
    }

    this.vm.next({
      userVotedId: id,
      isSubmitButtonDisabled: false,
    });
  }

  override onVote(): void {
    super.onVote();
    this.vm.next({
      buttonLabel: 'Szavaztál',
      isSubmitButtonDisabled: true,
    });
  }

  isThisTheHighestVoteCount(voteCount: number | undefined): boolean {
    if (!voteCount) return false;

    const highestVoteCount = this.data?.answers?.reduce((acc, curr) => {
      return (acc.voteCount as number) > (curr.voteCount as number) ? acc : curr;
    }).voteCount;

    return highestVoteCount === voteCount;
  }
}
