import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, GalleryData } from '@trendency/kesma-ui';
import { ManGalleryListItemComponent } from '../gallery-list-item/man-gallery-list-item.component';
import { NgFor } from '@angular/common';
import { ManGalleryStripComponent } from '../gallery-strip/man-gallery-strip.component';

@Component({
  selector: 'man-gallery-list',
  templateUrl: './man-gallery-list.component.html',
  styleUrls: ['./man-gallery-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManGalleryStripComponent, NgFor, ManGalleryListItemComponent],
})
export class ManGalleryListComponent extends BaseComponent<GalleryData[]> {}
