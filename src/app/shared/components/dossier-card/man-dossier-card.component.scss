@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-primary);
  color: var(--kui-white);
}

.dossier-card-wrapper {
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  position: relative;

  .background-image {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
  }

  .dossier-card {
    max-width: 800px;
    padding: 50px;

    @include media-breakpoint-down(md) {
      padding: 50px 20px 50px 40px;
    }

    &-header {
      display: flex;
      justify-content: center;
      gap: 10px;
      align-items: center;

      .icon {
        width: 150px;
        height: 30px;
      }

      &-mark {
        font-size: 18px;
        font-weight: 700;
      }
    }

    &-title {
      font-size: 32px;
      font-weight: 700;
      font-family: var(--kui-font-secondary);
      text-align: center;
      margin: 10px 0 20px;
      line-height: 38px;
      color: var(--kui-white);
    }

    &-articles {
      list-style-type: decimal;
      text-decoration: underline;
      line-height: 32px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .article {
        line-height: 24px;

        .article-link {
          color: var(--kui-white);
        }
      }
    }

    &-button {
      background: var(--kui-orange-600);
      padding: 12px 16px;
      color: var(--kui-white);
      font-weight: 700;
      font-size: 14px;
      margin: 30px auto 0;
      display: block;
    }
  }
}
