<div class="dossier-card-wrapper">
  <div [style.background-image]="'url(' + data?.thumbnail + ')'" class="background-image"></div>

  <div class="dossier-card">
    <div class="dossier-card-header">
      <i class="icon icon-logo-gallery-box"></i>
      <span class="dossier-card-header-mark">AKTÁK</span>
    </div>

    <h1 class="dossier-card-title">{{ data?.title }}</h1>

    <ol class="dossier-card-articles">
      <li class="article" *ngFor="let article of data?.articles | slice: 0 : 10">
        <a [routerLink]="articleLink(article)" class="article-link">{{ article?.title }}</a>
      </li>
    </ol>

    <button type="button" class="dossier-card-button" [routerLink]="['/dosszie', data?.slug]" *ngIf="hasButton">Az összes cikk</button>
  </div>
</div>
