import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, DossierArticle } from '@trendency/kesma-ui';
import { DossierCard } from './man-dossier-card.definitions';
import { format } from 'date-fns';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'man-dossier-card',
  templateUrl: './man-dossier-card.component.html',
  styleUrls: ['./man-dossier-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, NgIf, SlicePipe],
})
export class ManDossierCardComponent extends BaseComponent<DossierCard> {
  @Input() hasButton = true;

  articleLink(article: Partial<DossierArticle>): string[] {
    let publishYear = '';
    let publishMonth = '';
    try {
      publishYear = article?.publishDate ? format(article.publishDate, 'yyyy') : '';
      publishMonth = article?.publishDate ? format(article.publishDate, 'MM') : '';
    } catch (e) {
      console.error('Unable to use publishDate: ', article?.publishDate, e);
    }

    return article.publishDate ? (['/', article.columnSlug || article.slug, publishYear, publishMonth, article.slug] as string[]) : [];
  }
}
