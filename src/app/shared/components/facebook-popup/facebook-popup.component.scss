@use 'shared' as *;

.facebook-popup {
  padding: 10px;
  max-width: 360px;
  height: 180px;
  position: sticky;
  left: 2%;
  bottom: 0;
  background-color: white;
  z-index: 1;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
  display: none;

  @include media-breakpoint-down(sm) {
    left: 0;
  }

  &.visible {
    display: block;
    animation: 1.5s slide-up;
  }

  &-header {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;

    &-logo {
      width: 100px;
      height: 20px;
      margin-right: 10px;
    }

    &-title {
      font-family: var(--kui-font-primary);
      text-transform: uppercase;
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
    }

    &-close {
      width: 32px;
      height: 40px;
      cursor: pointer;
      padding: 14px 10px;
      position: absolute;
      top: -10px;
      right: -10px;
    }
  }

  .fb-page {
    width: 100%;
    max-width: 340px;
    height: 130px;
  }
}
