import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../../environments/environment';
import { addDays, isAfter } from 'date-fns';
import { DOCUMENT, NgClass } from '@angular/common';
import { FacebookEndpoint, LocalStorageKey } from '../../definitions';

const FBSDK_URL = FacebookEndpoint.Sdk;
const FB_POPUP_STORAGE_KEY = LocalStorageKey.FbPopup;

@Component({
  selector: 'app-facebook-popup',
  templateUrl: './facebook-popup.component.html',
  styleUrls: ['./facebook-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [NgClass],
})
export class FacebookPopupComponent implements OnInit {
  isVisible = false;
  fbMandinerEndpoint = FacebookEndpoint.Mandiner;

  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly cd: ChangeDetectorRef,
    private readonly renderer: Renderer2,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      const closedUntil = this.storageService.getLocalStorageData(FB_POPUP_STORAGE_KEY);
      if (!closedUntil) {
        this.isVisible = true;
      } else {
        if (isAfter(new Date(), new Date(closedUntil))) {
          this.storageService.removeLocalStorageData(FB_POPUP_STORAGE_KEY);
          this.isVisible = true;
        }
      }
      this.cd.detectChanges();

      const fbLoaded = this.document.querySelector(`script[src^='${FBSDK_URL}']`);
      // load facebook sdk if required
      if (!fbLoaded) {
        // initialise facebook sdk after it loads if required
        if (!(window as any)['fbAsyncInit']) {
          (window as any)['fbAsyncInit'] = this.initFb;
        }

        const script = this.renderer.createElement('script');
        script.type = 'text/javascript';
        script.src = FBSDK_URL;
        script.onload = (): void => {
          this.cd.detectChanges();
        };
        this.renderer.appendChild(this.document.body, script);
      }
      if (fbLoaded) {
        this.initFb();
        this.cd.detectChanges();
      }
    }
  }

  onClose(): void {
    const closedUntil = addDays(new Date(), 3);
    if (this.utilsService.isBrowser()) {
      this.storageService.setLocalStorageData(FB_POPUP_STORAGE_KEY, closedUntil.toString());
    }
    this.isVisible = false;
  }

  private initFb(): void {
    (window as any)['FB'].init({
      appId: environment.facebookAppId,
      autoLogAppEvents: true,
      xfbml: true,
      version: 'v16.0',
    });
  }
}
