import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, DossierData } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { FormatDatePipe } from '@trendency/kesma-core';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgIf, NgFor } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-news-feed',
  templateUrl: 'man-news-feed.component.html',
  styleUrls: ['man-news-feed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Router<PERSON>ink, NgIf, NgFor, ManArticleCardComponent, FormatDatePipe],
})
export class ManNewsFeedComponent extends BaseComponent<DossierData> {
  public dossierLink?: string[];

  readonly ArticleCardType = ArticleCardType;

  override ngOnInit(): void {
    this.dossierLink = ['/', 'hirfolyam', this.data?.slug as string];
  }
}
