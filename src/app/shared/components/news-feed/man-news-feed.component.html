<div class="news-feed-thumbnail-wrapper" [routerLink]="dossierLink">
  <img class="news-feed-thumbnail" [src]="data?.headerImage || 'assets/images/placeholder-16-9.svg'" alt="Hírfolyam" loading="lazy" />
  <i class="icon icon-news-feed"></i>
</div>
<a [routerLink]="dossierLink">
  <h2 class="news-feed-title">
    {{ data?.title }}
  </h2>
</a>
<div class="news-feed-lead">
  {{ data?.lead }}
</div>

<ng-container *ngIf="data?.secondaryArticles as articles">
  <div class="news-feed-divider"></div>

  <ng-container *ngFor="let article of articles; trackBy: trackByFn">
    <ng-container *ngIf="article">
      <span class="news-feed-date" *ngIf="article?.publishDate">{{ article.publishDate | formatDate: 'h-m' }}</span>
      <man-article-card [styleID]="ArticleCardType.TitleMeta" [data]="article"> </man-article-card>
    </ng-container>
  </ng-container>

  <div class="news-feed-divider"></div>
</ng-container>
