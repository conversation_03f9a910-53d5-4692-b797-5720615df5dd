@use 'shared' as *;

:host {
  display: block;

  ::ng-deep {
    man-article-card.article-card {
      &.style-TitleMeta {
        li::before {
          top: calc(50% - 5px) !important;
        }
      }
    }
  }

  .news-feed {
    &-thumbnail {
      object-fit: cover;
      aspect-ratio: 16 / 9;
      width: 100%;

      &-wrapper {
        position: relative;
        cursor: pointer;
      }
    }

    &-title {
      font-size: 28px;
      font-weight: 700;
      line-height: 38px;
      font-family: var(--kui-font-secondary);
      color: var(--kui-white);
      background-color: var(--kui-black);
      display: inline;
      cursor: pointer;
      box-shadow:
        -5px 0 0 var(--kui-black),
        5px 0 0 var(--kui-black);
      -webkit-box-decoration-break: clone;
      margin-left: 5px;
      margin-right: -5px;
    }

    &-lead {
      font-size: 16px;
      font-family: var(--kui-font-primary);
      font-weight: 400;
      font-style: normal;
      line-height: 21px;
      color: var(--kui-black);
      margin-top: 10px;
    }

    &-divider {
      display: block;
      width: 100%;
      height: 1px;
      background-color: var(--kui-gray-150);
      margin: 10px 0;

      &:last-of-type {
        margin: 15px 0 10px 0;
      }
    }

    &-date {
      font-size: 12px;
      font-weight: 400;
      line-height: 12px;
      font-style: normal;
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-700);
      padding-left: 20px;
    }
  }

  .icon-news-feed {
    position: absolute;
    width: 120px;
    height: 30px;
    bottom: 0;
    left: 0;
  }
}
