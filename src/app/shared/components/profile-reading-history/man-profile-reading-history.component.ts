import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { RouterLink } from '@angular/router';
import { NgI<PERSON>, NgFor, SlicePipe } from '@angular/common';

@Component({
  selector: 'man-profile-reading-history',
  templateUrl: './man-profile-reading-history.component.html',
  styleUrls: ['./man-profile-reading-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, ManSimpleButtonComponent, SlicePipe],
})
export class ManProfileReadingHistoryComponent {
  @Input() articles: ArticleCard[] = [];

  getArticleUrl(article: ArticleCard): string[] {
    return buildArticleUrl(article);
  }
}
