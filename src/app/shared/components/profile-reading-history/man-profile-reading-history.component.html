<div class="profile-recently">
  <div class="profile-divider"></div>
  <h2 class="profile-title">Utoljára elol<PERSON>ott cikkek</h2>
  <ng-container *ngIf="articles.length > 0; else EMPTY">
    <ul *ngFor="let article of articles | slice: 0 : 5" class="profile-recently-list">
      <li class="profile-recently-list-item">
        <a class="profile-recently-list-item-link" [routerLink]="getArticleUrl(article)">{{ article.title }}</a>
      </li>
    </ul>
    <man-simple-button class="w-100" routerLink="/profil/elozmenyek">Összes korábban elolvasott cikk »</man-simple-button>
  </ng-container>
  <ng-template #EMPTY>
    <p>Nincsenek olvasási előzmények.</p>
    <p class="profile-recently-empty-cta">
      Böngésszen <a class="profile-recently-link" href="/">cikkeink</a> k<PERSON><PERSON><PERSON><PERSON>, í<PERSON> a későbbiekben előzményeit itt bármikor nyomon követheti.
    </p>
  </ng-template>
</div>
