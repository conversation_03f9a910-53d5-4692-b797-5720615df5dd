@use 'shared' as *;

.profile {
  &-divider {
    width: 100%;
    height: 1px;
    background: var(--kui-gray-300);
    margin-bottom: 20px;
  }

  &-title {
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  &-recently {
    &-list {
      &-item {
        margin-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;

        &-link {
          color: var(--kui-orange-600);
          text-decoration: underline;
        }
      }
    }

    &-link {
      color: var(--kui-orange-600);
      text-decoration: underline;
    }

    &-empty-cta {
      margin-top: 10px;
    }
  }
}
