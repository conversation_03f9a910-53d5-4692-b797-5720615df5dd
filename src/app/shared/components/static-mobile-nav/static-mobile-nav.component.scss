@use 'shared' as *;

.static-mobile-nav {
  display: none;

  @include media-breakpoint-down(lg) {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    background: var(--kui-gray-300);
    padding: 10px;

    &-link {
      font-weight: 600;
      line-height: 16px;
    }

    .divider {
      width: 1px;
      height: 16px;
      background: var(--kui-orange-600);
    }
  }
}
