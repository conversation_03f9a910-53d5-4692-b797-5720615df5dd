import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, DossierData } from '@trendency/kesma-ui';
import { ManRelatedArticlesComponent } from '../../related-articles/man-related-articles.component';
import { ManDossierTagComponent } from '../../dossier-tag/man-dossier-tag.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'man-dossier-repeater-card',
  templateUrl: './man-dossier-repeater-card.component.html',
  styleUrls: ['./man-dossier-repeater-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ManDossierTagComponent, ManRelatedArticlesComponent],
})
export class ManDossierRepeaterCardComponent extends BaseComponent<DossierData> {}
