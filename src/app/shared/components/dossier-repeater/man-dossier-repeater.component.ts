import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, BlockTitle, DossierData } from '@trendency/kesma-ui';
import { ManBlockTitleRowComponent } from '../block-title-row/man-block-title-row.component';

@Component({
  selector: 'man-dossier-repeater',
  templateUrl: './man-dossier-repeater.component.html',
  styleUrls: ['./man-dossier-repeater.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManBlockTitleRowComponent],
})
export class ManDossierRepeaterComponent extends BaseComponent<DossierData[]> {
  @Input() blockTitleData: BlockTitle = {
    text: 'Akták',
    url: 'dossziek',
    urlName: 'Még több akta',
    visibleIcon: true,
  };
}
