import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';
import { BaseComponent, TrendingTag } from '@trendency/kesma-ui';
import { RouterLinkActive, RouterLink } from '@angular/router';
import { NgFor } from '@angular/common';

@Component({
  selector: 'man-trending-tags',
  templateUrl: './man-trending-tags.component.html',
  styleUrls: ['./man-trending-tags.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLinkActive, RouterLink],
})
export class ManTrendingTagsComponent extends BaseComponent<TrendingTag[]> {
  @Output() private readonly tagClicked: EventEmitter<boolean> = new EventEmitter<boolean>();

  public onClickTag(): void {
    this.tagClicked.emit(true);
  }
}
