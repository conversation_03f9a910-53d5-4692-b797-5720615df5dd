@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.man-article-title {
  display: block;
  color: var(--kui-orange-600);
  font-weight: 700;
  font-size: 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--kui-gray-100);
  margin-bottom: 30px !important;
  text-align: left;
}

.trending-tags {
  display: grid;
  grid-gap: 20px;
  @include media-breakpoint-up(md) {
    grid-template-columns: repeat(3, 1fr);
  }
  &-item {
    border: 1px solid var(--kui-orange-600);
    display: flex;
    justify-content: space-between;
    color: var(--kui-black);
    text-transform: uppercase;
    &-fig {
      display: flex !important;
      gap: 10px;
      &-img {
        object-fit: cover;
        width: 50px;
        height: 50px !important;
      }
      &-caption {
        display: flex !important;
        align-items: center;
        font-weight: 600;
      }
    }
    &-icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
    }
    .icon {
      width: 24px;
      height: 24px;
      &.icon-mandiner-chevron-up {
        transform: rotate(90deg);
        width: 12px;
        height: 12px;
      }
    }
  }
}
