@use 'shared' as *;

:host {
  display: block;
}

.footer-navigator {
  display: block;
  min-height: 60px;
  background-color: var(--kui-brand-olimpic-dark-gold);
  border-bottom: 4px solid var(--kui-brand-olimpic-gold);
  cursor: pointer;

  .wrapper {
    display: flex;
    max-width: 1310px;
    width: calc(100% - 145px);
    justify-content: space-between;
    padding: 16px 0px 8px 0px;
    margin: 0 auto;

    @include media-breakpoint-down(md) {
      width: calc(100% - 25px);
    }
  }

  &-title,
  &-image-wrapper {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--kui-white);
    flex: 1 0 0;
  }

  &-title {
    justify-content: flex-start;
    color: var(--kui-white);
    font-family: var(--kui-font-opensans);
    font-size: 20px;
    font-weight: 700;
    line-height: 24px;

    @include media-breakpoint-down(sm) {
      line-height: 22px;
      letter-spacing: 0.2px;
    }
  }

  &-image-wrapper {
    justify-content: flex-end;

    @include media-breakpoint-up(sm) {
      gap: 4px;
    }
  }

  &-olimpia-logo {
    width: 140px;
    height: 22px;
  }

  &-chevron {
    height: 24px;
    width: 24px;
  }
}

:host {
  &.is-list-page {
    .wrapper {
      padding: 8px 12px;
      width: 100%;
    }

    .footer-navigator {
      min-height: 48px;
      border-color: #dbcb86;

      &-title,
      &-image-wrapper {
        border-bottom: 0;
        padding-bottom: 0;
      }

      @include media-breakpoint-down(md) {
        &-title {
          font-size: 16px;
          line-height: 18px;
        }

        &-olimpia-logo {
          width: 129px;
          height: 20px;
        }

        &-chevron {
          height: 20px;
          width: 20px;
        }

        .wrapper {
          padding: 8px 0 8px 8px;
        }
      }
    }
  }

  &.is-home-page {
    .wrapper {
      padding: 16px 0;
      width: 100%;
    }

    .footer-navigator {
      margin-inline: 12px;
      background-color: var(--kui-white);
      border-width: 1px;
      min-height: unset;

      &-title {
        color: var(--kui-brand-olimpic-dark-gold);
      }

      &-title,
      &-image-wrapper {
        padding-bottom: 0;
      }
    }
  }
}
