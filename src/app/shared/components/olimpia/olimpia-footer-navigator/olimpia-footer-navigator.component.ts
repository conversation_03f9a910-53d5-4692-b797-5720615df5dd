import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-olimpia-footer-navigator',
  templateUrl: './olimpia-footer-navigator.component.html',
  styleUrl: './olimpia-footer-navigator.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf],
})
export class OlimpiaFooterNavigatorComponent {
  @Input() @HostBinding('class.is-list-page') isListPage: boolean;
  @Input() @HostBinding('class.is-home-page') isHomePage: boolean;

  @Input() title = 'Mindent egy helyen az olimpiáról';
}
