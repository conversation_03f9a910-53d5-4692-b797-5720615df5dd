import { ChangeDetectionStrategy, Component } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { map } from 'rxjs/operators';
import { OlimpiaMedalsNational, OlimpiaResultsComponent, OlimpiaResultsData, OlimpicPortalEnum } from '@trendency/kesma-ui';
import { ApiService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-olimpia-results-block',
  templateUrl: './olimpia-results-block.component.html',
  styleUrls: ['olimpia-results-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

  imports: [NgIf, AsyncPipe, OlimpiaResultsComponent],
})
export class OlimpiaResultsBlockComponent {
  countryMedals$: Observable<MedalTable[]> = this.apiService.getOlimpiaCountryMedals().pipe(map((res) => res.data ?? []));
  nationalMedals$: Observable<OlimpiaMedalsNational[]> = this.apiService.getOlimpiaNationalMedals().pipe(map((res) => res.data ?? []));
  olimpiaResultsData$: Observable<OlimpiaResultsData> = combineLatest([this.countryMedals$, this.nationalMedals$]).pipe(
    map(([medalsTableData, nationalMedalsTableData]) => ({
      medalsTableData,
      nationalMedalsTableData,
    }))
  );
  readonly OlimpicPortalEnum = OlimpicPortalEnum;

  constructor(private readonly apiService: ApiService) {}
}
