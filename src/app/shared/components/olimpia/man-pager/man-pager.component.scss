@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;

  .pager {
    position: relative;

    .button-arrow {
      border-radius: 2px;

      .icon-prev {
        background-image: url('/assets/images/icons/chevron-left-indigo.svg');
      }

      .icon-next {
        background-image: url('/assets/images/icons/chevron-left-indigo.svg');
        transform: rotate(180deg);
      }

      &.last,
      &.first {
        display: none;
      }

      &.next,
      &.prev {
        background-color: var(--kui-white);
        border: 1px solid var(--kui-brand-olimpic-Indigo);
      }

      &.next {
        margin-left: 38px;
      }

      &.prev {
        margin-right: 38px;
      }

      .icon {
        height: 24px;
        width: 24px;
      }
    }

    .nums {
      color: var(--kui-brand-olimpic-Indigo);
    }
    .button-arrow,
    .button-num,
    .count-pager {
      border: 1px solid var(--kui-brand-olimpic-Indigo);
      color: var(--kui-brand-olimpic-Indigo);
      width: 32px;
      height: 32px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 700;
    }

    .list-pager {
      gap: 6px;

      .button-num {
        padding: 6px 7px;

        &.active {
          color: var(--kui-white);
          background-color: var(--kui-brand-olimpic-Indigo);
          font-weight: 700;
          border: 1px solid var(--kui-brand-olimpic-Indigo);
        }
      }
    }

    .buttons + .button-num.more {
      margin-right: 6px;
    }

    .list-pager + .button-num.more {
      margin-left: 6px;
    }

    .separator {
      margin: 0 5px;
    }
  }

  .disabled {
    background-color: var(--kui-gray-50);
  }
}
