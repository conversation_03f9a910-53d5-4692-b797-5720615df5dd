import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PagerComponent as KesmaPagerComponent } from '@trendency/kesma-ui';
import { NgClass, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'mandiner-pager',
  templateUrl: '../../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.html',
  styleUrls: ['../../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.scss', './man-pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink],
})
export class ManPagerComponent extends KesmaPagerComponent {}
