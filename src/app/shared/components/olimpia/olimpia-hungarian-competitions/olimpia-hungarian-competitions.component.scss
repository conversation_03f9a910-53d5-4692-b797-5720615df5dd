@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-opensans);

  .hungarian-competitions {
    &-header-container {
      display: flex;
      min-height: 64px;
      background-color: var(--kui-brand-olimpic-Indigo);
      padding: 16px 12px 8px 12px;
    }

    &-header {
      width: 100%;
      color: var(--kui-white);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0 16px 0;
      border-bottom: 1px solid var(--kui-white);

      @include media-breakpoint-down(md) {
        gap: 8px;
      }

      &-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: normal;
      }

      &-logo {
        width: 155px;
        height: 24px;

        @include media-breakpoint-down(md) {
          width: 130px;
          height: 20px;
        }
      }
    }
  }

  .more-program {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px 0 20px 0;

    &-title {
      display: flex;
      width: 440px;
      min-height: 40px;
      align-items: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      color: var(--kui-brand-olimpic-turquoise);
      justify-content: center;
      line-height: 24px;
      border-radius: 2px;
      padding: 8px;
      border: 1px solid var(--kui-brand-olimpic-turquoise);
      gap: 8px;
      text-transform: uppercase;

      &:hover {
        background-color: var(--kui-brand-olimpic-turquoise);
        color: var(--kui-white);
      }
    }

    @include media-breakpoint-down(md) {
      padding: 24px 0;

      &-title {
        letter-spacing: -0.16px;
      }
    }
  }
}
