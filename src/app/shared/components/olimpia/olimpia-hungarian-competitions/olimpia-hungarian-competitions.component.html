<ng-container *ngIf="hungarianCompetitions$ | async as competitions">
  <div class="hungarian-competitions-header-container">
    <div class="hungarian-competitions-header">
      <span class="hungarian-competitions-header-title">Mai magy<PERSON></span>
      <img class="hungarian-competitions-header-logo" alt="Fejléc logó" [src]="'./assets/images/olimpia/olimpia-logo-white.svg'" loading="lazy" />
    </div>
  </div>
  <kesma-olimpia-hungarian-competitions
    *ngIf="competitions.today.length > 0"
    [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
    [data]="competitions?.today"
  ></kesma-olimpia-hungarian-competitions>
  <kesma-olimpia-hungarian-competitions
    *ngIf="competitions.today.length < 5 && competitions.upcoming.length > 0"
    [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
    [data]="competitions?.upcoming"
    [headerDateOverride]="upcomingDayDate"
  ></kesma-olimpia-hungarian-competitions>
  <div class="more-program">
    <a class="more-program-title" [routerLink]="'/olimpia-2024/menetrend'"
      >A magyar csapat teljes programja <kesma-icon name="icon-olimpia-chevron-right"></kesma-icon>
    </a>
  </div>
</ng-container>
