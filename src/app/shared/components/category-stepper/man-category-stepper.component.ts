import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, inject, Input, Output } from '@angular/core';
import { CategoryStepperComponent, MinuteToMinuteState, toBool } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { ManCategoryStepperInput } from './api/man-category-stepper.definitions';
import { SocialInteractionEvent } from '../social-share-modal/man-social-share-modal.definitions';
import { distinctUntilChanged, fromEvent, map, Observable, of, startWith } from 'rxjs';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { ManTabComponent } from '../tabs/man-tab/man-tab.component';
import { ManTabsComponent } from '../tabs/man-tabs.component';
import { NgIf, NgFor, NgClass, AsyncPipe } from '@angular/common';

@Component({
  selector: 'man-category-stepper',
  templateUrl: './man-category-stepper.component.html',
  styleUrls: ['./man-category-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManTabsComponent, NgFor, ManTabComponent, NgClass, ManArticleCardComponent, AsyncPipe],
})
export class ManCategoryStepperComponent extends CategoryStepperComponent<ManCategoryStepperInput[]> {
  private readonly elementRef = inject(ElementRef<HTMLDivElement>);
  @Input() override articleCardType: ArticleCardType = ArticleCardType.ImgTitleLeadDateMeta;
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  protected readonly MinuteToMinuteState = MinuteToMinuteState;
  protected readonly toBool = toBool;

  currentWindowWidth$: Observable<number> = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        distinctUntilChanged()
      )
    : of(1920);

  // min-width of 3 article card
  wrap$: Observable<boolean> = this.currentWindowWidth$.pipe(map(() => this.elementRef.nativeElement.offsetWidth < 789));

  // max-width of 3 article card
  showSeparator$: Observable<boolean> = this.currentWindowWidth$.pipe(map(() => this.elementRef.nativeElement.offsetWidth < 1152));
}
