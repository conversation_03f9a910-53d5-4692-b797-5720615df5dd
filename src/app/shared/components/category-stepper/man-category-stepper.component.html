<ng-container *ngIf="!data?.length">Nincsenek rovatok felvéve!</ng-container>
<man-tabs #tabs (tabSelected)="tabSelected($event)" [activeTabIndex]="(activeCategoryIndex$ | async) ?? 0" *ngIf="data?.length" [class.w-100]="wide">
  <man-tab
    *ngFor="let tabData of data; let i = index; trackBy: trackByFn"
    [label]="tabData.column.title"
    [active]="i === ((activeCategoryIndex$ | async) ?? 0)"
  >
    <div class="article-container" *ngIf="i === ((activeCategoryIndex$ | async) ?? 0)" [ngClass]="{ wrap: (wrap$ | async) }">
      <ng-container *ngFor="let article of tabData.selectedArticles ?? []">
        <man-article-card
          [data]="article"
          [styleID]="articleCardType"
          [isSidebar]="article.meta?.isSidebar ?? false"
          [isTagVisible]="article.meta?.isTagVisible ?? true"
          [isExcerptVisible]="article.meta?.isExcerptVisible ?? true"
          (socialInteraction)="socialInteraction.emit($event)"
          [hasVideo]="article.meta?.hasVideo ?? false"
          [isMplus]="toBool(article.meta?.isMplus) ?? true"
          [isLive]="article?.['minuteToMinute'] === MinuteToMinuteState.RUNNING ?? false"
          [isWide]="true"
        ></man-article-card>
        <div class="separator" *ngIf="showSeparator$ | async">&nbsp;</div>
      </ng-container>
    </div>
  </man-tab>
</man-tabs>
