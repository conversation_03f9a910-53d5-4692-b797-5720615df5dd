import { ArticleCard, CategoryStepperInput } from '@trendency/kesma-ui';

export type ManCategoryStepperInput = CategoryStepperInput & {
  selectedArticles: (ArticleCard & { link: string } & ManArticleContentMeta)[];
};

export type ManArticleContentMeta = {
  meta?: Partial<{
    isSidebar: boolean;
    isTagVisible: boolean;
    isExcerptVisible: boolean;
    hasVideo: boolean;
    isMplus: boolean;
    isLive: boolean;
    socialInteractions: {
      like: number;
      dislike: number;
      comment: number;
      commentUrl?: string;
      bookmark?: boolean;
    };
  }>;
};
