import { ChangeDetectionStrategy, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { BaseComponent, DossierData } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgI<PERSON>, NgFor } from '@angular/common';

@Component({
  selector: 'man-dossier-list',
  templateUrl: './man-dossier-list.component.html',
  styleUrls: ['./man-dossier-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink],
})
export class ManDossierListComponent extends BaseComponent<DossierData[]> implements OnInit {
  @Output() private readonly dossierClicked: EventEmitter<boolean> = new EventEmitter<boolean>();

  cacheData!: DossierData[];
  protected override setProperties(): void {
    super.setProperties();
    this.setCacheData();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.setCacheData();
  }

  private setCacheData(): void {
    if (!this.data) {
      return;
    }

    if (JSON.stringify(this.cacheData) !== JSON.stringify(this.data)) {
      this.cacheData = this.data;
    }
  }

  public onClickDossier(): void {
    this.dossierClicked.emit(true);
  }
}
