@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.dossier-tag {
  display: flex;
  margin-bottom: 10px;
  border: 1px solid var(--kui-orange-600);

  &:hover {
    background-color: var(--kui-orange-600);

    .dossier-tag-akta-text {
      background-color: var(--kui-black);
    }

    .dossier-tag-text {
      color: var(--kui-white);
    }
  }

  &-thumbnail {
    width: 24px;
    height: 24px;
    object-fit: cover;
  }

  &-flex-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &-akta-text {
    font-size: 12px;

    background-color: var(--kui-orange-600);
    color: var(--kui-white);
    padding: 0 5px;
    display: flex;
    align-items: center;
  }

  &-text {
    padding: 8.5px 10px;
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 21px;
    color: var(--kui-black);
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 10px;

    .icon {
      margin-top: 2px;
      width: 24px;
      height: 24px;

      &.icon-mandiner-chevron-up {
        transform: rotate(90deg);
        width: 12px;
        height: 12px;
      }
    }
  }
}
