<div class="profile-divider"></div>
<h2 class="profile-title">Le<PERSON><PERSON><PERSON>j<PERSON><PERSON> elmentett cikkek</h2>
<ng-container *ngIf="articles.length > 0; else EMPTY">
  <div class="profile-saved profile-grid-3">
    <ng-container *ngFor="let article of articles | slice: 0 : 3; let i = index">
      <man-article-card [data]="article" [styleID]="ArticleCardType.TitleLeadDateMeta" [isMplus]="article.isPaywalled"></man-article-card>
      <man-simple-button class="w-100" *ngIf="i === 1" routerLink="/profil/mentett-cikkek">Összes mentett cikk »</man-simple-button>
    </ng-container>
  </div>
</ng-container>
<ng-template #EMPTY>
  <p>Nincsenek mentett cikkek.</p>
  <p class="profile-saved-empty-cta">
    Kedvenc cikkeit bármikor elmentheti a <a class="profile-saved-link" href="/">cikkek</a> melle<PERSON> könyv<PERSON>lz<PERSON> ikonra kattintva.
  </p>
</ng-template>
