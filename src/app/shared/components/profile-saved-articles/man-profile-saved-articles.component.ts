import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardType } from '../article-card/man-article-card.types';
import { RouterLink } from '@angular/router';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { NgIf, NgFor, SlicePipe } from '@angular/common';

@Component({
  selector: 'man-profile-saved-articles',
  templateUrl: './man-profile-saved-articles.component.html',
  styleUrls: ['./man-profile-saved-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, Ng<PERSON><PERSON>, ManArticleCardComponent, ManSimpleButtonComponent, RouterLink, SlicePipe],
})
export class ManProfileSavedArticlesComponent {
  @Input() articles: ArticleCard[] = [];

  readonly ArticleCardType = ArticleCardType;
}
