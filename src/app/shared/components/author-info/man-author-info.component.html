<div *ngIf="isAuthorPage" class="author-info-name author-title">
  {{ data?.name }}
  <hr class="author-title-line" />
</div>

<figure [style.margin-bottom.px]="!isAuthorPage || 16" class="author-info-header">
  <img *ngIf="data?.avatar; else avatarPlaceholder" [alt]="data?.name" [src]="data?.avatar" class="author-info-img" />
  <ng-template #avatarPlaceholder>
    <div class="author-info-img author-info-img-placeholder"></div>
  </ng-template>
  <figcaption [class.on-author-page]="isAuthorPage" class="author-info-caption">
    <div class="author-info-name">
      <a [routerLink]="['/', 'szerzo', data?.slug]">{{ data?.name }}</a>
      <button
        (click)="!isFollowLoading && followEvent.emit()"
        *ngIf="isInnerAuthor && !data?.articleMedium && showButton"
        [ngClass]="{ followed: isFollowed === FollowStatus.LOGGED_IN_FOLLOWED }"
        class="author-info-btn-follow header-btn"
      >
        <i class="icon icon-check"></i>
        {{ isFollowLoading ? 'Kérem várjon...' : isFollowed === FollowStatus.LOGGED_IN_FOLLOWED ? 'Szerző követve' : 'Szerző követése' }}
      </button>
    </div>
    <div *ngIf="isOpinion" class="author-info-medium">{{ data?.articleMedium }}</div>
    <div *ngIf="isInnerAuthor && showAuthorRank" class="author-info-rank">{{ data?.title }}</div>
  </figcaption>
</figure>
<ng-container *ngIf="showAuthorDetails">
  <div *ngIf="showAuthorDescription" class="author-info-body">
    {{ data?.description }}
  </div>
  <div *ngIf="!isAuthorPage" class="author-info-footer">
    <a [routerLink]="['/', 'szerzo', data?.slug]" class="author-info-footer-btn header-btn">
      Szerző cikkei
      <i class="icon icon-mandiner-chevron-right"></i>
    </a>
  </div>
</ng-container>
