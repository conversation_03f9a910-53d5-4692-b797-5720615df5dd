import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ArticleAuthor, BaseComponent, FollowStatus } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf, NgClass } from '@angular/common';

@Component({
  selector: 'man-author-info',
  templateUrl: './man-author-info.component.html',
  styleUrls: ['./man-author-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgClass],
})
export class ManAuthorInfoComponent extends BaseComponent<ArticleAuthor> implements OnInit {
  @Input() isInnerAuthor: boolean = false;
  @Input() isAuthorPage: boolean = false;
  @Input() showAuthorDetails: boolean = true;
  @Input() showAuthorDescription: boolean = true;
  @Input() showButton: boolean = true;
  @Input() isOpinion: boolean | undefined = false;

  @Input() isFollowed?: FollowStatus | null = FollowStatus.LOGGED_OUT_NOT_FOLLOWED;
  @Input() isFollowLoading: boolean = false;
  @Input() showAuthorRank: boolean = true;
  @Output() followEvent: EventEmitter<void> = new EventEmitter<void>();
  FollowStatus = FollowStatus;
  @HostBinding('style.border-bottom') private borderStyle: string = '1px solid var(--kui-gray-100)';

  public override ngOnInit(): void {
    if (this.isAuthorPage) {
      this.borderStyle = '';
    }
  }
}
