@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  padding: 30px 0 10px;
  max-width: 996px;

  .icon {
    width: 7px;
    height: 12px;
  }

  .author-title {
    color: var(--kui-orange-600);
    line-height: 24px;
    font-size: 24px;

    &-line {
      height: 1px;
      background-color: var(--kui-gray-200);
      border: none;
    }
  }
}

.header-btn {
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
}

.author-info {
  &-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 11px;
    margin-bottom: 5px;
  }

  &-img {
    box-shadow: 0 0 0 1px var(--kui-orange-600);
    border: 1px solid var(--kui-white);
    border-radius: 14px;
    height: 60px;
    width: 60px;

    &-placeholder {
      background-color: var(--kui-gray-800);

      &:after {
        display: block;
        width: 58px;
        height: 58px;
        content: '';
        background-repeat: no-repeat;
        background-size: 40px;
        background-position: center;
        border-radius: 14px;
        @include icon('icons/icon-mandiner-user-gold.svg');
      }
    }
  }

  &-name {
    width: 100%;
    font-weight: 700;
    font-size: 18px;
    line-height: 18px;

    &:not(.author-title) {
      display: flex;
      align-items: center;
      gap: 16px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        gap: 5px;
        margin-bottom: 5px;
        align-items: flex-start;
      }
    }
  }

  &-btn-follow {
    border: 1px solid var(--kui-orange-600);
    border-radius: 3px;
    padding: 6px 12px;
    transition:
      background-color 0.3s ease,
      color 0.3s ease,
      border-color 0.3s ease;
    display: inline-flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    color: var(--kui-black);

    &:not(.followed):hover {
      background-color: var(--kui-orange-600);
      color: var(--kui-white);
    }

    &.followed {
      border: 1px solid var(--kui-black);

      &:hover {
        border-color: var(--kui-orange-600);
      }
    }

    .icon {
      display: none;
      height: 10px;
      width: 14px;
      margin-right: 5px;
    }

    &.followed .icon {
      display: block;
    }
  }

  &-body {
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
    margin-bottom: 10px;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;

    &-btn {
      color: var(--kui-black);
      display: flex;
      align-items: center;
      gap: 5px;
      position: relative;

      &:hover {
        color: var(--kui-orange-600);

        .icon {
          @include icon('icons/icon-mandiner-chevron-right-gold.svg');
        }
      }
    }
  }

  &-medium {
    width: 100%;
    font-size: 14px;
    color: var(--kui-orange-600);
  }

  &-rank {
    color: var(--kui-orange-600);
    width: 100%;
    font-size: 14px;
    line-height: 18px;
  }

  &-caption {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;

    &.on-author-page {
      margin: auto 0;
    }
  }
}
