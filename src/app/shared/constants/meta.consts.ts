export const defaultMetaInfo = {
  title: 'Mandiner',
  //eslint-disable-next-line max-len
  index: 'index, follow, max-image-preview:large',
  //eslint-disable-next-line max-len
  description: '',
  ogTitle: 'Mandiner',
  ogImageWidth: '1200',
  ogImageHeight: '600',
  ogLocale: 'hu_HU',
  //eslint-disable-next-line max-len
  ogDescription: '',
  ogSiteName: 'Mandiner',
  ogType: 'website',
};

export const galleryMetaInfo = {
  title: 'Galéria - Mandiner',
  index: 'index, follow, max-image-preview:large',
  ogType: 'gallery',
};

/**
 * These additional meta infos can be used for categories which needs unique metadata for SEO.
 * Currently there are no information about this for Mandiner.
 * This part is inherited from Metropol, you can check it for more info.
 */
export const categoriesMetaInfo: Record<string, { title: string; description: string; keywords?: string }> = {
  //'aktualis' : {
  //  title: 'Aktuális: friss hírek, információk',
  //eslint-disable-next-line max-len
  //  description: `Folyamatosan f<PERSON>, információk, események Magyarországon és a világban a Metropol Aktuális rovatában.Kövesd és olvasd el nálunk a nap legfontosabb történéseit.`,
  //  keywords: 'friss hírek, információk, időjárás, események'
  //},
};
