import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'manLimitString',
})
export class LimitStringPipe implements PipeTransform {
  /**
   * Limit string to maxCharacters and add ending if needed
   * @param value string to limit
   * @param maxCharacters maximum allowed characters (Ending included)
   * @param ending ending to add if string is limited (default: '...')
   */
  transform(value: string, maxCharacters: number, ending = '...'): string {
    return value.length > maxCharacters ? value.substring(0, maxCharacters - ending.length).trimEnd() + ending : value;
  }
}
