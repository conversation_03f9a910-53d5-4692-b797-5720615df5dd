import { Pipe, PipeTransform } from '@angular/core';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';

@Pipe({
  name: 'manTranslatedDatePipe',
})
export class TranslatedDatePipe implements PipeTransform {
  private readonly monthAndDayFormatInHungarian = 'yyyy. LLLL dd.'; // (EEEE) = nap.
  private readonly options = { locale: hu };

  transform(date: string | Date, dateFormat: string = this.monthAndDayFormatInHungarian): string {
    return format(new Date(date), dateFormat, this.options);
  }
}
