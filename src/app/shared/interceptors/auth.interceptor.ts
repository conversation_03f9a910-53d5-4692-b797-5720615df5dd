import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services';
import { inject } from '@angular/core';

export function authInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<any>> {
  const authService: AuthService = inject(AuthService);
  const SECURE_API: string = 'secureapi';

  const addTokenToSecureApi = (req: HttpRequest<any>): HttpRequest<any> => {
    if (req.url.includes(SECURE_API)) {
      const token: string | undefined = authService.getToken();
      return req.clone({ setHeaders: { 'X-Auth-Token': token ?? '' } });
    }

    return req;
  };

  return next(addTokenToSecureApi(req));
}
