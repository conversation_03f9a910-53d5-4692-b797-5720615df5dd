import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { FollowStatus } from '@trendency/kesma-ui';
import { Router } from '@angular/router';
import { SecureApiService } from './secure-api.service';
import { UserExtraData } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class FollowHelperService {
  constructor(
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly secureApiService: SecureApiService
  ) {}

  getColumnFollowedStatus(columnSlug: string): Observable<FollowStatus> {
    return this.authService.currentUserExtraDataSubject.pipe(map(() => this.isColumnFollowed(columnSlug)));
  }

  isColumnFollowed(columnSlug: string): FollowStatus {
    const userExtraData: UserExtraData | undefined = this.authService.currentUserExtraData;

    if (!columnSlug || !userExtraData || !userExtraData.followedColumns) {
      return FollowStatus.LOGGED_OUT_NOT_FOLLOWED;
    }

    if (userExtraData.followedColumns.includes(columnSlug)) {
      return FollowStatus.LOGGED_IN_FOLLOWED;
    }

    return FollowStatus.LOGGED_IN_NOT_FOLLOWED;
  }

  handleColumnFollow(columnSlug: string): Observable<void> {
    const isColumnFollowed: FollowStatus = this.isColumnFollowed(columnSlug);
    const followedColumns: string[] = this.authService.currentUserExtraData?.followedColumns ?? [];

    if (isColumnFollowed === FollowStatus.LOGGED_IN_NOT_FOLLOWED) {
      return this.secureApiService.setFollowedColumn(columnSlug).pipe(
        tap(() => {
          this.authService.currentUserExtraDataSubject.next({
            ...this.authService.currentUserExtraData,
            followedColumns: followedColumns.filter((slug: string) => slug !== columnSlug).concat(columnSlug),
          });
        })
      );
    } else if (isColumnFollowed === FollowStatus.LOGGED_IN_FOLLOWED) {
      return this.secureApiService.deleteFollowedColumn(columnSlug).pipe(
        tap(() => {
          this.authService.currentUserExtraDataSubject.next({
            ...this.authService.currentUserExtraData,
            followedColumns: followedColumns.filter((slug: string) => slug !== columnSlug),
          });
        })
      );
    } else {
      this.router.navigate([`/regisztracio`], { queryParams: { redirect: this.router.url } });
      return new Observable<void>();
    }
  }

  getAuthorFollowedStatus(authorSlug: string): Observable<FollowStatus> {
    return this.authService.currentUserExtraDataSubject.pipe(map(() => this.isAuthorFollowed(authorSlug)));
  }

  isAuthorFollowed(authorSlug: string, extraData?: UserExtraData): FollowStatus {
    const userExtraData: UserExtraData | undefined = extraData ?? this.authService.currentUserExtraData;

    if (!authorSlug || !userExtraData || !userExtraData.followedAuthors) {
      return FollowStatus.LOGGED_OUT_NOT_FOLLOWED;
    }

    if (userExtraData.followedAuthors.includes(authorSlug)) {
      return FollowStatus.LOGGED_IN_FOLLOWED;
    }

    return FollowStatus.LOGGED_IN_NOT_FOLLOWED;
  }

  handleAuthorFollow(authorSlug: string): Observable<void> {
    const isAuthorFollowed: FollowStatus = this.isAuthorFollowed(authorSlug);
    const followedAuthors: <AUTHORS>

    if (isAuthorFollowed === FollowStatus.LOGGED_IN_NOT_FOLLOWED) {
      return this.secureApiService.setFollowedAuthor(authorSlug).pipe(
        tap(() => {
          this.authService.currentUserExtraDataSubject.next({
            ...this.authService.currentUserExtraData,
            followedAuthors: <AUTHORS>
          });
        })
      );
    } else if (isAuthorFollowed === FollowStatus.LOGGED_IN_FOLLOWED) {
      return this.secureApiService.deleteFollowedAuthor(authorSlug).pipe(
        tap(() => {
          this.authService.currentUserExtraDataSubject.next({
            ...this.authService.currentUserExtraData,
            followedAuthors: <AUTHORS>
          });
        })
      );
    } else {
      this.router.navigate([`/regisztracio`], { queryParams: { redirect: this.router.url } });
      return new Observable<void>();
    }
  }
}
