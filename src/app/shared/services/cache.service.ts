import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CacheService {
  private readonly cache: Map<string, { data: unknown; ttl: number }> = new Map();

  get<T>(key: string): T | undefined {
    const cachedData = this.cache.get(key);
    if (!cachedData) {
      return undefined;
    }

    if (cachedData.ttl < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }

    return cachedData.data as T;
  }

  set<T>(key: string, value: T, timeoutMs = 10000): void {
    this.cache.set(key, {
      data: value,
      ttl: Date.now() + timeoutMs,
    });
  }

  has(key: string): boolean {
    if (!this.cache.has(key)) {
      return false;
    }

    const cachedData = this.cache.get(key);
    if (!cachedData) {
      return false;
    }

    if (cachedData.ttl < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}
