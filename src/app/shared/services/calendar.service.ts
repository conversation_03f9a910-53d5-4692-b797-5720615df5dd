import { Injectable } from '@angular/core';
import { Calendar } from '../definitions';
import { ApiService } from './api.service';
import { map } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CalendarService {
  readonly calendarSubject = new BehaviorSubject<Calendar>({} as Calendar);

  public calendar$: Observable<Calendar> = this.calendarSubject.asObservable();

  constructor(private readonly apiService: ApiService) {}

  public fetchCalendar(): void {
    this.apiService
      .getCalendar()
      .pipe(map((apiResult) => apiResult.data))
      .subscribe((calendar: Calendar) => {
        this.setCalendar(calendar);
      });
  }

  public setCalendar(calendar: Calendar): void {
    this.calendarSubject.next(calendar);
  }
}
