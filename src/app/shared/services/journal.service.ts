import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ArticleCardWithSecondaryColumns, HeaderMagazineData, JournalColumn, JournalDataWithArticles, JournalDataWithColumns } from '../definitions';
import { map, switchMap } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, ArticleCard, buildArticleUrl, User } from '@trendency/kesma-ui';
import { Router } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class JournalService {
  constructor(
    private readonly apiService: ApiService,
    private readonly utilService: UtilService,
    private readonly router: Router
  ) {}

  subscribeClicked(): void {
    window.open(environment.shopUrls.subscriptions);
  }

  pdfClicked(user: User | undefined, item: JournalDataWithArticles): void {
    if (!user) {
      this.router.navigate(['/', 'bejelentkezes']);
    } else if (user.hasValidSubscription) {
      this.router.navigate(['/', 'hetilap', item.urlSlug, 'lapozhato-pdf']);
    } else if (!user.hasValidSubscription) {
      window.open(environment.shopUrls.subscriptions);
    }
  }

  getHeaderMagazineData(): Observable<HeaderMagazineData | null> {
    return this.utilService.isBrowser()
      ? this.apiService.getHeaderMagazine().pipe(map((response) => (response?.data?.journalIssue ? response.data : null)))
      : of(null);
  }

  getInitialJournalListData(from: number, count = 13): Observable<ApiResult<JournalDataWithArticles[], ApiResponseMetaList>> {
    return this.apiService.getJournals(from, count);
  }

  getJournalListData(from: number, count = 13): Observable<JournalDataWithArticles[]> {
    return this.apiService.getJournals(from, count).pipe(map((data) => data.data));
  }

  getArticlesByJournal(journalId: string): Observable<ArticleCardWithSecondaryColumns[]> {
    return this.apiService.getArticlesByJournal(journalId).pipe(map((data) => data.data));
  }

  getJournalDetails(slug: string): Observable<JournalDataWithColumns> {
    return this.apiService.getJournal(slug).pipe(
      switchMap((response) =>
        this.getArticlesByJournal(slug).pipe(
          map((articles) => ({
            ...response.data,
            columns: this.groupByColumn(articles),
          }))
        )
      )
    );
  }

  getFeaturedArticlesOfJournal(journalSlug: string): Observable<ArticleCard[]> {
    return this.apiService.getArticlesByJournal(journalSlug).pipe(
      map((response) => response.data),
      map((data) => {
        const highlighted = [] as ArticleCard[]; // Articles on the side, with image and lead
        const featured = [] as ArticleCard[]; // Articles in the middle, with only title

        for (let i = 0; i < data.length; i++) {
          // If we have 2 highlighted and 6 featured articles, we can stop
          if (featured.length + highlighted.length >= 8) {
            break;
          }

          const item = data[i] as ArticleCard & { id: string }; // BE always sends id here
          // We need two highlighted articles with a thumbnail and lead
          if (highlighted.length < 2 && item.lead && (item.thumbnail || item.thumbnailUrl)) {
            highlighted.push(item);
            continue;
          }

          // We need 6 featured articles
          if (featured.length < 6 && !(item.id in highlighted.map((h) => h.id))) {
            featured.push(item);
          }
        }

        return [...highlighted, ...featured];
      })
    );
  }

  private groupByColumn(articles: ArticleCardWithSecondaryColumns[]): JournalColumn[] {
    const columns: Record<string, JournalColumn> = {};
    articles.forEach((article) => {
      const firstSecondaryColumn = article?.secondaryColumns?.[0];
      const index = firstSecondaryColumn?.slug ?? article.columnSlug;
      if (!index) {
        return;
      }

      if (!columns[index]) {
        columns[index] = {
          slug: index,
          title: firstSecondaryColumn?.title ?? article.columnTitle ?? '',
          order: (article as unknown as ArticleCard & { columnOrder: number | null }).columnOrder ?? Number.MAX_VALUE,
          // If columnOrder is null, it should be the last column
          articles: [],
        };
      }
      columns[index].articles.push({ ...article, url: buildArticleUrl(article).join('/') });
    });
    return Object.values(columns).sort((a, b) => a.order - b.order || a.title.localeCompare(b.title));
  }
}
