import { inject, Injectable } from '@angular/core';

import { Observable, Subject, take } from 'rxjs';
import { differenceInMinutes } from 'date-fns';
import { DOCUMENT } from '@angular/common';

const LOCALSTORAGE_TIMEOUT_KEY = 'kesma-vignette-last-activation';
/**
 * !!! Ensure that users see a maximum of one ad per hour !!!
 */
const VIGNETTE_TIMEOUT_MINUTES = 60;
@Injectable({
  providedIn: 'root',
})
export class VignetteAdService {
  private readonly document = inject(DOCUMENT);
  /**
   * Indicates that the Vignette could be closed.
   * Used to provide a method for signaling back from the event handler.
   * @private
   */
  private readonly canClose$ = new Subject<void>();

  /**
   * Marks if the user has been already saw a vignette since the app has been initialized.
   * This has no added functionality, only used to not check the Vignette anymore after it has been shown.
   * @private
   */
  private hasVignetteDisplayedInCurrentSession = false;

  /**
   * Return the date of the last emission of the Vignette.
   * If the localstorage item does not existst or if it's invalid, the date will be the
   * smallest representable date in order to have the required difference.
   */
  get latestVignette(): Date | null {
    const lastString = localStorage.getItem(LOCALSTORAGE_TIMEOUT_KEY) ?? '';
    try {
      const d = new Date(parseInt(lastString) * 1000);
      if (isNaN(d.getTime())) {
        throw new Error('Invalid date');
      }
      return d;
    } catch (e) {
      return null;
    }
  }

  /**
   * Handles the removal of the vignette when it emits back the specified value.
   * You can change the closing conditions in this function!
   * @param vignette
   */
  vignetteMessageHandler(vignette: HTMLIFrameElement) {
    return (e: any): void => {
      try {
        const messageData = JSON.parse(e.data);
        console.log('VignetteAdService -> Message received: ', messageData);
        // The vignette ins element sends back very specific messages, maybe we have to change this in the future.
        if (messageData.msg_type === 'i-dismiss' || messageData.eventType === 'adClosed') {
          vignette.style.display = 'none';
          this.canClose$.next();
        }
      } catch (e) {
        /* empty */
      }
    };
  }

  canDeactivate(): Observable<boolean> | Promise<boolean> | boolean {
    return new Promise((resolve) => {
      // Short circuit to increase performance after the Vignette has been shown since the app initialized.
      // As Vignette could be shown once per hour it is not necessary to check it before every route change.
      // If there is no LocalStorage in the browser we should skip the Vignette logic.
      if (this.hasVignetteDisplayedInCurrentSession || !localStorage) {
        resolve(true);
        return;
      }
      const now = new Date();
      const latestVignetteDate = this.latestVignette;

      if (!latestVignetteDate || Math.abs(differenceInMinutes(latestVignetteDate, now)) > VIGNETTE_TIMEOUT_MINUTES) {
        const vignette: HTMLIFrameElement | undefined = this.document.querySelector('ins.adsbygoogle[data-vignette-loaded="true"]') as HTMLIFrameElement;
        if (!vignette) {
          resolve(true);
          return;
        }

        // Force close vignette after 500ms if the message does not arrive to us.
        // This is to ensure that the vignette will always close as sometimes the message does not use the predefines message format
        // The delay is to ensure that the click event is processed by the browser and the ad link is opened in a new tab.
        vignette?.addEventListener(
          'click',
          () => {
            setTimeout(() => {
              if (vignette.style.display === 'block') {
                console.log('VignetteAdService -> Closing vignette after clicking and waiting for 500ms', vignette);
                vignette.style.display = 'none';
                this.canClose$.next();
              }
            }, 500);
          },
          { once: true, passive: true }
        );
        vignette.style.display = 'block';
        localStorage.setItem(LOCALSTORAGE_TIMEOUT_KEY, Math.floor(now.getTime() / 1000).toString());
        this.hasVignetteDisplayedInCurrentSession = true;
        const handler = this.vignetteMessageHandler(vignette);
        window.addEventListener('message', handler);
        //We should stop listening for window messages after the vignette has been closed.
        this.canClose$.pipe(take(1)).subscribe(() => {
          window.removeEventListener('message', handler);
          vignette?.remove();
          resolve(true);
        });
      } else {
        resolve(true);
      }
    });
  }
}
