import { Injectable } from '@angular/core';
import { PortalConfigSetting } from '@trendency/kesma-ui';
import { PortalConfigService } from './portal-config.service';

@Injectable({
  providedIn: 'root',
})
export class EbService {
  private slug = '';

  constructor(private readonly portalConfigService: PortalConfigService) {}

  setSlug(slug: string): void {
    this.slug = slug;
  }

  getSlug(): string {
    return this.slug;
  }

  isEnableFootballEbElements(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }
}
