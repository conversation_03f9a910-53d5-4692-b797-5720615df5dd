import { inject, Injectable } from '@angular/core';
import { CurrencyExchangeRateData } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { CleanHttpService } from './clean-http.service';
import { Header } from '../definitions';
import { environment } from '../../../environments/environment';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class ExchangeRateService {
  private readonly httpService = inject(CleanHttpService);
  private readonly utilsService = inject(UtilService);

  get financialApiUrl(): string {
    if (typeof environment.financialApiUrl === 'string') {
      return environment.financialApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.financialApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getExchangeRates(): Observable<Header> {
    return this.httpService
      .get<{
        data: CurrencyExchangeRateData;
      }>(`${this.financialApiUrl}/exchangerate/latest`, { withCredentials: false })
      .pipe(
        map((data) => {
          const exchangeData = data.data as CurrencyExchangeRateData;
          const usdData = exchangeData.rates['USD'];
          const eurData = exchangeData.rates['EUR'];
          return {
            exchangeRateDollar: {
              currency: 'USD',
              price: usdData?.rate
                ? usdData.rate.toLocaleString('hu', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
                : '--',
              direction: usdData?.direction === 'up' ? 'up' : 'down',
            },
            exchangeRateEuro: {
              currency: 'EUR',
              price: eurData?.rate
                ? eurData.rate.toLocaleString('hu', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
                : '--',
              direction: eurData?.direction === 'up' ? 'up' : 'down',
            },
          };
        })
      );
  }
}
