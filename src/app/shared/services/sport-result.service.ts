import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiListResult, ApiResult, LiveSport, SingleElimination } from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { CompetitionSummary } from '../components/eb/eb-teams/eb-teams.definitions';
import { ScheduleByCompetitions, SportCompetitions } from '../definitions/eb.definitions';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class SportResultService {
  constructor(private readonly reqService: ReqService) {}

  getLiveSports(): Observable<ApiListResult<LiveSport>> {
    return this.reqService.get<ApiListResult<LiveSport>>(`/sport/live-sports`);
  }

  getCompetitions(liveSportId: string): Observable<ApiListResult<SportCompetitions>> {
    return this.reqService.get(`/sport/competitions/mandiner/${liveSportId}`);
  }

  getScheduleByCompetition(competitionSlug: string): Observable<ApiResult<ScheduleByCompetitions>> {
    if (!competitionSlug) return of({ data: { schedules: [] }, meta: { responseType: 'item', dataCount: 0, requestUrl: '' } });
    return this.reqService.get(`/sport/schedule/by-competition/${competitionSlug}`);
  }

  getCompetitionSummary(competitionSlug: string): Observable<CompetitionSummary[]> {
    return this.reqService.get<{ data: CompetitionSummary[] }>(`sport/competition/${competitionSlug}/summary`).pipe(map(({ data }) => data));
  }

  getSchedulesGroupedByRound(competitionSlug: string): Observable<SingleElimination> {
    return this.reqService.get<{ data: SingleElimination }>(`/sport/schedule/groupped-by-round/${competitionSlug}`).pipe(map(({ data }) => data));
  }
}
