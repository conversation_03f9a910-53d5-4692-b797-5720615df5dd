import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, distinctUntilChanged, Observable, Subject, takeUntil } from 'rxjs';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Location } from '@angular/common';

const SPORT_ARTICLE_PATTERN = /^^\/sport(?:\/.*|\?.*)?$/;
const SPORT_CATEGORY_PATTERN = /^^\/rovat\/sport(?:\/.*|\?.*)?$/;

@Injectable({
  providedIn: 'root',
})
export class SportService implements OnDestroy {
  readonly #unSubscribe = new Subject<boolean>();
  readonly #isSportSubject = new BehaviorSubject<boolean>(false);

  isSport$: Observable<boolean> = this.#isSportSubject.asObservable().pipe(distinctUntilChanged());

  constructor(
    private readonly router: Router,
    private readonly location: Location
  ) {
    this.setIsSport();
  }

  public get isSport(): boolean {
    return this.#isSportSubject.value || SPORT_ARTICLE_PATTERN.test(this.location.path()) || SPORT_CATEGORY_PATTERN.test(this.location.path());
  }

  public handleChanges(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.#unSubscribe)
      )
      .subscribe(() => {
        this.setIsSport();
      });
  }
  ngOnDestroy(): void {
    this.#unSubscribe.next(true);
    this.#unSubscribe.complete();
  }
  private setIsSport(): void {
    const isSport = SPORT_ARTICLE_PATTERN.test(this.location.path()) || SPORT_CATEGORY_PATTERN.test(this.location.path());
    this.#isSportSubject.next(isSport);
  }
}
