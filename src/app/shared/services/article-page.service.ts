import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import {
  ApiResponseMeta,
  ApiResult,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  BackendArticle,
  BackendRecommendationsData,
  previewBackendArticleToArticleCard,
  RecommendationsData,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { backendArticleSocialToSocial, backendArticlesToArticles, backendRecommendedArticleToArticleCard, externalRecommendationToArticleCard } from '../utils';
import { BackendArticleSocial } from '../definitions';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(
    private readonly reqService: ReqService,
    private readonly apiService: ApiService
  ) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    return this.reqService
      .get<ApiResult<BackendArticle, ApiResponseMeta>>(`/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}`, {
        params: {
          previewType,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getArticleRedirect(requestUrl: string): Observable<{ url: string }> {
    return this.reqService.get(`/portal/redirection?url=${requestUrl}`);
  }

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${(month + '').padStart(2, '0')}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: backendArticlesToArticles(data),
        meta,
      }))
    );
  }

  getArticleRecommendations(articleSlug: string, categorySlug?: string): Observable<ApiResult<RecommendationsData>> {
    if (categorySlug) {
      const request$ = this.apiService.getCategoryArticles(categorySlug, 0, 5).pipe(
        switchMap((categoryArticles) => {
          return this.reqService
            .get<
              ApiResult<BackendRecommendationsData>
            >(`/content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`)
            .pipe(
              map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
                data: {
                  ...data,
                  categoryArticles: categoryArticles.data,
                  highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                  lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                  externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
                  lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
                },
                meta,
              }))
            );
        })
      );
      return request$;
    } else {
      return this.reqService
        .get<
          ApiResult<BackendRecommendationsData>
        >(`/content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`)
        .pipe(
          map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
            data: {
              ...data,
              highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
              lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
              externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
              lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
            },
            meta,
          }))
        );
    }
  }

  getArticleSocialData(article: ApiResult<Article>): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticleSocial>>(`/content-page/article/${article.data.id}/social-count`).pipe(
      map((result: ApiResult<BackendArticleSocial>) => {
        return {
          data: {
            ...article.data,
            ...backendArticleSocialToSocial(result.data),
          },
          meta: article.meta,
        };
      })
    );
  }

  prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    return (body ?? []).map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.prepareArticleBodyDetail(detail, bodyPart.type),
      })),
    }));
  }

  prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: { ...previewBackendArticleToArticleCard(detail.value), ...{ label: { text: 'Ezt is ajánljuk a témában' } } },
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
}
