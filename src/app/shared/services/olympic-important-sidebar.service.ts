import { Injectable, makeStateKey, TransferState } from '@angular/core';
import { Layout, PortalConfigSetting } from '@trendency/kesma-ui';
import { BehaviorSubject, catchError, map, Observable, of, switchMap } from 'rxjs';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { StaticPageService } from 'src/app/feature/static-page/api/static-page.service';

export const TRANSFERSTATE_KEY = 'olympic-2024-important-sidebar';
export const SIDEBAR_SLUG = 'sport-olimpia-2024-kozos';

@Injectable({
  providedIn: 'root',
})
export class OlympicImportantSidebarService {
  #isLayoutRequested = false;
  readonly #layout$ = new BehaviorSubject<Layout | null>(null);
  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly transferState: TransferState,
    private readonly portalConfig: PortalConfigService
  ) {
    if (this.transferState.hasKey(this.stateKey)) {
      this.#isLayoutRequested = true;
      const layout = this.transferState.get(this.stateKey, null);
      this.#layout$.next(layout);
    }
  }
  readonly stateKey = makeStateKey<Layout>(TRANSFERSTATE_KEY);

  getLayout(): Observable<Layout | null> {
    if (!this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS)) {
      return of(null);
    }
    const layout$ = this.#layout$.asObservable();
    if (this.#isLayoutRequested) {
      return layout$;
    }
    this.#isLayoutRequested = true;
    return this.staticPageService.getStaticPage(SIDEBAR_SLUG).pipe(
      catchError(() => {
        return of(null);
      }),
      map((res) => {
        const data = res?.data as any as Layout;
        if (data) {
          this.#layout$.next(data);
        }
        return data;
      }),
      switchMap(() => layout$)
    );
  }
}
