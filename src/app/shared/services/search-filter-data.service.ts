import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { format, sub } from 'date-fns';
import { FilterData, FilterValues, ITimeframe, OwnMaterialFilter, SearchFilterDefinitions } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class SearchFilterDataService {
  data = {
    datum: [
      {
        title: 'Bármikor',
        slug: 'anytime',
        match: 2335,
      },
      {
        title: 'Az elmúlt 24 órában',
        slug: 'last_24_hours',
        match: 15,
      },
      {
        title: 'Az utóbbi héten',
        slug: 'last_week',
        match: 82,
      },
      {
        title: '<PERSON>z utóbbi hónapban',
        slug: 'last_month',
        match: 324,
      },
      {
        title: '<PERSON>z utóbbi év<PERSON>',
        slug: 'last_year',
        match: 1430,
      },
      {
        title: 'Pontos dátum szerint',
        slug: 'exact_date',
        match: null,
      },
    ],
    rovat: [] as FilterData[],
    tipus: [
      {
        title: 'Cikk',
        slug: 'article',
        match: 2335,
      },
      {
        title: 'Videó',
        slug: 'articleVideo',
        match: 246,
      },
      {
        title: 'Szerzőink írásai',
        slug: 'opinion',
        match: 192,
      },
    ],
  };

  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  public getSearchFilterColoumns(): Observable<SearchFilterDefinitions> {
    return this.apiService.getParentColumns().pipe(
      map((res) => {
        this.data = {
          ...this.data,
          rovat: res.data.map((data) => ({ ...data, match: null })).filter((data) => data.slug !== 'hetilap'),
        };
        return this.data;
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(err);
      })
    );
  }

  public setSearchQuery(filterValues: FilterValues): ITimeframe {
    const timeframeObj: ITimeframe = { fromDate: undefined, toDate: undefined };
    if (filterValues?.dateFilterType) {
      const today = format(new Date(), 'yyyy-MM-dd');
      const yesterday = format(sub(new Date(), { days: 1 }), 'yyyy-MM-dd');
      const lastWeek = format(sub(new Date(), { days: 7 }), 'yyyy-MM-dd');
      const lastMonth = format(sub(new Date(), { months: 1 }), 'yyyy-MM-dd');
      const lastYear = format(sub(new Date(), { years: 1 }), 'yyyy-MM-dd');

      switch (filterValues.dateFilterType) {
        case 'anytime':
          filterValues.toDate = undefined;
          filterValues.fromDate = undefined;
          break;
        case 'last_24_hours':
          timeframeObj.fromDate = `${yesterday}`;
          timeframeObj.toDate = `${today}`;
          break;
        case 'last_week':
          timeframeObj.fromDate = `${lastWeek}`;
          timeframeObj.toDate = `${today}`;
          break;
        case 'last_month':
          timeframeObj.fromDate = `${lastMonth}`;
          timeframeObj.toDate = `${today}`;
          break;
        case 'last_year':
          timeframeObj.fromDate = `${lastYear}`;
          timeframeObj.toDate = `${today}`;
          break;
        case 'exact_date':
          timeframeObj.fromDate = filterValues.fromDate ? `${format(filterValues.fromDate as Date, 'yyyy-MM-dd')}` : undefined;
          timeframeObj.toDate = filterValues.toDate ? `${format(filterValues.toDate as Date, 'yyyy-MM-dd')}` : undefined;
          break;
      }
    }
    const searchQuery: ITimeframe = {
      fromDate: timeframeObj.fromDate,
      toDate: timeframeObj.toDate,
    };
    return searchQuery;
  }

  // We need remove 'opinion' from contentTypes array and set ownMaterial property
  setcontentTypes(contentTypes: string[] | undefined): OwnMaterialFilter {
    if (!contentTypes || contentTypes.indexOf('opinion') === -1) {
      return { contentTypes, ownMaterial: undefined };
    } else {
      const correctContentTypes = contentTypes?.slice(0, contentTypes?.indexOf('opinion'));
      return {
        contentTypes: correctContentTypes,
        ownMaterial: 'own_material',
      };
    }
  }
}
