import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Injector, runInInjectionContext } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ScriptLoaderService } from '@trendency/kesma-ui';
import { BehaviorSubject, interval, Subject, takeUntil, timer } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AdvertService {
  constructor(
    private readonly injector: Injector,
    private readonly utilsService: UtilService,
    private readonly scriptLoaderService: ScriptLoaderService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  destroyInterval$ = new Subject<void>();
  readonly enableAdsSubject = new BehaviorSubject<boolean>(true);

  disableAds(): void {
    this.enableAdsSubject.next(false);
    this.addAdsDoNotShowClass();
    this.advertInterval();
  }

  enableAds(): void {
    this.enableAdsSubject.next(true);
    this.destroyInterval$.next();
    this.removeAdsDoNotShowClass();
    this.advertInterval();
  }

  isAdEnabled(): boolean {
    return this.enableAdsSubject.getValue();
  }

  callAdvertScriptOnNavigation(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    setTimeout(() => {
      if (this.isAdEnabled()) {
        this.reloadAds();
      } else {
        this.clearAds();
      }
    }, 1000);
  }

  advertInterval(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (this.isAdEnabled()) {
      return;
    }

    const advertCleaner$ = interval(500).pipe(takeUntil(timer(5000)), takeUntil(this.destroyInterval$));
    advertCleaner$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.clearAds();
      }
    });
  }

  clearAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.clearAds();
  }

  reloadAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.spaHardReset();
    window.__adsConfig?.reinsertStrossle();
  }

  handleAdDisabling(isWithoutAds: boolean): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!isWithoutAds === this.enableAdsSubject.getValue()) {
      return;
    }

    if (isWithoutAds) {
      this.disableAds();
    } else {
      this.enableAds();
    }
  }

  loadStrossleScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }
    setTimeout(() => {
      runInInjectionContext(this.injector, () => {
        const activatedRoute = inject(ActivatedRoute, { optional: true });
        if (activatedRoute?.snapshot?.firstChild?.data?.['skipSsrConditionalElements']) {
          return;
        }
        const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        const strossleSrc = 'https://cdn-alpha.adsinteractive.com/mandiner.hu.js?v=' + todayDate;

        this.scriptLoaderService.loadScript(strossleSrc, true, false);
      });
    }, 500);
  }

  addAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.add('adsDoNotShowAds');
  }

  removeAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.remove('adsDoNotShowAds');
  }
}
