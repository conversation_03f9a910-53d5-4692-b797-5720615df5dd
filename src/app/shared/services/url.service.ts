import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UrlService {
  private currentUrl = '';
  private readonly previousUrl: BehaviorSubject<string> = new BehaviorSubject<string>('');

  public previousUrl$: Observable<string> = this.previousUrl.asObservable();

  public setPreviousUrl(newUrl: string): void {
    const previousUrl = this.previousUrl.value;
    this.previousUrl.next(this.currentUrl);
    if (!previousUrl && !newUrl.includes('/galeria/')) {
      this.currentUrl = newUrl;
    }
  }
}
