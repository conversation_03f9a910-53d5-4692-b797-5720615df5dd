import { MenuChild } from '@trendency/kesma-ui';

export type Header = Readonly<{
  searchRecommendedExpressions?: MenuChild[];
  searchRecommendedArticles?: null;

  exchangeRateEuro: {
    currency: string;
    price: string;
    direction?: string;
  };

  exchangeRateDollar: {
    currency: string;
    price: string;
    direction?: string;
  };
}>;

/**
 * FIXME!
 * Draft version just to make the component work!
 * ADJUST IT FOR THE BE RESPONSE!
 */
export type HeaderMagazine = Readonly<{
  featuredItem: HeaderMagazineItem;
  sideItems?: HeaderMagazineItem[];
}>;
export type HeaderMagazineItem = Readonly<{
  title: string;
  subtitle?: string;
  version?: string;
  coverUrl?: string;
  coverAlt?: string;
  url?: string;
}>;
