export interface ISearchParams {
  readonly global_filter?: string;
  readonly from_date?: string;
  readonly to_date?: string;
  readonly 'publishDate_order[0]'?: 'asc' | 'desc';
  readonly 'content_types[]'?: string[];
  readonly 'columnSlugs[]'?: string[];
  readonly 'tagSlugs[]'?: string[];
  readonly 'priorityIds[]'?: string[];
  readonly page_limit?: string;
  readonly rowCount_limit?: string;
  readonly material_types_only?: string;
}

export interface ITimeframe {
  fromDate: string | undefined;
  toDate: string | undefined;
}

export interface OwnMaterialFilter {
  contentTypes?: string[];
  ownMaterial?: string;
}
