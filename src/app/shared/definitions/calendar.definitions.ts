import { ArticleBody } from '@trendency/kesma-ui/lib/definitions/article-card.definitions';

export type Calendar = Readonly<{
  readonly id: string;
  readonly title: string;
  readonly type: CalendarType;
  readonly content: ArticleBody[];
  readonly url: string;
  readonly popupImageUrl: string;
  readonly itemEndImageUrl: string;
  readonly slug?: string; // Only for Advent type.
}>;

export enum CalendarType {
  Popup = 'popup',
  ContentLink = 'content_link',
  Advent = 'advent',
  Article = 'article', // Custom type for Article Page
}
