import { ISearchParams } from './search-definitions';

export type AuthorData = Readonly<{
  readonly facebook: string;
  readonly instagram: string;
  readonly publicAuthorDescription: string;
  readonly publicAuthorName: string;
  readonly tiktok: string;
  readonly avatar: AuthorDataAvatar;
  readonly title?: string;
  readonly rank?: string;
  readonly slug?: string;
  readonly name?: string;
}>;

export type AuthorDataAvatar = Readonly<{
  readonly fullSizeUrl: string;
  readonly thumbnailUrl: string;
  readonly variantId: number;
  readonly altText?: string;
}>;

export type BackendAuthorData = Readonly<
  AuthorData & {
    public_author_name: string;
    public_author_description?: string;
  }
>;

export interface IAthorSearchListParams extends ISearchParams {
  author?: string;
  authorSlug?: string;
}
