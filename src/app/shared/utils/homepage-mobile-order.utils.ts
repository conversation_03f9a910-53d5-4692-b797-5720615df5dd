import { LayoutElement, LayoutElementColumn, LayoutElementContent, LayoutElementRow, LayoutElementType, LayoutStruct } from '@trendency/kesma-ui';

export function makeMobileLayout(sortedContentElements: LayoutElementContent[]): LayoutElementRow {
  return <LayoutElementRow>{
    id: 'mobile-row',
    type: LayoutElementType.Row,
    hideMobile: false,
    withBlockTitle: false,
    elements: [
      <LayoutElementColumn>{
        id: 'mobile-col',
        type: LayoutElementType.Column,
        widthDesktop: 12,
        hideMobile: false,
        withBlockTitle: false,
        elements: sortedContentElements,
      },
    ],
  };
}

export function searchPreviewLayoutContentElements(structure: LayoutStruct[]): LayoutElementContent[] {
  const contentElements: LayoutElementContent[] = [];
  structure.forEach((element: any) => {
    getLayoutContentElements(element, contentElements);
  });

  return contentElements.sort(sortByMobileOrder);
}

export function searchLayoutContentElements(structure: LayoutElementRow[]): LayoutElementContent[] {
  const contentElements: LayoutElementContent[] = [];
  structure.forEach((element: any) => {
    getLayoutContentElements(element, contentElements);
  });

  return contentElements.sort(sortByMobileOrder);
}
export function getLayoutContentElements(element: LayoutElement, toStore: LayoutElement[]): void {
  if (element.type === LayoutElementType.Row || element.type === LayoutElementType.Column) {
    const container = element as LayoutElementRow | LayoutElementColumn;
    if (container.elements?.length > 0) {
      container.elements.forEach((elem: LayoutElement) => {
        getLayoutContentElements(elem, toStore);
      });
    }
  } else {
    if ((element as LayoutElementContent).mobileOrder) {
      toStore.push(element as LayoutElementContent);
    }
  }
}

export function sortByMobileOrder(elementA: LayoutElementContent, elementB: LayoutElementContent): 0 | 1 | -1 {
  if (!elementA.mobileOrder) {
    return 1;
  }
  if (!elementB.mobileOrder) {
    return -1;
  }
  if (elementA.mobileOrder < elementB.mobileOrder) {
    return -1;
  }
  if (elementA.mobileOrder > elementB.mobileOrder) {
    return 1;
  }
  return 0;
}
