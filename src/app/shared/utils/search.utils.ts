import { FilterValues, ISearchParams } from '../definitions';
import { Ordering } from '@trendency/kesma-ui';

export function generateSearchParams(filter: FilterValues, limit: { page?: number; perPage?: number }, sort: Ordering): ISearchParams {
  let params = {} as Mutable<ISearchParams>;
  params.global_filter = filter.keyword;
  params.from_date = filter.fromDate?.toDateString();
  params.to_date = filter.toDate?.toDateString();
  params.page_limit = limit.page?.toString();
  params.rowCount_limit = limit.perPage?.toString();
  params['columnSlugs[]'] = filter.columns;
  params['content_types[]'] = filter.contentTypes;

  const sortParams: {
    [Key in Ordering]: SortExpression[];
  } = {
    latest: [{ publishDate: 'desc' }],
    'most-popular': ['likeCount', 'createdAt'],
    oldest: ['publishDate'],
  };
  params = { ...params, ...generateSortingQuery(sortParams[sort]) };
  return params;
}

export type Mutable<Type> = {
  -readonly [Key in keyof Type]: Type[Key];
};

export type ResultType = Partial<{
  [Key in Sortable as `${Key}_ordering`]: 'asc' | 'desc';
}>;

export type Sortable = 'createdAt' | 'likeCount' | 'publishDate';
export type SortExpression = { [Key in Sortable]?: 'asc' | 'desc' } | Sortable;

export function generateSortingQuery(sorts: SortExpression[]): ResultType {
  const result = {} as ResultType;
  for (let index = 0; index < sorts.length; index++) {
    const sort = sorts[index];
    if (typeof sort === 'string') {
      result[`${sort}_ordering`] = 'asc';
    }
    const key = Object.keys(sort)[0] as Sortable;
    result[`${key}_ordering`] = Object.values(sort)[0] as 'asc' | 'desc';
  }
  return result;
}
