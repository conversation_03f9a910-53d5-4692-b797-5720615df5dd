import {
  Article,
  ArticleBody,
  ArticleBodyType,
  ArticleCard,
  ArticleSocial,
  BackendArticle,
  backendDateToDate,
  BackendDossierArticle,
  BackendRecommendedArticle,
  backendVotingDataToVotingData,
  BasicDossier,
  DossierArticle,
  ExternalRecommendation,
  getPrimaryColumnColorComboByColumnTitleColor,
  PreviewBackendArticle,
  SubsequentDossier,
  backendDateToUTC,
  BrandingBoxArticle,
  PersonalizedRecommendationArticle,
} from '@trendency/kesma-ui';
import { BackendArticleSocial } from '../definitions/social.definitions';

export const backendArticlesToArticles = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let year: number = 0;
  let month: number = 0;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    year = publishDate?.getUTCFullYear() ?? 0;
    month = publishDate?.getUTCMonth() ?? 0;
  }

  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const body: ArticleBody[] = article?.body.map((element: ArticleBody) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });
  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'Mandíner',
    dossier: dossier?.[0],
    lastUpdated: last,
    rawLastUpdated: backendDateToUTC(article.lastUpdated as string) as string,
    publishDate,
    rawPublishDate: backendDateToUTC(article.publishDate as string) as string,
    year,
    month,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
    readingTime: (article as any)?.readingLength,
  };
};

export const backendDossierArticleToDossierArticle = (dossier: BackendDossierArticle): DossierArticle =>
  dossier && {
    ...dossier,
    publishDate: backendDateToDate(dossier.publishDate) as Date,
    length: dossier.length ? parseInt(dossier.length as string, 10) : undefined,
    thumbnailCreatedAt: dossier.thumbnailCreatedAt ? (backendDateToDate(dossier.thumbnailCreatedAt) ?? undefined) : undefined,
    regions: [],
    tags: [],
  };

export const externalRecommendationToArticleCard = (externalRecommendation: ExternalRecommendation): ArticleCard => ({
  id: externalRecommendation.spr,
  title: externalRecommendation.title,
  columnTitle: externalRecommendation.siteName,
  category: {
    name: externalRecommendation.siteName,
    slug: undefined,
  },
  thumbnail: externalRecommendation.imagePath
    ? {
        url: externalRecommendation.imagePath,
      }
    : undefined,
  slug: undefined,
  publishDate: undefined,
  label: {
    text: externalRecommendation.siteName,
    url: externalRecommendation.siteName ?? '',
  },
  publishYear: undefined,
  publishMonth: undefined,
  url: externalRecommendation.url,
});

export const backendRecommendedArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  thumbnailUrl,
  thumbnail,
  excerpt,
  readingLength,
  columnTitle,
  columnTitleColor,
  titleColor,
  columnSlug,
  preTitle,
  preTitleColor,
}: BackendRecommendedArticle): ArticleCard => {
  const [publishYear, publishMonth] = publishDate.split('-');
  return {
    id,
    title,
    slug,
    publishDate: backendDateToDate(publishDate) as Date,
    publishMonth,
    publishYear,
    thumbnail:
      thumbnailUrl || thumbnail
        ? {
            url: thumbnailUrl || thumbnail || '',
          }
        : undefined,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    readingTime: readingLength?.toString(),
    columnTitle: columnTitle,
    preTitle: preTitle,
    primaryColumnColorCombo:
      columnTitleColor || columnTitleColor ? getPrimaryColumnColorComboByColumnTitleColor(columnTitleColor ? columnTitleColor : (titleColor ?? '')) : undefined,
    columnSlug,
    preTitleColor,
    lead: excerpt,
    label: {
      text: preTitle ?? '',
    },
  };
};

export const previewBackendArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  primaryColumn,
  column,
  preTitle,
  excerpt,
  thumbnailUrl,
}: PreviewBackendArticle): ArticleCard => {
  if (!id) {
    id = '';
  }
  let publishDateFromBackend: Date | undefined;
  let publishYear = 0;
  let publishMonth = 0;
  if (publishDate) {
    publishDateFromBackend = backendDateToDate(typeof publishDate === 'string' ? publishDate : publishDate.date) ?? undefined;
    publishYear = publishDateFromBackend?.getUTCFullYear() ?? 0;
    publishMonth = (publishDateFromBackend?.getUTCMonth() ?? 0) + 1;
  }

  return {
    id,
    slug,
    title,
    preTitle,
    excerpt,
    publishDate: publishDateFromBackend,
    columnTitle: column?.title,
    columnSlug: column?.slug,
    thumbnail: thumbnailUrl
      ? {
          url: thumbnailUrl,
        }
      : undefined,
    primaryColumnColorCombo: primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(primaryColumn?.titleColor) : undefined,
    publishYear,
    publishMonth,
    category: { ...primaryColumn, name: primaryColumn?.title },
    label: {
      text: 'Ezt ne hagyja ki!',
    },
  };
};

export const subsequentDossierToBasicDossier = ({
  slug,
  title,
  coverImage: thumbnailUrl,
  relatedArticles: articles,
}: SubsequentDossier): BasicDossier<Date> | undefined =>
  slug
    ? {
        slug,
        title,
        thumbnailUrl,
        articles: articles?.map((article) => ({
          ...article,
          publishDate: backendDateToDate(article.publishDate) as Date,
        })),
      }
    : undefined;

export const backendArticleSocialToSocial = (backendArticleSocial: BackendArticleSocial): ArticleSocial => ({
  likeCount: backendArticleSocial.likeCount,
  dislikeCount: backendArticleSocial.dislikeCount,
  commentCount: backendArticleSocial.commentCount,
  isCommentsDisabled: backendArticleSocial.disableComments,
  isLikesAndDislikesDisabled: backendArticleSocial.disableLikesAndDislikes,
});

export const mapTrafficDeflectorArticlesToBrandingBoxArticle = (personalizedRecommendationArticle: PersonalizedRecommendationArticle): BrandingBoxArticle => ({
  title: personalizedRecommendationArticle?.title,
  lead: personalizedRecommendationArticle?.head,
  thumbnail: personalizedRecommendationArticle?.image,
  url: personalizedRecommendationArticle?.url,
});
