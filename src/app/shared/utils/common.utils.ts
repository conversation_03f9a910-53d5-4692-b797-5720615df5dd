export function* makeIterator(count: number): Generator<number, void> {
  for (let i = 0; i < count; i++) {
    yield i;
  }
}

export const setArticleSourceHost = (source: string | undefined): string => {
  const whiteList = ['http://', 'https://', 'www.'];
  const regex = new RegExp(whiteList.join('|'), 'gi');
  if (!source || !regex.test(source)) {
    return '';
  }

  // remove http://, https:// or www.
  const first = source.replace(regex, '');
  const domain = first.split('/');
  return domain[0];
};
