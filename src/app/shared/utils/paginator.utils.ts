import { BehaviorSubject, finalize, first, Observable } from 'rxjs';
import { ApiResponseMetaList, ApiR<PERSON>ult } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';

type ResultType<T> = ApiResult<T[], ApiResponseMetaList>;
type LoadFunction<T> = (page: number) => Observable<ResultType<T>>;

/**
 * Helper class for handling pagination of data. Handles loading, appending and page numbers.
 * @example
 * // create paginator. The data type will be ArticleCard[], the meta type is ApiResponseMetaList
 * readonly paginator = new Paginator((page) => this.apiService.getArticles(page, MAX_RESULTS_PER_PAGE));
 * // If `getArticles()` returns `ApiResult<ArticleCard[], ApiResponseMetaList>`, `ArticleCard` will be inferred as the data type
 */
export class Paginator<T> {
  private readonly _data$ = new BehaviorSubject<ResultType<T> | undefined>(undefined);
  private readonly _isLoading$ = new BehaviorSubject<boolean>(false);
  private _loadFunction: LoadFunction<T>;

  constructor(loadFunction: LoadFunction<T>, startValue?: ResultType<T> | Observable<ResultType<T>>) {
    this.setLoadFunction(loadFunction, !!startValue);
    if (startValue) {
      if (startValue instanceof Observable) {
        startValue.pipe(first()).subscribe((result) => this._data$.next(result));
        return;
      }
      this._data$.next(startValue);
    }
  }

  /**
   * The data of the current page
   * @note Holds `undefined` until the first `load()` or `reset()` call
   * @example
   * <div *ngFor="let article of (paginator.data$ | async)">
   *   <man-article-card [data]="article"></man-article-card>
   * </div>
   */
  readonly data$: Observable<T[] | undefined> = this._data$.pipe(map((result) => result?.data));

  /**
   * The data of the current page. Please use `data$` whenever possible, to avoid change detection issues.
   * @note Holds `undefined` until the first `load()` or `reset()` call
   * @example
   * if(paginator.data) {
   *  // do something with paginator.data
   *  // paginator.data is of type T[]
   * }
   */
  get data(): T[] | undefined {
    return this._data$.value?.data;
  }

  /**
   * The metadata of the current page
   * @note Holds `undefined` until the first `load()` or `reset()` call
   * @example
   * <span>{{ (paginator.meta$ | async)?.limitable?.rowAllCount }}db cikk</span>
   */
  readonly meta$: Observable<ApiResponseMetaList | undefined> = this._data$.pipe(map((result) => result?.meta));

  /**
   * Whether there are more pages to load
   * @note Holds `false` until the first `load()` call
   * @example
   * <man-simple-button *ngIf="paginator.hasMore$ | async" (click)="loadMore()">Továbbiak betöltése</man-simple-button>
   */
  readonly hasMore$: Observable<boolean> = this.meta$.pipe(map((meta) => (meta?.limitable?.pageCurrent ?? 0) < (meta?.limitable?.pageMax ?? 0)));

  /**
   * Whether the paginator is currently loading data
   * @notes initially `false`
   * @example
   * <man-spinner *ngIf="paginator.isLoading$ | async"></man-spinner>
   */
  readonly isLoading$: Observable<boolean> = this._isLoading$.asObservable();

  /**
   * Whether the paginator is currently loading data. Please use `isLoading$` whenever possible, to avoid change detection issues.
   * @notes initially `false`
   * @example
   * if(paginator.isLoading) {
   *   // Do something
   * }
   */
  get isLoading(): boolean {
    return this._isLoading$.value;
  }

  /**
   * Sets the load function. Load function will be called whenever data needs to be fetched. (e.g. `load()`, `reset()`, `next()`)
   *
   * By default this function will call `reset()` after setting the new load function.
   *
   * **IMPORTANT**: Use an arrow function to avoid `this` binding issues.
   * @param value The load function
   * @param skipReset If true, the paginator will NOT be reset after setting the load function. Defaults to false
   * @note After called, the first page will be loaded
   * @example
   * ngOnInit(): void {
   *   this.paginator.setLoadFunction((page) => this.apiService.getArticles(page, MAX_RESULTS_PER_PAGE));
   *   // You can also use class members, they will be bound correctly:
   *   this.paginator.setLoadFunction((page) => this.apiService.getArticles(page, MAX_RESULTS_PER_PAGE, this.filterParams));
   *   // Or put logic in the function:
   *   this.paginator.setLoadFunction((page) => {
   *     if (this.authService.isLoggedIn()) {
   *       return this.apiService.getSecureArticles(page, MAX_RESULTS_PER_PAGE, this.filterParams);
   *     }
   *     return this.apiService.getPublicArticles(page, MAX_RESULTS_PER_PAGE, this.filterParams);
   * }
   */
  setLoadFunction(value: LoadFunction<T>, skipReset = false): void {
    this._loadFunction = value;
    if (!skipReset) {
      this.reset();
    }
  }

  /**
   * Loads the given page. If the page is 0, the data will be replaced, otherwise it will be appended.
   * @param page The page to load (zero based). Defaults to 0
   * @throws Error if loadFunction was not set
   * @example
   * this.paginator.load(1);
   * <man-simple-button (click)="paginator.load(1)">Második oldalra</man-simple-button>
   */
  load(page: number): void {
    this._isLoading$.next(true);
    this._loadFunction(page)
      .pipe(
        first(),
        finalize(() => this._isLoading$.next(false))
      )
      .subscribe((result) => {
        if (!this._data$.value || page === 0) {
          this._data$.next(result);
          return;
        }
        this._data$.next({
          data: [...this._data$.value.data, ...result.data],
          meta: result.meta,
        });
      });
  }

  /**
   * Resets the paginator. The data will be replaced with the first page
   * @note This is equivalent to calling `load(0)`
   */
  reset(): void {
    this.load(0);
  }

  /**
   * Loads the next page
   * @throws Error if loadFunction was not set
   * @example
   * <man-simple-button (click)="paginator.next()">Továbbiak betöltése</man-simple-button>
   */
  next(): void {
    this.load((this._data$.value?.meta?.limitable?.pageCurrent ?? -1) + 1);
  }
}
