import { Ordering } from '@trendency/kesma-ui';

export const getSortingQuery = (
  sort: Ordering
): {
  'createdAt_order[1]'?: 'desc';
  'likeCount_order[0]'?: 'desc';
  'createdAt_order[0]'?: 'asc' | 'desc';
} =>
  (
    ({
      'most-popular': {
        'likeCount_order[0]': 'desc',
        'createdAt_order[1]': 'desc',
      },
      latest: { 'createdAt_order[0]': 'desc' },
      oldest: { 'createdAt_order[0]': 'asc' },
    }) as const
  )[sort];
