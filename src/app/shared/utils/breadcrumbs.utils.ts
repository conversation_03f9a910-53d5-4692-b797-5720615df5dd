import { BreadcrumbItem } from '@trendency/kesma-ui';
import { BreadcrumbList, BreadcrumbListElement } from '@trendency/kesma-core';
import { environment } from 'src/environments/environment';

export const makeBreadcrumbSchema = (items: BreadcrumbItem[]): BreadcrumbList => {
  return {
    '@type': 'BreadcrumbList',
    itemListElement: <BreadcrumbListElement[]>items?.map((item, index) => {
      const url = item.url ? environment.siteUrl + (item.url as []).join('/').substring(1) : '';
      return {
        '@type': 'ListItem',
        position: index,
        item: {
          '@id': url,
          name: item.label,
        },
      };
    }),
  };
};
