import { MandinerEnvironment } from './environment.definitions';

// <PERSON><PERSON>
export const environment: MandinerEnvironment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://mandiner.hu/publicapi/hu',
    serverApiUrl: 'http://mandinerfeapi.app.content.private/publicapi/hu',
  },
  secureApiUrl: {
    clientApiUrl: 'https://mandiner.hu/secureapi/hu',
    serverApiUrl: 'http://mandinerfeapi.app.content.private/secureapi/hu',
  },
  financialApiUrl: {
    clientApiUrl: 'https://fd.mediaworks.hu',
    serverApiUrl: 'http://findata-restapi.app.content.private',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '1618891101939213',
  googleClientId: '708972576598-bd1qq5tklrrmvibl303b4f0q0ud1vmu2.apps.googleusercontent.com',
  appleClientId: 'service.hu.mandiner.sso',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'https://mandiner.hu', // TODO: revert this to mandiner.hu before release !!!
  googleSiteKey: '6LcvzSolAAAAADF8wjiCpQBI4rp3nZeUvkWIPH-1',
  googleTagManager: 'GTM-TC4WGP7',
  gemiusId: '..iQcfhzv7BVlRP0QFMNQJPZfStuU68PkYZybtIshY7.f7',
  httpReqTimeout: 30, // second
  shopUrls: {
    manageCards: 'https://shop.mandiner.hu/account',
    cancelSubscription: 'https://shop.mandiner.hu/account',
    subscriptions: 'https://www.mandiner-elofizetes.com/elofizetes-start',
  },
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
