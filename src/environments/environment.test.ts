import { MandinerEnvironment } from './environment.definitions';

// UAT teszt környezet
export const environment: MandinerEnvironment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://mandinerfe.apptest.content.private/publicapi/hu',
  secureApiUrl: 'http://mandinerfe.apptest.content.private/secureapi/hu',
  financialApiUrl: 'http://findata.apptest.content.private/restapi',
  personalizedRecommendationApiUrl: 'https://terelo.dev.trendency.hu/api',
  facebookAppId: '1618891101939213',
  googleClientId: '708972576598-bd1qq5tklrrmvibl303b4f0q0ud1vmu2.apps.googleusercontent.com',
  appleClientId: 'service.hu.mandiner.sso',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'http://mandinerfe.apptest.content.private',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-TC4WGP7',
  gemiusId: '..iQcfhzv7BVlRP0QFMNQJPZfStuU68PkYZybtIshY7.f7',
  httpReqTimeout: 30, // second
  shopUrls: {
    manageCards: 'https://shop.mandiner.hu/account',
    cancelSubscription: 'https://shop.mandiner.hu/account',
    subscriptions: 'https://www.mandiner-elofizetes.com/elofizetes-start',
  },
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
